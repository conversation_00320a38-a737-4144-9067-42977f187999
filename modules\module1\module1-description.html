<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Module 1: Fundamentals of Medical Instrumentation</title>
    <link rel="stylesheet" href="../../css/style.css">
    <link rel="stylesheet" href="module1-styles.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/3.9.1/chart.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/gsap.min.js"></script>
</head>
<body>
    <header>
        <div class="container">
            <h1>Module 1: Fundamentals of Medical Instrumentation</h1>
            <p class="subtitle">From Cellular Origins to Signal Generation</p>
            <div class="course-info">
                <p class="author">Course Author: <strong>Dr. <PERSON></strong></p>
                <p class="institution">SUST - BME 2025</p>
            </div>
        </div>
    </header>

    <nav>
        <div class="container">
            <ul>
                <li><a href="../../index.html">Home</a></li>
                <li><a href="#overview">Overview</a></li>
                <li><a href="#lectures">Lectures</a></li>
                <li><a href="#diagrams">Diagrams</a></li>
                <li><a href="#interactive">Interactive</a></li>
                <li><a href="#assessment">Assessment</a></li>
            </ul>
        </div>
    </nav>

    <main>
        <section id="overview" class="section">
            <div class="container">
                <h2>🧬 Module Overview: Signal Origins & Fundamentals</h2>
                <div class="module-intro">
                    <p>This foundational module explores the fascinating journey from <strong>cellular activities to measurable bioelectric signals</strong>. Students will understand how ion channels, action potentials, and cellular mechanisms generate the signals that form the basis of all medical instrumentation.</p>
                    
                    <div class="learning-pathway-module1">
                        <h3>📈 Learning Progression</h3>
                        <div class="pathway-steps">
                            <div class="step-item" data-step="1">
                                <div class="step-icon">🧬</div>
                                <h4>Cellular Foundations</h4>
                                <p>Ion channels & membrane potentials</p>
                            </div>
                            <div class="step-connector">→</div>
                            <div class="step-item" data-step="2">
                                <div class="step-icon">⚡</div>
                                <h4>Action Potentials</h4>
                                <p>Signal generation mechanisms</p>
                            </div>
                            <div class="step-connector">→</div>
                            <div class="step-item" data-step="3">
                                <div class="step-icon">🔬</div>
                                <h4>Organ Systems</h4>
                                <p>From cells to measurable signals</p>
                            </div>
                            <div class="step-connector">→</div>
                            <div class="step-item" data-step="4">
                                <div class="step-icon">🛡️</div>
                                <h4>Safety & Standards</h4>
                                <p>Medical device regulations</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="module-objectives">
                    <h3>🎯 Learning Objectives</h3>
                    <div class="objectives-grid">
                        <div class="objective-card">
                            <div class="objective-icon">🧬</div>
                            <h4>Understand Cellular Origins</h4>
                            <p>Trace how cellular activities generate bioelectric signals from ion movement to action potentials</p>
                        </div>
                        <div class="objective-card">
                            <div class="objective-icon">⚡</div>
                            <h4>Master Signal Generation</h4>
                            <p>Comprehend the mechanisms of action potential propagation and signal transmission</p>
                        </div>
                        <div class="objective-card">
                            <div class="objective-icon">🔬</div>
                            <h4>Scale to Organ Systems</h4>
                            <p>Connect single-cell signals to organ-level measurements in medical instrumentation</p>
                        </div>
                        <div class="objective-card">
                            <div class="objective-icon">🛡️</div>
                            <h4>Apply Safety Standards</h4>
                            <p>Understand medical device classification, regulations, and patient safety requirements</p>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <section id="lectures" class="section">
            <div class="container">
                <h2>📚 Interactive Lecture Slides</h2>
                <div class="lectures-grid">
                    
                    <!-- Lecture 1: Cellular Foundations -->
                    <div class="lecture-card" data-lecture="1">
                        <div class="lecture-header">
                            <span class="lecture-number">Lecture 1</span>
                            <h3>Cellular Foundations of Bioelectric Signals</h3>
                            <div class="lecture-duration">Duration: 90 minutes</div>
                        </div>
                        <div class="lecture-content">
                            <div class="lecture-preview">
                                <div class="animated-cell" id="cell-animation-1">
                                    <div class="cell-membrane"></div>
                                    <div class="ion-channel"></div>
                                    <div class="ions"></div>
                                </div>
                            </div>
                            <div class="lecture-topics">
                                <h4>Topics Covered:</h4>
                                <ul>
                                    <li>🧬 Cell membrane structure and properties</li>
                                    <li>⚡ Ion channels: voltage-gated, ligand-gated</li>
                                    <li>🔋 Resting membrane potential (-70mV)</li>
                                    <li>📊 Nernst equation and equilibrium potentials</li>
                                    <li>🌊 Ion gradients and pumps</li>
                                </ul>
                            </div>
                            <div class="lecture-actions">
                                <button class="btn-primary" onclick="openLectureSlides(1)">📖 View Slides</button>
                                <button class="btn-secondary" onclick="startInteractiveDemo(1)">🔬 Interactive Demo</button>
                            </div>
                        </div>
                    </div>

                    <!-- Lecture 2: Action Potentials -->
                    <div class="lecture-card" data-lecture="2">
                        <div class="lecture-header">
                            <span class="lecture-number">Lecture 2</span>
                            <h3>Action Potential Generation & Propagation</h3>
                            <div class="lecture-duration">Duration: 90 minutes</div>
                        </div>
                        <div class="lecture-content">
                            <div class="lecture-preview">
                                <div class="action-potential-graph" id="ap-graph">
                                    <canvas id="apCanvas" width="300" height="150"></canvas>
                                </div>
                            </div>
                            <div class="lecture-topics">
                                <h4>Topics Covered:</h4>
                                <ul>
                                    <li>⚡ Threshold potential and excitability</li>
                                    <li>📈 Action potential phases (depolarization, repolarization)</li>
                                    <li>🚀 Propagation mechanisms and conduction velocity</li>
                                    <li>🔄 Refractory periods and signal integrity</li>
                                    <li>🧠 Myelination and saltatory conduction</li>
                                </ul>
                            </div>
                            <div class="lecture-actions">
                                <button class="btn-primary" onclick="openLectureSlides(2)">📖 View Slides</button>
                                <button class="btn-secondary" onclick="startInteractiveDemo(2)">⚡ AP Simulator</button>
                            </div>
                        </div>
                    </div>

                    <!-- Lecture 3: Organ System Signals -->
                    <div class="lecture-card" data-lecture="3">
                        <div class="lecture-header">
                            <span class="lecture-number">Lecture 3</span>
                            <h3>From Cells to Organ System Signals</h3>
                            <div class="lecture-duration">Duration: 90 minutes</div>
                        </div>
                        <div class="lecture-content">
                            <div class="lecture-preview">
                                <div class="organ-system-diagram" id="organ-diagram">
                                    <div class="heart-icon">💓</div>
                                    <div class="brain-icon">🧠</div>
                                    <div class="muscle-icon">💪</div>
                                </div>
                            </div>
                            <div class="lecture-topics">
                                <h4>Topics Covered:</h4>
                                <ul>
                                    <li>💓 Cardiac electrophysiology and ECG origins</li>
                                    <li>🧠 Neural networks and EEG generation</li>
                                    <li>💪 Muscle activation and EMG signals</li>
                                    <li>📊 Signal summation and interference</li>
                                    <li>🔬 Measurement challenges and solutions</li>
                                </ul>
                            </div>
                            <div class="lecture-actions">
                                <button class="btn-primary" onclick="openLectureSlides(3)">📖 View Slides</button>
                                <button class="btn-secondary" onclick="startInteractiveDemo(3)">🔬 Signal Explorer</button>
                            </div>
                        </div>
                    </div>

                    <!-- Lecture 4: Safety & Standards -->
                    <div class="lecture-card" data-lecture="4">
                        <div class="lecture-header">
                            <span class="lecture-number">Lecture 4</span>
                            <h3>Medical Device Safety & Standards</h3>
                            <div class="lecture-duration">Duration: 90 minutes</div>
                        </div>
                        <div class="lecture-content">
                            <div class="lecture-preview">
                                <div class="safety-icons">
                                    <div class="safety-icon">🛡️</div>
                                    <div class="regulation-icon">📋</div>
                                    <div class="standard-icon">✅</div>
                                </div>
                            </div>
                            <div class="lecture-topics">
                                <h4>Topics Covered:</h4>
                                <ul>
                                    <li>🛡️ Patient safety and electrical isolation</li>
                                    <li>📋 FDA device classification (Class I, II, III)</li>
                                    <li>🌍 International standards (ISO, IEC)</li>
                                    <li>⚡ Electrical safety and leakage currents</li>
                                    <li>✅ Quality management and risk assessment</li>
                                </ul>
                            </div>
                            <div class="lecture-actions">
                                <button class="btn-primary" onclick="openLectureSlides(4)">📖 View Slides</button>
                                <button class="btn-secondary" onclick="startInteractiveDemo(4)">🛡️ Safety Simulator</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <section id="diagrams" class="section">
            <div class="container">
                <h2>📊 Interactive Block & Schematic Diagrams</h2>
                
                <!-- Block Diagram Section -->
                <div class="diagram-section">
                    <h3>🔲 Block Diagram: Signal Generation Pathway</h3>
                    <div class="block-diagram-container">
                        <div class="interactive-block-diagram" id="signalPathwayDiagram">
                            <div class="block-item" data-block="cell">
                                <div class="block-icon">🧬</div>
                                <h4>Cell Membrane</h4>
                                <p>Ion channels & gradients</p>
                            </div>
                            <div class="block-arrow">→</div>
                            <div class="block-item" data-block="potential">
                                <div class="block-icon">⚡</div>
                                <h4>Action Potential</h4>
                                <p>Electrical signal generation</p>
                            </div>
                            <div class="block-arrow">→</div>
                            <div class="block-item" data-block="propagation">
                                <div class="block-icon">🌊</div>
                                <h4>Signal Propagation</h4>
                                <p>Conduction & transmission</p>
                            </div>
                            <div class="block-arrow">→</div>
                            <div class="block-item" data-block="measurement">
                                <div class="block-icon">📊</div>
                                <h4>Measurement</h4>
                                <p>External detection</p>
                            </div>
                        </div>
                        <div class="diagram-controls">
                            <button class="btn-control" onclick="animateBlockDiagram()">▶️ Animate Flow</button>
                            <button class="btn-control" onclick="resetBlockDiagram()">🔄 Reset</button>
                        </div>
                    </div>
                </div>

                <!-- Schematic Diagram Section -->
                <div class="diagram-section">
                    <h3>⚡ Schematic Diagram: Cell Membrane Equivalent Circuit</h3>
                    <div class="schematic-diagram-container">
                        <div class="interactive-schematic" id="membraneCircuit">
                            <svg width="600" height="400" viewBox="0 0 600 400">
                                <!-- Membrane Capacitor -->
                                <g class="component" data-component="capacitor">
                                    <rect x="250" y="150" width="20" height="60" fill="#3498db" stroke="#2980b9" stroke-width="2"/>
                                    <rect x="280" y="150" width="20" height="60" fill="#3498db" stroke="#2980b9" stroke-width="2"/>
                                    <text x="255" y="240" text-anchor="middle" class="component-label">Cm</text>
                                    <text x="255" y="255" text-anchor="middle" class="component-value">1 µF/cm²</text>
                                </g>
                                
                                <!-- Sodium Channel -->
                                <g class="component" data-component="sodium">
                                    <circle cx="150" cy="120" r="15" fill="#e74c3c" stroke="#c0392b" stroke-width="2"/>
                                    <rect x="135" y="140" width="30" height="20" fill="#e74c3c" stroke="#c0392b" stroke-width="2"/>
                                    <text x="150" y="185" text-anchor="middle" class="component-label">Na+ Channel</text>
                                    <text x="150" y="200" text-anchor="middle" class="component-value">ENa = +60mV</text>
                                </g>
                                
                                <!-- Potassium Channel -->
                                <g class="component" data-component="potassium">
                                    <circle cx="450" cy="120" r="15" fill="#9b59b6" stroke="#8e44ad" stroke-width="2"/>
                                    <rect x="435" y="140" width="30" height="20" fill="#9b59b6" stroke="#8e44ad" stroke-width="2"/>
                                    <text x="450" y="185" text-anchor="middle" class="component-label">K+ Channel</text>
                                    <text x="450" y="200" text-anchor="middle" class="component-value">EK = -90mV</text>
                                </g>
                                
                                <!-- Connections -->
                                <line x1="165" y1="150" x2="250" y2="150" stroke="#34495e" stroke-width="2"/>
                                <line x1="300" y1="150" x2="435" y2="150" stroke="#34495e" stroke-width="2"/>
                                <line x1="150" y1="160" x2="150" y2="300" stroke="#34495e" stroke-width="2"/>
                                <line x1="450" y1="160" x2="450" y2="300" stroke="#34495e" stroke-width="2"/>
                                <line x1="150" y1="300" x2="450" y2="300" stroke="#34495e" stroke-width="2"/>
                                
                                <!-- Voltage indicators -->
                                <text x="300" y="30" text-anchor="middle" class="voltage-label">Vm = -70mV (resting)</text>
                                <text x="300" y="50" text-anchor="middle" class="voltage-label">Vm = +30mV (depolarized)</text>
                            </svg>
                        </div>
                        <div class="diagram-controls">
                            <button class="btn-control" onclick="simulateDepolarization()">⚡ Simulate Depolarization</button>
                            <button class="btn-control" onclick="showCurrentFlow()">🌊 Show Current Flow</button>
                            <button class="btn-control" onclick="resetSchematic()">🔄 Reset</button>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <section id="interactive" class="section">
            <div class="container">
                <h2>🎮 Interactive Learning Tools</h2>
                
                <div class="interactive-tools-grid">
                    <!-- Ion Channel Simulator -->
                    <div class="tool-card">
                        <h3>🧬 Ion Channel Simulator</h3>
                        <div class="tool-preview">
                            <div class="channel-simulator" id="channelSim">
                                <div class="membrane-representation">
                                    <div class="ion na-ion">Na+</div>
                                    <div class="ion k-ion">K+</div>
                                    <div class="ion cl-ion">Cl-</div>
                                    <div class="channel-gate" id="naGate">Na+ Gate</div>
                                    <div class="channel-gate" id="kGate">K+ Gate</div>
                                </div>
                            </div>
                        </div>
                        <div class="tool-controls">
                            <label>Membrane Voltage: <span id="voltageDisplay">-70mV</span></label>
                            <input type="range" id="voltageSlider" min="-100" max="50" value="-70">
                            <button onclick="startChannelSimulation()">▶️ Start Simulation</button>
                        </div>
                    </div>

                    <!-- Action Potential Generator -->
                    <div class="tool-card">
                        <h3>⚡ Action Potential Generator</h3>
                        <div class="tool-preview">
                            <canvas id="apGenerator" width="400" height="200"></canvas>
                        </div>
                        <div class="tool-controls">
                            <label>Stimulus Strength: <span id="stimulusDisplay">0mA</span></label>
                            <input type="range" id="stimulusSlider" min="0" max="10" value="0" step="0.1">
                            <button onclick="generateActionPotential()">⚡ Generate AP</button>
                            <button onclick="clearAPTrace()">🗑️ Clear</button>
                        </div>
                    </div>

                    <!-- Signal Pathway Explorer -->
                    <div class="tool-card">
                        <h3>🔬 Signal Pathway Explorer</h3>
                        <div class="tool-preview">
                            <div class="pathway-explorer" id="pathwayExplorer">
                                <div class="organ-selector">
                                    <button class="organ-btn" data-organ="heart">💓 Heart</button>
                                    <button class="organ-btn" data-organ="brain">🧠 Brain</button>
                                    <button class="organ-btn" data-organ="muscle">💪 Muscle</button>
                                </div>
                                <div class="signal-display" id="signalDisplay">
                                    <canvas id="signalCanvas" width="400" height="150"></canvas>
                                </div>
                            </div>
                        </div>
                        <div class="tool-controls">
                            <button onclick="exploreSignalPath('heart')">💓 Explore Heart Signals</button>
                            <button onclick="exploreSignalPath('brain')">🧠 Explore Brain Signals</button>
                            <button onclick="exploreSignalPath('muscle')">💪 Explore Muscle Signals</button>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <section id="assessment" class="section">
            <div class="container">
                <h2>📝 Assessment & Evaluation</h2>
                
                <div class="assessment-overview">
                    <div class="assessment-breakdown">
                        <h3>Assessment Components</h3>
                        <div class="assessment-chart">
                            <canvas id="assessmentChart" width="300" height="300"></canvas>
                        </div>
                    </div>
                    
                    <div class="assessment-details">
                        <div class="assessment-item">
                            <h4>🧪 Virtual Lab Exercises (40%)</h4>
                            <ul>
                                <li>Ion Channel Simulation Lab</li>
                                <li>Action Potential Analysis</li>
                                <li>Signal Pathway Exploration</li>
                                <li>Safety Standards Assessment</li>
                            </ul>
                        </div>
                        
                        <div class="assessment-item">
                            <h4>📝 Quizzes & Tests (35%)</h4>
                            <ul>
                                <li>Weekly concept quizzes</li>
                                <li>Midterm examination</li>
                                <li>Interactive diagram tests</li>
                                <li>Problem-solving exercises</li>
                            </ul>
                        </div>
                        
                        <div class="assessment-item">
                            <h4>🎯 Project Work (25%)</h4>
                            <ul>
                                <li>Signal origin research project</li>
                                <li>Device classification analysis</li>
                                <li>Safety standard presentation</li>
                                <li>Peer review activities</li>
                            </ul>
                        </div>
                    </div>
                </div>
                
                <div class="quick-assessment">
                    <h3>🚀 Quick Knowledge Check</h3>
                    <div class="quiz-container" id="quickQuiz">
                        <div class="question" data-question="1">
                            <h4>What is the typical resting membrane potential of a neuron?</h4>
                            <div class="options">
                                <button class="option" data-answer="wrong">-90mV</button>
                                <button class="option" data-answer="correct">-70mV</button>
                                <button class="option" data-answer="wrong">-50mV</button>
                                <button class="option" data-answer="wrong">0mV</button>
                            </div>
                        </div>
                    </div>
                    <div class="quiz-feedback" id="quizFeedback"></div>
                </div>
            </div>
        </section>
    </main>

    <footer>
        <div class="container">
            <div class="footer-content">
                <div class="author-info">
                    <h3>Course Author</h3>
                    <p><strong>Dr. Mohammed Yagoub Esmail</strong></p>
                    <p>Sudan University of Science and Technology (SUST)</p>
                    <p>Biomedical Engineering Department</p>
                </div>
                
                <div class="contact-info">
                    <h3>Contact Information</h3>
                    <p>📧 Email: <a href="mailto:<EMAIL>"><EMAIL></a></p>
                    <p>📱 Phone: +249912867327, +966538076790</p>
                </div>
            </div>
            
            <div class="copyright">
                <p>&copy; 2025 Dr. Mohammed Yagoub Esmail - SUST BME</p>
                <p>All rights reserved. Email: <EMAIL></p>
            </div>
        </div>
    </footer>

    <script src="module1-animations.js"></script>
    <script src="module1-interactive.js"></script>
</body>
</html>
