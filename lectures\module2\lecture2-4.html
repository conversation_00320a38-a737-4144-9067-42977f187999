<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Lecture 2.4: Analog-to-Digital Conversion in Biomedical Systems</title>
    <link rel="stylesheet" href="../../css/style.css">
    <link rel="stylesheet" href="../module1/lecture-slides.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/gsap.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/3.9.1/chart.min.js"></script>
</head>
<body class="lecture-mode">
    <header class="lecture-header">
        <div class="container">
            <div class="lecture-navigation">
                <a href="../../index.html" class="nav-btn">🏠 Home</a>
                <a href="../../modules/module2/module2-description.html" class="nav-btn">📚 Module 2</a>
                <div class="lecture-info">
                    <span class="lecture-title">Lecture 2.4: ADC Systems</span>
                    <span class="slide-counter">Slide <span id="currentSlide">1</span> of <span id="totalSlides">10</span></span>
                </div>
                <div class="lecture-controls">
                    <button type="button" id="prevSlide" class="control-btn">⬅️ Previous</button>
                    <button type="button" id="nextSlide" class="control-btn">➡️ Next</button>
                    <button type="button" id="fullscreenBtn" class="control-btn">🔍 Fullscreen</button>
                </div>
            </div>
        </div>
    </header>

    <main class="lecture-content">
        <div class="slides-container" id="slidesContainer">
            
            <!-- Slide 1: Title Slide -->
            <div class="slide active" data-slide="1">
                <div class="slide-content title-slide">
                    <h1>💻 Analog-to-Digital Conversion in Biomedical Systems</h1>
                    <h2>Module 2 - Lecture 2.4</h2>
                    <div class="title-info">
                        <p><strong>Sampling Theory, Quantization, and Digital Signal Processing</strong></p>
                        <p>Converting continuous bioelectric signals to digital domain for processing and analysis</p>
                    </div>
                    <div class="author-info">
                        <p><strong>Dr. Mohammed Yagoub Esmail</strong></p>
                        <p>Sudan University of Science and Technology (SUST)</p>
                        <p>Biomedical Engineering Department - 2025</p>
                    </div>
                    <div class="adc-preview" id="adcPreview">
                        <div class="conversion-flow">
                            <div class="signal-type">📈 Analog</div>
                            <div class="arrow">→</div>
                            <div class="process-step">⏱️ Sample</div>
                            <div class="arrow">→</div>
                            <div class="process-step">🔢 Quantize</div>
                            <div class="arrow">→</div>
                            <div class="signal-type">💻 Digital</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Slide 2: Learning Objectives -->
            <div class="slide" data-slide="2">
                <div class="slide-content">
                    <h2>🎯 Learning Objectives</h2>
                    <div class="objectives-list">
                        <div class="objective-item" data-animate="1">
                            <div class="objective-icon">⏱️</div>
                            <div class="objective-text">
                                <h3>Master Sampling Theory</h3>
                                <p>Understand Nyquist theorem, aliasing effects, and optimal sampling rates for biomedical signals</p>
                            </div>
                        </div>
                        <div class="objective-item" data-animate="2">
                            <div class="objective-icon">🔢</div>
                            <div class="objective-text">
                                <h3>Analyze Quantization Effects</h3>
                                <p>Study resolution requirements, quantization noise, and dynamic range considerations</p>
                            </div>
                        </div>
                        <div class="objective-item" data-animate="3">
                            <div class="objective-icon">🔧</div>
                            <div class="objective-text">
                                <h3>Compare ADC Architectures</h3>
                                <p>Examine successive approximation, delta-sigma, and pipeline ADC topologies</p>
                            </div>
                        </div>
                        <div class="objective-item" data-animate="4">
                            <div class="objective-icon">⚙️</div>
                            <div class="objective-text">
                                <h3>Optimize System Performance</h3>
                                <p>Learn to select appropriate ADC specifications for specific biomedical applications</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Slide 3: Sampling Theory -->
            <div class="slide" data-slide="3">
                <div class="slide-content">
                    <h2>⏱️ Sampling Theory and Nyquist Criterion</h2>
                    <div class="content-grid">
                        <div class="content-left">
                            <div class="sampling-fundamentals">
                                <h3>📊 Nyquist-Shannon Sampling Theorem</h3>
                                <div class="theorem-statement">
                                    <div class="theorem-box">
                                        <h4>🎯 Theorem Statement</h4>
                                        <p>A continuous signal can be perfectly reconstructed from its samples if the sampling frequency is at least twice the highest frequency component in the signal.</p>
                                        <div class="formula">
                                            <strong>fs ≥ 2 × fmax</strong>
                                        </div>
                                        <p>Where fs = sampling frequency, fmax = maximum signal frequency</p>
                                    </div>
                                </div>
                                
                                <div class="biomedical-frequencies">
                                    <h4>🏥 Biomedical Signal Frequencies</h4>
                                    <div class="frequency-table">
                                        <table class="signal-freq-table">
                                            <thead>
                                                <tr>
                                                    <th>Signal Type</th>
                                                    <th>Frequency Range</th>
                                                    <th>Typical fs</th>
                                                    <th>Nyquist fs</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <tr>
                                                    <td>ECG</td>
                                                    <td>0.05-100 Hz</td>
                                                    <td>500 Hz</td>
                                                    <td>200 Hz</td>
                                                </tr>
                                                <tr>
                                                    <td>EEG</td>
                                                    <td>0.5-70 Hz</td>
                                                    <td>256 Hz</td>
                                                    <td>140 Hz</td>
                                                </tr>
                                                <tr>
                                                    <td>EMG</td>
                                                    <td>10-500 Hz</td>
                                                    <td>2 kHz</td>
                                                    <td>1 kHz</td>
                                                </tr>
                                                <tr>
                                                    <td>Blood Pressure</td>
                                                    <td>0-50 Hz</td>
                                                    <td>200 Hz</td>
                                                    <td>100 Hz</td>
                                                </tr>
                                                <tr>
                                                    <td>Ultrasound</td>
                                                    <td>1-15 MHz</td>
                                                    <td>50 MHz</td>
                                                    <td>30 MHz</td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                                
                                <div class="aliasing-effects">
                                    <h4>⚠️ Aliasing and Anti-Aliasing</h4>
                                    <div class="aliasing-explanation">
                                        <p><strong>Aliasing:</strong> High-frequency components appear as lower frequencies when fs < 2fmax</p>
                                        <p><strong>Prevention:</strong> Anti-aliasing filters before ADC</p>
                                        <div class="filter-specs">
                                            <span class="spec-tag">Cutoff: fs/2</span>
                                            <span class="spec-tag">Roll-off: >40 dB/decade</span>
                                            <span class="spec-tag">Type: Low-pass</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="content-right">
                            <div class="sampling-visualization">
                                <h3>📈 Interactive Sampling Demo</h3>
                                <div class="sampling-plot">
                                    <canvas id="samplingCanvas" width="320" height="200"></canvas>
                                </div>
                                
                                <div class="sampling-controls">
                                    <div class="control-group">
                                        <label for="signalFreq">Signal Frequency (Hz):</label>
                                        <input type="range" id="signalFreq" min="1" max="50" value="10">
                                        <span id="signalFreqValue">10</span>
                                    </div>
                                    <div class="control-group">
                                        <label for="samplingFreq">Sampling Frequency (Hz):</label>
                                        <input type="range" id="samplingFreq" min="5" max="200" value="50">
                                        <span id="samplingFreqValue">50</span>
                                    </div>
                                    <div class="nyquist-indicator" id="nyquistIndicator">
                                        <span class="indicator-label">Nyquist Criterion:</span>
                                        <span class="indicator-status">✅ Satisfied</span>
                                    </div>
                                    <button type="button" class="demo-btn" onclick="toggleSamplingDemo()">Start/Stop Demo</button>
                                </div>
                                
                                <div class="reconstruction-demo">
                                    <h4>🔄 Signal Reconstruction</h4>
                                    <canvas id="reconstructionCanvas" width="320" height="150"></canvas>
                                    <div class="reconstruction-controls">
                                        <button type="button" class="demo-btn" onclick="showOriginalSignal()">Original</button>
                                        <button type="button" class="demo-btn" onclick="showSampledSignal()">Sampled</button>
                                        <button type="button" class="demo-btn" onclick="showReconstructed()">Reconstructed</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Slide 4: Quantization -->
            <div class="slide" data-slide="4">
                <div class="slide-content">
                    <h2>🔢 Quantization and Resolution</h2>
                    <div class="content-grid">
                        <div class="content-left">
                            <div class="quantization-theory">
                                <h3>📐 Quantization Fundamentals</h3>
                                <div class="quantization-process">
                                    <h4>🎯 Quantization Process</h4>
                                    <p>Converting continuous amplitude values to discrete digital levels</p>
                                    
                                    <div class="resolution-calculation">
                                        <h5>📊 Resolution Calculation</h5>
                                        <div class="formula-box">
                                            <div class="formula-main">LSB = Vref / 2^n</div>
                                            <div class="formula-variables">
                                                <p>LSB = Least Significant Bit</p>
                                                <p>Vref = Reference voltage</p>
                                                <p>n = Number of bits</p>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="dynamic-range">
                                        <h5>📈 Dynamic Range</h5>
                                        <div class="formula-box">
                                            <div class="formula-main">DR = 6.02n + 1.76 dB</div>
                                            <p>For n-bit ADC</p>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="bit-resolution-table">
                                    <h4>🔢 Resolution vs. Bits</h4>
                                    <table class="resolution-table">
                                        <thead>
                                            <tr>
                                                <th>Bits</th>
                                                <th>Levels</th>
                                                <th>Resolution (mV)*</th>
                                                <th>Dynamic Range (dB)</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td>8</td>
                                                <td>256</td>
                                                <td>19.5</td>
                                                <td>49.9</td>
                                            </tr>
                                            <tr>
                                                <td>12</td>
                                                <td>4,096</td>
                                                <td>1.22</td>
                                                <td>74.0</td>
                                            </tr>
                                            <tr>
                                                <td>16</td>
                                                <td>65,536</td>
                                                <td>0.076</td>
                                                <td>98.1</td>
                                            </tr>
                                            <tr>
                                                <td>24</td>
                                                <td>16,777,216</td>
                                                <td>0.0003</td>
                                                <td>146.2</td>
                                            </tr>
                                        </tbody>
                                    </table>
                                    <p class="table-note">*For 5V reference voltage</p>
                                </div>
                            </div>
                        </div>
                        <div class="content-right">
                            <div class="quantization-demo">
                                <h3>🎛️ Interactive Quantization Demo</h3>
                                <div class="quantization-plot">
                                    <canvas id="quantizationCanvas" width="320" height="200"></canvas>
                                </div>
                                
                                <div class="quantization-controls">
                                    <div class="control-group">
                                        <label for="adcBits">ADC Resolution (bits):</label>
                                        <select id="adcBits">
                                            <option value="4">4 bits</option>
                                            <option value="8" selected>8 bits</option>
                                            <option value="12">12 bits</option>
                                            <option value="16">16 bits</option>
                                        </select>
                                    </div>
                                    <div class="control-group">
                                        <label for="inputRange">Input Range (V):</label>
                                        <select id="inputRange">
                                            <option value="1">±1V</option>
                                            <option value="2.5">±2.5V</option>
                                            <option value="5" selected>±5V</option>
                                            <option value="10">±10V</option>
                                        </select>
                                    </div>
                                    <button type="button" class="demo-btn" onclick="updateQuantizationDemo()">Update Demo</button>
                                </div>
                                
                                <div class="quantization-metrics">
                                    <h4>📊 Quantization Metrics</h4>
                                    <div class="metrics-display">
                                        <div class="metric-item">
                                            <span class="metric-label">LSB:</span>
                                            <span class="metric-value" id="lsbValue">39.1 mV</span>
                                        </div>
                                        <div class="metric-item">
                                            <span class="metric-label">SNR:</span>
                                            <span class="metric-value" id="snrValue">49.9 dB</span>
                                        </div>
                                        <div class="metric-item">
                                            <span class="metric-label">ENOB:</span>
                                            <span class="metric-value" id="enobValue">8.0 bits</span>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="biomedical-requirements">
                                    <h4>🏥 Biomedical Requirements</h4>
                                    <div class="requirement-list">
                                        <div class="req-item">
                                            <h5>ECG Systems</h5>
                                            <p>12-16 bits, 1-5 mV range</p>
                                        </div>
                                        <div class="req-item">
                                            <h5>EEG Systems</h5>
                                            <p>16-24 bits, 100 μV range</p>
                                        </div>
                                        <div class="req-item">
                                            <h5>EMG Systems</h5>
                                            <p>12-16 bits, 10 mV range</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Slide 5: ADC Architectures -->
            <div class="slide" data-slide="5">
                <div class="slide-content">
                    <h2>🔧 ADC Architectures for Biomedical Applications</h2>
                    <div class="content-grid">
                        <div class="content-left">
                            <div class="adc-types">
                                <h3>🏗️ ADC Architecture Comparison</h3>
                                <div class="architecture-item">
                                    <h4>🔄 Successive Approximation (SAR)</h4>
                                    <div class="arch-details">
                                        <div class="principle">
                                            <h5>Operating Principle</h5>
                                            <p>Binary search algorithm to find digital equivalent</p>
                                        </div>
                                        <div class="characteristics">
                                            <h5>Characteristics</h5>
                                            <ul>
                                                <li>Resolution: 8-18 bits</li>
                                                <li>Speed: 100 kSPS - 5 MSPS</li>
                                                <li>Power: Low-medium</li>
                                                <li>Cost: Low-medium</li>
                                            </ul>
                                        </div>
                                        <div class="applications">
                                            <h5>Biomedical Applications</h5>
                                            <p>ECG, EMG, general purpose data acquisition</p>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="architecture-item">
                                    <h4>🌊 Delta-Sigma (ΔΣ)</h4>
                                    <div class="arch-details">
                                        <div class="principle">
                                            <h5>Operating Principle</h5>
                                            <p>Oversampling with noise shaping and digital filtering</p>
                                        </div>
                                        <div class="characteristics">
                                            <h5>Characteristics</h5>
                                            <ul>
                                                <li>Resolution: 16-32 bits</li>
                                                <li>Speed: 1 SPS - 1 MSPS</li>
                                                <li>Power: Very low</li>
                                                <li>Cost: Low</li>
                                            </ul>
                                        </div>
                                        <div class="applications">
                                            <h5>Biomedical Applications</h5>
                                            <p>EEG, high-precision measurements, portable devices</p>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="architecture-item">
                                    <h4>🚀 Pipeline</h4>
                                    <div class="arch-details">
                                        <div class="principle">
                                            <h5>Operating Principle</h5>
                                            <p>Multi-stage conversion with sample-and-hold</p>
                                        </div>
                                        <div class="characteristics">
                                            <h5>Characteristics</h5>
                                            <ul>
                                                <li>Resolution: 8-16 bits</li>
                                                <li>Speed: 1-100 MSPS</li>
                                                <li>Power: High</li>
                                                <li>Cost: High</li>
                                            </ul>
                                        </div>
                                        <div class="applications">
                                            <h5>Biomedical Applications</h5>
                                            <p>Ultrasound imaging, high-speed data acquisition</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="content-right">
                            <div class="adc-selection">
                                <h3>🎯 ADC Selection Guide</h3>
                                <div class="selection-matrix">
                                    <canvas id="selectionCanvas" width="320" height="250"></canvas>
                                </div>
                                
                                <div class="selection-controls">
                                    <div class="control-group">
                                        <label for="applicationSelect">Application:</label>
                                        <select id="applicationSelect">
                                            <option value="ecg">ECG Monitoring</option>
                                            <option value="eeg">EEG Recording</option>
                                            <option value="emg">EMG Analysis</option>
                                            <option value="ultrasound">Ultrasound Imaging</option>
                                        </select>
                                    </div>
                                    <button type="button" class="demo-btn" onclick="updateSelectionGuide()">Show Recommendations</button>
                                </div>
                                
                                <div class="recommendation-display" id="recommendationDisplay">
                                    <h4>💡 Recommended ADC</h4>
                                    <div class="recommendation-content">
                                        <div class="rec-type">Delta-Sigma ADC</div>
                                        <div class="rec-specs">
                                            <span class="spec-item">Resolution: 24 bits</span>
                                            <span class="spec-item">Sample Rate: 1 kSPS</span>
                                            <span class="spec-item">Power: <1 mW</span>
                                        </div>
                                        <div class="rec-reason">
                                            <strong>Reason:</strong> High resolution needed for small EEG signals (μV range)
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="performance-tradeoffs">
                                    <h4>⚖️ Performance Trade-offs</h4>
                                    <div class="tradeoff-chart">
                                        <div class="tradeoff-axis">
                                            <span class="axis-label">Speed</span>
                                            <div class="axis-line">
                                                <div class="adc-point sar" title="SAR ADC">SAR</div>
                                                <div class="adc-point pipeline" title="Pipeline ADC">Pipeline</div>
                                                <div class="adc-point delta-sigma" title="Delta-Sigma ADC">ΔΣ</div>
                                            </div>
                                        </div>
                                        <div class="tradeoff-axis">
                                            <span class="axis-label">Resolution</span>
                                            <div class="axis-line">
                                                <div class="adc-point pipeline" title="Pipeline ADC">Pipeline</div>
                                                <div class="adc-point sar" title="SAR ADC">SAR</div>
                                                <div class="adc-point delta-sigma" title="Delta-Sigma ADC">ΔΣ</div>
                                            </div>
                                        </div>
                                        <div class="tradeoff-axis">
                                            <span class="axis-label">Power</span>
                                            <div class="axis-line">
                                                <div class="adc-point delta-sigma" title="Delta-Sigma ADC">ΔΣ</div>
                                                <div class="adc-point sar" title="SAR ADC">SAR</div>
                                                <div class="adc-point pipeline" title="Pipeline ADC">Pipeline</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Slide 6: Module 2 Complete Summary -->
            <div class="slide" data-slide="6">
                <div class="slide-content">
                    <h2>📋 Module 2 Complete Summary</h2>
                    <div class="module-summary">
                        <div class="summary-overview">
                            <h3>🎓 Signal Acquisition Journey Complete</h3>
                            <p>We've mastered the complete signal acquisition chain from transduction principles to digital conversion, providing the foundation for advanced biomedical instrumentation design.</p>
                        </div>
                        
                        <div class="summary-grid">
                            <div class="summary-section">
                                <h3>🔄 Lecture 2.1: Transduction Principles</h3>
                                <ul>
                                    <li>Energy conversion fundamentals</li>
                                    <li>Transducer characteristics and selection</li>
                                    <li>Resistive, capacitive, and piezoelectric mechanisms</li>
                                    <li>Performance optimization strategies</li>
                                </ul>
                            </div>
                            <div class="summary-section">
                                <h3>🔘 Lecture 2.2: Electrode Systems</h3>
                                <ul>
                                    <li>Electrode-electrolyte interface physics</li>
                                    <li>Surface and invasive electrode technologies</li>
                                    <li>Impedance characteristics and modeling</li>
                                    <li>Signal quality optimization techniques</li>
                                </ul>
                            </div>
                            <div class="summary-section">
                                <h3>📈 Lecture 2.3: Signal Conditioning</h3>
                                <ul>
                                    <li>Instrumentation amplifier design</li>
                                    <li>Active filtering systems</li>
                                    <li>Electrical isolation for patient safety</li>
                                    <li>Noise reduction and artifact rejection</li>
                                </ul>
                            </div>
                            <div class="summary-section">
                                <h3>💻 Lecture 2.4: ADC Systems</h3>
                                <ul>
                                    <li>Sampling theory and Nyquist criterion</li>
                                    <li>Quantization effects and resolution</li>
                                    <li>ADC architecture comparison</li>
                                    <li>Application-specific selection criteria</li>
                                </ul>
                            </div>
                        </div>
                        
                        <div class="next-module">
                            <h3>🔜 Next: Module 3 - Biomedical Signal Processing</h3>
                            <p>Now that we can acquire and digitize bioelectric signals, we'll explore advanced digital signal processing techniques for analysis, feature extraction, and clinical interpretation.</p>
                            <div class="module-preview">
                                <div class="preview-topics">
                                    <span class="topic-tag">🔍 Digital Filtering</span>
                                    <span class="topic-tag">📊 Spectral Analysis</span>
                                    <span class="topic-tag">🎯 Feature Extraction</span>
                                    <span class="topic-tag">🤖 Pattern Recognition</span>
                                </div>
                            </div>
                            <a href="../../modules/module3/module3-description.html" class="next-module-btn">➡️ Continue to Module 3</a>
                        </div>
                    </div>
                </div>
            </div>

        </div>
    </main>

    <footer class="lecture-footer">
        <div class="container">
            <div class="footer-content">
                <div class="author-info">
                    <p><strong>Dr. Mohammed Yagoub Esmail</strong> | SUST - BME 2025</p>
                    <p>📧 <EMAIL> | 📱 +249912867327, +966538076790</p>
                </div>
                <div class="copyright">
                    <p>&copy; 2025 Dr. Mohammed Yagoub Esmail - All rights reserved</p>
                </div>
            </div>
        </div>
    </footer>

    <script src="../module1/lecture-slides.js"></script>
    <script src="lecture2-4-interactive.js"></script>
</body>
</html>
