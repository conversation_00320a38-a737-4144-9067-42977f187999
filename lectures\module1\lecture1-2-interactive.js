// Lecture 1.2 Interactive Elements - Action Potential Generation & Propagation

// Action Potential Animation and Visualization
class ActionPotentialVisualizer {
    constructor() {
        this.isAnimating = false;
        this.currentPhase = 0;
        this.timeStep = 0;
        this.initializeCanvases();
    }
    
    initializeCanvases() {
        // Initialize preview canvas
        this.initPreviewCanvas();
        
        // Initialize main AP graph
        this.initMainAPGraph();
        
        // Initialize current diagram
        this.initCurrentDiagram();
        
        // Initialize refractory canvas
        this.initRefractoryCanvas();
        
        // Initialize propagation canvas
        this.initPropagationCanvas();
    }
    
    initPreviewCanvas() {
        const canvas = document.getElementById('apPreviewCanvas');
        if (!canvas) return;
        
        const ctx = canvas.getContext('2d');
        this.drawActionPotentialPreview(ctx, canvas.width, canvas.height);
        
        // Animate the preview
        this.animatePreview(ctx, canvas.width, canvas.height);
    }
    
    drawActionPotentialPreview(ctx, width, height) {
        ctx.clearRect(0, 0, width, height);
        
        // Draw axes
        ctx.strokeStyle = '#34495e';
        ctx.lineWidth = 2;
        ctx.beginPath();
        ctx.moveTo(30, height - 30);
        ctx.lineTo(width - 20, height - 30);
        ctx.moveTo(30, 20);
        ctx.lineTo(30, height - 30);
        ctx.stroke();
        
        // Draw AP waveform
        ctx.strokeStyle = '#e74c3c';
        ctx.lineWidth = 3;
        ctx.beginPath();
        
        const points = this.getAPPoints(width, height);
        ctx.moveTo(points[0].x, points[0].y);
        for (let i = 1; i < points.length; i++) {
            ctx.lineTo(points[i].x, points[i].y);
        }
        ctx.stroke();
        
        // Add labels
        ctx.fillStyle = '#2c3e50';
        ctx.font = '12px Arial';
        ctx.fillText('Time', width - 40, height - 10);
        ctx.save();
        ctx.translate(10, height / 2);
        ctx.rotate(-Math.PI / 2);
        ctx.fillText('mV', 0, 0);
        ctx.restore();
    }
    
    getAPPoints(width, height) {
        const baseY = height - 40;
        const restingY = baseY - 30; // -70mV
        const peakY = 30; // +30mV
        
        return [
            { x: 30, y: restingY },      // Resting
            { x: 60, y: restingY },      // Stimulus
            { x: 90, y: peakY },         // Depolarization
            { x: 120, y: peakY - 5 },    // Peak
            { x: 150, y: restingY + 5 }, // Repolarization
            { x: 180, y: restingY - 10 }, // Hyperpolarization
            { x: 210, y: restingY },     // Return to rest
            { x: width - 20, y: restingY }
        ];
    }
    
    animatePreview(ctx, width, height) {
        let animationStep = 0;
        const animate = () => {
            if (animationStep > 100) {
                animationStep = 0;
            }
            
            this.drawActionPotentialPreview(ctx, width, height);
            
            // Add moving indicator
            const indicatorX = 30 + (animationStep / 100) * (width - 50);
            ctx.fillStyle = '#3498db';
            ctx.beginPath();
            ctx.arc(indicatorX, height - 40, 5, 0, 2 * Math.PI);
            ctx.fill();
            
            animationStep += 0.5;
            requestAnimationFrame(animate);
        };
        
        animate();
    }
    
    initMainAPGraph() {
        const canvas = document.getElementById('apMainGraph');
        if (!canvas) return;
        
        const ctx = canvas.getContext('2d');
        this.drawDetailedAPGraph(ctx, canvas.width, canvas.height);
    }
    
    drawDetailedAPGraph(ctx, width, height) {
        ctx.clearRect(0, 0, width, height);
        
        // Draw grid
        this.drawGrid(ctx, width, height);
        
        // Draw axes
        ctx.strokeStyle = '#34495e';
        ctx.lineWidth = 2;
        ctx.beginPath();
        ctx.moveTo(50, height - 50);
        ctx.lineTo(width - 30, height - 50);
        ctx.moveTo(50, 30);
        ctx.lineTo(50, height - 50);
        ctx.stroke();
        
        // Draw voltage markers
        ctx.fillStyle = '#2c3e50';
        ctx.font = '12px Arial';
        const voltageMarkers = [
            { v: 40, y: 50 },
            { v: 0, y: height/2 },
            { v: -70, y: height - 80 }
        ];
        
        voltageMarkers.forEach(marker => {
            ctx.fillText(`${marker.v}mV`, 10, marker.y);
            ctx.strokeStyle = '#ecf0f1';
            ctx.lineWidth = 1;
            ctx.beginPath();
            ctx.moveTo(45, marker.y);
            ctx.lineTo(width - 30, marker.y);
            ctx.stroke();
        });
        
        // Draw time markers
        for (let i = 0; i <= 5; i++) {
            const x = 50 + (i * (width - 80) / 5);
            ctx.fillText(`${i}ms`, x - 10, height - 30);
            ctx.strokeStyle = '#ecf0f1';
            ctx.lineWidth = 1;
            ctx.beginPath();
            ctx.moveTo(x, 30);
            ctx.lineTo(x, height - 55);
            ctx.stroke();
        }
        
        // Draw action potential
        this.drawAPWaveform(ctx, width, height);
        
        // Draw phase labels
        this.drawPhaseLabels(ctx, width, height);
    }
    
    drawGrid(ctx, width, height) {
        ctx.strokeStyle = '#f8f9fa';
        ctx.lineWidth = 0.5;
        
        // Vertical grid lines
        for (let x = 50; x < width - 30; x += 20) {
            ctx.beginPath();
            ctx.moveTo(x, 30);
            ctx.lineTo(x, height - 50);
            ctx.stroke();
        }
        
        // Horizontal grid lines
        for (let y = 30; y < height - 50; y += 20) {
            ctx.beginPath();
            ctx.moveTo(50, y);
            ctx.lineTo(width - 30, y);
            ctx.stroke();
        }
    }
    
    drawAPWaveform(ctx, width, height) {
        ctx.strokeStyle = '#e74c3c';
        ctx.lineWidth = 4;
        ctx.beginPath();
        
        const points = [
            { x: 50, y: height - 80 },    // Resting -70mV
            { x: 100, y: height - 80 },   // Stimulus
            { x: 130, y: height - 80 },   // Threshold
            { x: 160, y: 50 },            // Peak +30mV
            { x: 200, y: height/2 },      // Repolarization 0mV
            { x: 240, y: height - 70 },   // Repolarization -70mV
            { x: 280, y: height - 90 },   // Hyperpolarization -80mV
            { x: 320, y: height - 80 },   // Return to rest
            { x: width - 30, y: height - 80 }
        ];
        
        ctx.moveTo(points[0].x, points[0].y);
        for (let i = 1; i < points.length; i++) {
            if (i === 3) {
                // Sharp rise for depolarization
                ctx.lineTo(points[i].x, points[i].y);
            } else {
                ctx.lineTo(points[i].x, points[i].y);
            }
        }
        ctx.stroke();
        
        // Mark threshold
        ctx.strokeStyle = '#f39c12';
        ctx.lineWidth = 2;
        ctx.setLineDash([5, 5]);
        ctx.beginPath();
        ctx.moveTo(50, height - 65); // -55mV threshold
        ctx.lineTo(width - 30, height - 65);
        ctx.stroke();
        ctx.setLineDash([]);
        
        ctx.fillStyle = '#f39c12';
        ctx.font = '12px Arial';
        ctx.fillText('Threshold (-55mV)', width - 150, height - 70);
    }
    
    drawPhaseLabels(ctx, width, height) {
        ctx.fillStyle = '#3498db';
        ctx.font = 'bold 12px Arial';
        
        const labels = [
            { text: '1. Resting', x: 75, y: height - 100 },
            { text: '2. Depolarization', x: 145, y: 40 },
            { text: '3. Repolarization', x: 220, y: height/2 - 10 },
            { text: '4. Hyperpolarization', x: 280, y: height - 50 }
        ];
        
        labels.forEach(label => {
            ctx.fillText(label.text, label.x, label.y);
        });
    }
    
    initCurrentDiagram() {
        const canvas = document.getElementById('currentCanvas');
        if (!canvas) return;
        
        const ctx = canvas.getContext('2d');
        this.drawCurrentDiagram(ctx, canvas.width, canvas.height);
    }
    
    drawCurrentDiagram(ctx, width, height) {
        ctx.clearRect(0, 0, width, height);
        
        // Draw axes
        ctx.strokeStyle = '#34495e';
        ctx.lineWidth = 2;
        ctx.beginPath();
        ctx.moveTo(50, height - 30);
        ctx.lineTo(width - 30, height - 30);
        ctx.moveTo(50, 20);
        ctx.lineTo(50, height - 30);
        ctx.stroke();
        
        // Draw zero line
        ctx.strokeStyle = '#95a5a6';
        ctx.lineWidth = 1;
        ctx.setLineDash([3, 3]);
        ctx.beginPath();
        ctx.moveTo(50, height/2);
        ctx.lineTo(width - 30, height/2);
        ctx.stroke();
        ctx.setLineDash([]);
        
        // Labels
        ctx.fillStyle = '#2c3e50';
        ctx.font = '12px Arial';
        ctx.fillText('Time (ms)', width - 60, height - 10);
        ctx.save();
        ctx.translate(15, height/2);
        ctx.rotate(-Math.PI / 2);
        ctx.fillText('Current', 0, 0);
        ctx.restore();
        
        ctx.fillText('Inward', 10, 30);
        ctx.fillText('Outward', 10, height - 40);
    }
    
    initRefractoryCanvas() {
        const canvas = document.getElementById('refractoryCanvas');
        if (!canvas) return;
        
        const ctx = canvas.getContext('2d');
        this.drawRefractoryDiagram(ctx, canvas.width, canvas.height);
    }
    
    drawRefractoryDiagram(ctx, width, height) {
        ctx.clearRect(0, 0, width, height);
        
        // Draw AP waveform
        ctx.strokeStyle = '#e74c3c';
        ctx.lineWidth = 3;
        ctx.beginPath();
        
        const apPoints = [
            { x: 50, y: height - 50 },
            { x: 100, y: height - 50 },
            { x: 130, y: 50 },
            { x: 170, y: height - 40 },
            { x: 200, y: height - 60 },
            { x: 230, y: height - 50 }
        ];
        
        ctx.moveTo(apPoints[0].x, apPoints[0].y);
        for (let i = 1; i < apPoints.length; i++) {
            ctx.lineTo(apPoints[i].x, apPoints[i].y);
        }
        ctx.stroke();
        
        // Draw refractory periods
        // Absolute refractory period
        ctx.fillStyle = 'rgba(231, 76, 60, 0.3)';
        ctx.fillRect(100, 20, 70, height - 70);
        
        // Relative refractory period
        ctx.fillStyle = 'rgba(241, 196, 15, 0.3)';
        ctx.fillRect(170, 20, 80, height - 70);
        
        // Labels
        ctx.fillStyle = '#2c3e50';
        ctx.font = 'bold 12px Arial';
        ctx.fillText('Absolute', 110, 35);
        ctx.fillText('Refractory', 110, 50);
        ctx.fillText('Relative', 180, 35);
        ctx.fillText('Refractory', 180, 50);
        
        // Time scale
        ctx.font = '10px Arial';
        ctx.fillText('0', 50, height - 20);
        ctx.fillText('2ms', 170, height - 20);
        ctx.fillText('5ms', 250, height - 20);
    }
    
    initPropagationCanvas() {
        const canvas = document.getElementById('propagationCanvas');
        if (!canvas) return;
        
        const ctx = canvas.getContext('2d');
        this.drawPropagationDiagram(ctx, canvas.width, canvas.height);
    }
    
    drawPropagationDiagram(ctx, width, height) {
        ctx.clearRect(0, 0, width, height);
        
        // Draw axon
        const axonY = height / 2;
        ctx.strokeStyle = '#34495e';
        ctx.lineWidth = 8;
        ctx.beginPath();
        ctx.moveTo(50, axonY);
        ctx.lineTo(width - 50, axonY);
        ctx.stroke();
        
        // Draw membrane
        ctx.strokeStyle = '#3498db';
        ctx.lineWidth = 2;
        ctx.beginPath();
        ctx.moveTo(50, axonY - 20);
        ctx.lineTo(width - 50, axonY - 20);
        ctx.moveTo(50, axonY + 20);
        ctx.lineTo(width - 50, axonY + 20);
        ctx.stroke();
        
        // Draw action potential wave
        this.drawPropagatingWave(ctx, width, height, 0);
        
        // Labels
        ctx.fillStyle = '#2c3e50';
        ctx.font = '12px Arial';
        ctx.fillText('Axon', 20, axonY + 5);
        ctx.fillText('Direction of propagation →', width - 200, 30);
    }
    
    drawPropagatingWave(ctx, width, height, offset) {
        const waveX = 100 + offset;
        const axonY = height / 2;
        
        // Draw depolarized region
        ctx.fillStyle = 'rgba(231, 76, 60, 0.6)';
        ctx.fillRect(waveX - 20, axonY - 25, 40, 50);
        
        // Draw local currents
        ctx.strokeStyle = '#f39c12';
        ctx.lineWidth = 2;
        
        // Current arrows
        for (let i = -1; i <= 1; i++) {
            const x = waveX + i * 30;
            if (x > 50 && x < width - 50) {
                this.drawCurrentArrow(ctx, x, axonY - 30, x, axonY - 10);
                this.drawCurrentArrow(ctx, x, axonY + 10, x, axonY + 30);
            }
        }
    }
    
    drawCurrentArrow(ctx, x1, y1, x2, y2) {
        ctx.beginPath();
        ctx.moveTo(x1, y1);
        ctx.lineTo(x2, y2);
        ctx.stroke();
        
        // Arrowhead
        const angle = Math.atan2(y2 - y1, x2 - x1);
        const headLength = 8;
        ctx.beginPath();
        ctx.moveTo(x2, y2);
        ctx.lineTo(x2 - headLength * Math.cos(angle - Math.PI/6), y2 - headLength * Math.sin(angle - Math.PI/6));
        ctx.moveTo(x2, y2);
        ctx.lineTo(x2 - headLength * Math.cos(angle + Math.PI/6), y2 - headLength * Math.sin(angle + Math.PI/6));
        ctx.stroke();
    }
}

// Interactive Functions

function animateActionPotential() {
    const canvas = document.getElementById('apMainGraph');
    if (!canvas) return;
    
    const ctx = canvas.getContext('2d');
    const visualizer = new ActionPotentialVisualizer();
    
    let step = 0;
    const animate = () => {
        if (step > 100) return;
        
        visualizer.drawDetailedAPGraph(ctx, canvas.width, canvas.height);
        
        // Add progress indicator
        const progressX = 50 + (step / 100) * (canvas.width - 80);
        ctx.strokeStyle = '#3498db';
        ctx.lineWidth = 3;
        ctx.beginPath();
        ctx.moveTo(progressX, 20);
        ctx.lineTo(progressX, canvas.height - 40);
        ctx.stroke();
        
        step += 2;
        setTimeout(() => requestAnimationFrame(animate), 100);
    };
    
    animate();
}

function showPhaseDetails() {
    const phases = [
        { name: 'Resting State', duration: '∞', description: 'Stable at -70mV' },
        { name: 'Depolarization', duration: '0.5ms', description: 'Na⁺ influx' },
        { name: 'Repolarization', duration: '2ms', description: 'K⁺ efflux' },
        { name: 'Hyperpolarization', duration: '2ms', description: 'K⁺ overshoot' }
    ];
    
    let currentPhase = 0;
    const showPhase = () => {
        if (currentPhase >= phases.length) return;
        
        const phase = phases[currentPhase];
        alert(`Phase ${currentPhase + 1}: ${phase.name}\nDuration: ${phase.duration}\nMechanism: ${phase.description}`);
        
        currentPhase++;
        setTimeout(showPhase, 1000);
    };
    
    showPhase();
}

function showSodiumCurrent() {
    const canvas = document.getElementById('currentCanvas');
    if (!canvas) return;
    
    const ctx = canvas.getContext('2d');
    const visualizer = new ActionPotentialVisualizer();
    visualizer.drawCurrentDiagram(ctx, canvas.width, canvas.height);
    
    // Draw sodium current
    ctx.strokeStyle = '#e74c3c';
    ctx.lineWidth = 3;
    ctx.beginPath();
    
    const points = [
        { x: 50, y: canvas.height/2 },
        { x: 100, y: canvas.height/2 },
        { x: 130, y: 40 },  // Inward current
        { x: 160, y: canvas.height/2 },
        { x: canvas.width - 30, y: canvas.height/2 }
    ];
    
    ctx.moveTo(points[0].x, points[0].y);
    for (let i = 1; i < points.length; i++) {
        ctx.lineTo(points[i].x, points[i].y);
    }
    ctx.stroke();
    
    ctx.fillStyle = '#e74c3c';
    ctx.font = 'bold 14px Arial';
    ctx.fillText('Na⁺ Current', canvas.width - 100, 60);
}

function showPotassiumCurrent() {
    const canvas = document.getElementById('currentCanvas');
    if (!canvas) return;
    
    const ctx = canvas.getContext('2d');
    const visualizer = new ActionPotentialVisualizer();
    visualizer.drawCurrentDiagram(ctx, canvas.width, canvas.height);
    
    // Draw potassium current
    ctx.strokeStyle = '#9b59b6';
    ctx.lineWidth = 3;
    ctx.beginPath();
    
    const points = [
        { x: 50, y: canvas.height/2 },
        { x: 130, y: canvas.height/2 },
        { x: 160, y: canvas.height - 60 },  // Outward current
        { x: 220, y: canvas.height - 70 },
        { x: 280, y: canvas.height/2 },
        { x: canvas.width - 30, y: canvas.height/2 }
    ];
    
    ctx.moveTo(points[0].x, points[0].y);
    for (let i = 1; i < points.length; i++) {
        ctx.lineTo(points[i].x, points[i].y);
    }
    ctx.stroke();
    
    ctx.fillStyle = '#9b59b6';
    ctx.font = 'bold 14px Arial';
    ctx.fillText('K⁺ Current', canvas.width - 100, canvas.height - 40);
}

function showBothCurrents() {
    showSodiumCurrent();
    setTimeout(showPotassiumCurrent, 500);
}

function showRefractoryPeriods() {
    const canvas = document.getElementById('refractoryCanvas');
    if (!canvas) return;
    
    const ctx = canvas.getContext('2d');
    const visualizer = new ActionPotentialVisualizer();
    visualizer.drawRefractoryDiagram(ctx, canvas.width, canvas.height);
    
    // Animate highlighting
    let phase = 0;
    const highlight = () => {
        if (phase === 0) {
            // Highlight absolute refractory
            ctx.strokeStyle = '#e74c3c';
            ctx.lineWidth = 3;
            ctx.strokeRect(100, 20, 70, canvas.height - 70);
        } else if (phase === 1) {
            // Highlight relative refractory
            ctx.strokeStyle = '#f1c40f';
            ctx.lineWidth = 3;
            ctx.strokeRect(170, 20, 80, canvas.height - 70);
        }
        
        phase++;
        if (phase < 2) {
            setTimeout(highlight, 1000);
        }
    };
    
    highlight();
}

function testStimulation() {
    alert('Testing stimulation during refractory periods:\n\n' +
          '1. During absolute refractory: No response\n' +
          '2. During relative refractory: Requires stronger stimulus\n' +
          '3. After refractory: Normal response');
}

function startPropagation() {
    const canvas = document.getElementById('propagationCanvas');
    if (!canvas) return;
    
    const ctx = canvas.getContext('2d');
    const visualizer = new ActionPotentialVisualizer();
    
    let offset = 0;
    const propagate = () => {
        visualizer.drawPropagationDiagram(ctx, canvas.width, canvas.height);
        visualizer.drawPropagatingWave(ctx, canvas.width, canvas.height, offset);
        
        offset += 5;
        if (offset < canvas.width - 200) {
            setTimeout(() => requestAnimationFrame(propagate), 100);
        }
    };
    
    propagate();
}

function showLocalCurrents() {
    const canvas = document.getElementById('propagationCanvas');
    if (!canvas) return;
    
    const ctx = canvas.getContext('2d');
    const visualizer = new ActionPotentialVisualizer();
    visualizer.drawPropagationDiagram(ctx, canvas.width, canvas.height);
    
    // Enhanced current visualization
    ctx.strokeStyle = '#f39c12';
    ctx.lineWidth = 2;
    
    for (let x = 100; x < canvas.width - 100; x += 40) {
        visualizer.drawCurrentArrow(ctx, x, canvas.height/2 - 40, x + 20, canvas.height/2 - 20);
        visualizer.drawCurrentArrow(ctx, x + 20, canvas.height/2 + 20, x, canvas.height/2 + 40);
    }
    
    ctx.fillStyle = '#f39c12';
    ctx.font = 'bold 12px Arial';
    ctx.fillText('Local Current Flow', 20, 30);
}

function resetPropagation() {
    const canvas = document.getElementById('propagationCanvas');
    if (!canvas) return;
    
    const ctx = canvas.getContext('2d');
    const visualizer = new ActionPotentialVisualizer();
    visualizer.drawPropagationDiagram(ctx, canvas.width, canvas.height);
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    const visualizer = new ActionPotentialVisualizer();
    
    // Setup any additional interactive elements
    console.log('Lecture 1.2 interactive elements initialized');
});
