<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Lecture 3.1: Cardiac Electrophysiology</title>
    <link rel="stylesheet" href="../../css/style.css">
    <link rel="stylesheet" href="lecture-slides.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/gsap.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/3.9.1/chart.min.js"></script>
</head>
<body class="lecture-mode">
    <header class="lecture-header">
        <div class="container">
            <div class="lecture-navigation">
                <a href="../../index.html" class="nav-btn">🏠 Home</a>
                <a href="../../modules/modules-overview.html#module3" class="nav-btn">📚 Module 3</a>
                <div class="lecture-info">
                    <span class="lecture-title">Lecture 3.1: Cardiac Electrophysiology</span>
                    <span class="slide-counter">Slide <span id="currentSlide">1</span> of <span id="totalSlides">7</span></span>
                </div>
                <div class="lecture-controls">
                    <button id="prevSlide" class="control-btn">⬅️ Previous</button>
                    <button id="nextSlide" class="control-btn">➡️ Next</button>
                    <button id="fullscreenBtn" class="control-btn">🔍 Fullscreen</button>
                </div>
            </div>
        </div>
    </header>

    <main class="lecture-content">
        <div class="slides-container" id="slidesContainer">

            <!-- Slide 1: Title Slide -->
            <div class="slide active" data-slide="1">
                <div class="slide-content title-slide">
                    <h1>💓 Cardiac Electrophysiology</h1>
                    <h2>Module 3 - Lecture 3.1</h2>
                    <div class="title-info">
                        <p><strong>Medical Instrumentation: Principles and Applications</strong></p>
                        <p>Understanding the Heart's Electrical Symphony</p>
                    </div>
                    <div class="author-info">
                        <p><strong>Dr. Mohammed Yagoub Esmail</strong></p>
                        <p>Sudan University of Science and Technology (SUST)</p>
                        <p>Biomedical Engineering Department - 2025</p>
                    </div>
                    <!-- Placeholder for an animated heart/ECG intro -->
                    <div class="animated-cardiac-intro" id="cardiacIntroAnimation">
                        <!-- e.g., <img src="../../images/animated-heart-placeholder.gif" alt="Animated Heartbeat"> -->
                        <p style="text-align:center; margin-top:20px;">Animated Heart/ECG Visualization Placeholder</p>
                    </div>
                </div>
            </div>

            <!-- Slide 2: Learning Objectives -->
            <div class="slide" data-slide="2">
                <div class="slide-content">
                    <h2>🎯 Learning Objectives</h2>
                    <div class="objectives-list">
                        <div class="objective-item" data-animate="1">
                            <div class="objective-icon">❤️</div>
                            <div class="objective-text">
                                <h3>Understand Cardiac Cell Properties</h3>
                                <p>Explore automaticity, excitability, conductivity, and contractility of heart cells.</p>
                            </div>
                        </div>
                        <div class="objective-item" data-animate="2">
                            <div class="objective-icon">⚡</div>
                            <div class="objective-text">
                                <h3>Map the Heart's Conduction System</h3>
                                <p>Identify components: SA node, AV node, Bundle of His, Purkinje fibers and their roles.</p>
                            </div>
                        </div>
                        <div class="objective-item" data-animate="3">
                            <div class="objective-icon">📈</div>
                            <div class="objective-text">
                                <h3>Analyze Cardiac Action Potentials</h3>
                                <p>Differentiate action potentials in pacemaker vs. non-pacemaker cells.</p>
                            </div>
                        </div>
                        <div class="objective-item" data-animate="4">
                            <div class="objective-icon">📊</div>
                            <div class="objective-text">
                                <h3>Relate Electrical Activity to ECG</h3>
                                <p>Understand how the heart's electrical events generate the ECG waveform (P, QRS, T).</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Slide 3: The Heart's Electrical Conduction System -->
            <div class="slide" data-slide="3">
                <div class="slide-content">
                    <h2>⚡ The Heart's Electrical Conduction System</h2>
                    <div class="content-grid">
                        <div class="content-left">
                            <!-- SVG Block Diagram of Conduction System -->
                            <div class="diagram-container" id="conductionSystemDiagramContainer">
                                <svg id="conductionSystemSvg" viewBox="0 0 400 350" style="width:100%; max-width:400px; border:1px solid #ccc;">
                                    <title>Heart Conduction System</title>
                                    <!-- Atria -->
                                    <rect x="50" y="30" width="300" height="100" fill="#ffe0e0" stroke="#e74c3c" stroke-width="1" rx="10"/>
                                    <text x="200" y="20" fill="#c0392b" font-size="12" text-anchor="middle">Atria</text>

                                    <!-- Ventricles -->
                                    <rect x="50" y="180" width="300" height="150" fill="#ffeded" stroke="#e74c3c" stroke-width="1" rx="10"/>
                                    <text x="200" y="170" fill="#c0392b" font-size="12" text-anchor="middle">Ventricles</text>

                                    <!-- SA Node -->
                                    <g id="svg-sa-node" class="conduction-part">
                                        <ellipse cx="280" cy="60" rx="30" ry="15" fill="#e74c3c" stroke="#c0392b" stroke-width="2"/>
                                        <text x="280" y="65" fill="white" font-size="10" text-anchor="middle">SA Node</text>
                                    </g>

                                    <!-- Internodal Pathways (conceptual) -->
                                    <path d="M250,65 Q200,80 150,100" stroke="#f39c12" stroke-width="2" fill="none" stroke-dasharray="4 2"/>
                                    <path d="M280,75 Q220,100 155,105" stroke="#f39c12" stroke-width="2" fill="none" stroke-dasharray="4 2"/>

                                    <!-- AV Node -->
                                    <g id="svg-av-node" class="conduction-part">
                                        <ellipse cx="150" cy="110" rx="25" ry="12" fill="#e74c3c" stroke="#c0392b" stroke-width="2"/>
                                        <text x="150" y="115" fill="white" font-size="10" text-anchor="middle">AV Node</text>
                                    </g>

                                    <!-- Bundle of His -->
                                    <g id="svg-his-bundle" class="conduction-part">
                                        <rect x="140" y="130" width="20" height="30" fill="#c0392b" stroke="#a03020" stroke-width="1"/>
                                        <text x="150" y="145" fill="white" font-size="8" text-anchor="middle" writing-mode="tb">His</text>
                                    </g>

                                    <!-- Bundle Branches -->
                                    <g id="svg-bundle-branches" class="conduction-part">
                                        <path d="M145,160 L100,230" stroke="#c0392b" stroke-width="4" fill="none"/>
                                        <text x="90" y="200" fill="#c0392b" font-size="9">LBB</text>
                                        <path d="M155,160 L200,230" stroke="#c0392b" stroke-width="4" fill="none"/>
                                        <text x="210" y="200" fill="#c0392b" font-size="9">RBB</text>
                                    </g>

                                    <!-- Purkinje Fibers (conceptual representation) -->
                                    <g id="svg-purkinje-fibers" class="conduction-part">
                                        <path d="M100,230 L80,250 L100,270 L70,290" stroke="#a03020" stroke-width="2" fill="none" stroke-dasharray="3 1"/>
                                        <path d="M200,230 L220,250 L200,270 L230,290" stroke="#a03020" stroke-width="2" fill="none" stroke-dasharray="3 1"/>
                                        <text x="60" y="310" fill="#a03020" font-size="9">Purkinje</text>
                                        <text x="240" y="310" fill="#a03020" font-size="9">Purkinje</text>
                                    </g>
                                </svg>
                                <p class="caption">SVG Block Diagram: Heart's Conduction Pathway</p>
                            </div>
                            <div class="interactive-icons-info" style="margin-top:15px;">
                                <p>Interactive Icons (click to highlight):</p>
                                <span class="interactive-icon" data-target="svg-sa-node">SA Node</span>
                                <span class="interactive-icon" data-target="svg-av-node">AV Node</span>
                                <span class="interactive-icon" data-target="svg-his-bundle">Bundle of His</span>
                                <span class="interactive-icon" data-target="svg-bundle-branches">Bundle Branches</span>
                                <span class="interactive-icon" data-target="svg-purkinje-fibers">Purkinje Fibers</span>
                            </div>
                        </div>
                        <div class="content-right">
                            <h3>Key Components & Function</h3>
                            <p>The heart's conduction system is a network of specialized cells that initiate and transmit electrical impulses, ensuring coordinated contractions.</p>
                            <ul>
                                <li><strong>Sinoatrial (SA) Node:</strong> The natural pacemaker, initiates impulses.</li>
                                <li><strong>Atrioventricular (AV) Node:</strong> Delays impulse briefly to allow atrial contraction before ventricular.</li>
                                <li><strong>Bundle of His:</strong> Transmits impulse from AV node to ventricles.</li>
                                <li><strong>Bundle Branches (Left & Right):</strong> Carry impulse down the septum.</li>
                                <li><strong>Purkinje Fibers:</strong> Distribute impulse rapidly throughout ventricular myocardium.</li>
                            </ul>
                            <!-- Animated tool placeholder -->
                            <button class="demo-btn" onclick="animateConductionPath()">🎬 Animate Conduction Path</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Slide 4: Sinoatrial (SA) Node - The Pacemaker -->
            <div class="slide" data-slide="4">
                <div class="slide-content">
                    <h2>⏱️ Sinoatrial (SA) Node: The Natural Pacemaker</h2>
                    <div class="content-grid">
                        <div class="content-left">
                            <h3>SA Node Properties</h3>
                            <ul>
                                <li>Located in the upper wall of the right atrium.</li>
                                <li>Highest intrinsic firing rate (60-100 bpm).</li>
                                <li>Exhibits 'pacemaker potential' - spontaneous depolarization.</li>
                                <li>Influenced by autonomic nervous system (sympathetic ↑ rate, parasympathetic ↓ rate).</li>
                            </ul>
                            <!-- SVG Schematic Diagram of SA node cell -->
                            <div class="diagram-container" id="saNodeSchematicContainer">
                                <svg id="saNodeCellSvg" viewBox="0 0 350 250" style="width:100%; max-width:350px; border:1px solid #ccc;">
                                    <title>SA Node Cell Pacemaker Potential</title>
                                    <!-- Cell Membrane -->
                                    <ellipse cx="175" cy="125" rx="120" ry="70" fill="#fff5e6" stroke="#e74c3c" stroke-width="2" class="sa-cell-membrane"/>
                                    <text x="175" y="30" font-size="12" fill="#c0392b" text-anchor="middle">SA Node Cell</text>

                                    <!-- Ion Channels (Conceptual) -->
                                    <!-- Funny Current (If) - Na+ influx -->
                                    <g id="svg-if-channel" class="sa-channel" data-channel-name="Funny Current (If)">
                                        <rect x="50" y="100" width="20" height="50" fill="#f39c12" rx="5"/>
                                        <text x="60" y="95" font-size="10">If (Na⁺)</text>
                                        <path d="M60,150 v -40 l -7,7 m 7,-7 l 7,7" stroke="#2c3e50" stroke-width="1.5" fill="none" class="ion-flow na-flow"/>
                                    </g>

                                    <!-- T-type Ca2+ Channel -->
                                    <g id="svg-caT-channel" class="sa-channel" data-channel-name="T-type Ca²⁺ Channel">
                                        <rect x="120" y="50" width="20" height="50" fill="#3498db" rx="5"/>
                                        <text x="130" y="45" font-size="10">Ca²⁺ (T)</text>
                                        <path d="M130,100 v -40 l -7,7 m 7,-7 l 7,7" stroke="#2c3e50" stroke-width="1.5" fill="none" class="ion-flow ca-flow"/>
                                    </g>

                                    <!-- L-type Ca2+ Channel -->
                                    <g id="svg-caL-channel" class="sa-channel" data-channel-name="L-type Ca²⁺ Channel">
                                        <rect x="190" y="50" width="20" height="50" fill="#2ecc71" rx="5"/>
                                        <text x="200" y="45" font-size="10">Ca²⁺ (L)</text>
                                        <path d="M200,100 v -40 l -7,7 m 7,-7 l 7,7" stroke="#2c3e50" stroke-width="1.5" fill="none" class="ion-flow ca-flow"/>
                                    </g>

                                    <!-- K+ Channel -->
                                    <g id="svg-k-channel" class="sa-channel" data-channel-name="K⁺ Channel">
                                        <rect x="260" y="100" width="20" height="50" fill="#9b59b6" rx="5"/>
                                        <text x="270" y="95" font-size="10">K⁺</text>
                                        <path d="M270,110 v 40 l -7,-7 m 7,7 l 7,-7" stroke="#2c3e50" stroke-width="1.5" fill="none" class="ion-flow k-flow"/>
                                    </g>
                                    
                                    <text x="175" y="230" font-size="10" fill="#555" text-anchor="middle">Click channels for info. Arrows show ion flow during different phases.</text>
                                </svg>
                                <p class="caption">Schematic: SA Node Cell & Ion Channels for Pacemaker Potential</p>
                            </div>
                        </div>
                        <div class="content-right">
                            <h3>Pacemaker Potential (Diastolic Depolarization)</h3>
                            <p>Unlike other cardiac cells, SA node cells don't have a stable resting potential. They slowly depolarize due to:</p>
                            <ul>
                                <li>"Funny" current (If): Slow influx of Na⁺.</li>
                                <li>Decreased K⁺ efflux.</li>
                                <li>Transient Ca²⁺ current (T-type).</li>
                            </ul>
                            <p>Once threshold is reached, an action potential is triggered primarily by L-type Ca²⁺ channels.</p>
                            <!-- Animated tool placeholder for pacemaker potential -->
                            <div id="pacemakerPotentialAnimation" style="height:150px; background:#f0f0f0; text-align:center; line-height:150px;">Pacemaker Potential Animation Placeholder</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Slide 5: ECG Signal Generation - The Basics -->
            <div class="slide" data-slide="5">
                <div class="slide-content">
                    <h2>📊 ECG Signal Generation: Linking Electricity to Waveforms</h2>
                    <p>The Electrocardiogram (ECG) is a surface recording of the collective electrical activity of the heart. Each wave corresponds to specific electrical events.</p>
                    <div class="content-grid">
                        <div class="content-left">
                            <!-- SVG Diagram: Heart Chambers + ECG Waveform -->
                            <div class="diagram-container" id="heartEcgCorrelationDiagram">
                                <svg id="heartEcgSvg" viewBox="0 0 500 350" style="width:100%; max-width:500px; border:1px solid #ccc;">
                                    <title>Heart Electrical Activity and ECG Waves</title>
                                    <!-- Simplified Heart Outline -->
                                    <path id="svg-heart-outline" d="M250,60 C250,60 180,20 150,100 C120,180 250,280 250,280 C250,280 380,180 350,100 C320,20 250,60 250,60 Z"
                                          fill="#ffe0e0" stroke="#c0392b" stroke-width="2"/>
                                    <text x="250" y="40" font-size="12" text-anchor="middle" fill="#c0392b">Heart</text>
                                    
                                    <!-- Conceptual Atrial Depolarization Area -->
                                    <ellipse id="svg-atrial-depol" cx="250" cy="100" rx="80" ry="40" fill="rgba(231, 76, 60, 0)" opacity="0" class="ecg-event-highlight"/>
                                    <!-- Conceptual Ventricular Depolarization Area -->
                                    <path id="svg-ventricular-depol" d="M180,150 Q250,260 320,150 L250,270 Z" fill="rgba(231, 76, 60, 0)" opacity="0" class="ecg-event-highlight"/>
                                     <!-- Conceptual Ventricular Repolarization Area -->
                                    <path id="svg-ventricular-repol" d="M190,160 Q250,250 310,160 L250,260 Z" fill="rgba(46, 204, 113, 0)" opacity="0" class="ecg-event-highlight"/>

                                    <!-- ECG Baseline -->
                                    <line x1="50" y1="300" x2="450" y2="300" stroke="#7f8c8d" stroke-width="1.5"/>

                                    <!-- P Wave -->
                                    <path id="svg-p-wave" class="ecg-wave" d="M80,300 Q90,280 100,300 T120,300" stroke="#c0392b" stroke-width="2" fill="none"/>
                                    <text x="100" y="320" font-size="10" fill="#c0392b" text-anchor="middle">P</text>

                                    <!-- PR Segment -->
                                    <line id="svg-pr-segment" class="ecg-segment" x1="120" y1="300" x2="140" y2="300" stroke="#7f8c8d" stroke-width="2"/>
                                    
                                    <!-- QRS Complex -->
                                    <path id="svg-qrs-complex" class="ecg-wave" d="M140,300 L145,305 L155,270 L165,310 L170,300 L180,300" stroke="#c0392b" stroke-width="2" fill="none"/>
                                    <text x="160" y="320" font-size="10" fill="#c0392b" text-anchor="middle">QRS</text>

                                    <!-- ST Segment -->
                                    <line id="svg-st-segment" class="ecg-segment" x1="180" y1="300" x2="220" y2="300" stroke="#7f8c8d" stroke-width="2"/>

                                    <!-- T Wave -->
                                    <path id="svg-t-wave" class="ecg-wave" d="M220,300 Q235,275 250,300 T280,300" stroke="#c0392b" stroke-width="2" fill="none"/>
                                    <text x="250" y="320" font-size="10" fill="#c0392b" text-anchor="middle">T</text>
                                    
                                    <text x="250" y="340" font-size="10" fill="#555" text-anchor="middle">Simplified ECG Trace. Click buttons to see correlations.</text>
                                </svg>
                                <p class="caption">Diagram: Correlation of Cardiac Events and ECG Waves</p>
                            </div>
                        </div>
                        <div class="content-right">
                            <h3>ECG Components:</h3>
                            <ul>
                                <li><strong>P Wave:</strong> Atrial depolarization (SA node fires, atria contract).</li>
                                <li><strong>PR Interval:</strong> Time for impulse to travel from SA node through AV node to ventricles.</li>
                                <li><strong>QRS Complex:</strong> Ventricular depolarization (ventricles contract). Atrial repolarization is masked here.</li>
                                <li><strong>ST Segment:</strong> Period when ventricles are fully depolarized.</li>
                                <li><strong>T Wave:</strong> Ventricular repolarization.</li>
                                <li><strong>QT Interval:</strong> Duration of ventricular depolarization and repolarization.</li>
                            </ul>
                            <!-- Interactive icon/tool placeholder -->
                            <button class="demo-btn" onclick="highlightEcgWave('P')">Highlight P Wave</button>
                            <button class="demo-btn" onclick="highlightEcgWave('QRS')">Highlight QRS Complex</button>
                            <button class="demo-btn" onclick="highlightEcgWave('T')">Highlight T Wave</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Slide 6: Summary -->
            <div class="slide" data-slide="6">
                <div class="slide-content">
                    <h2>📋 Lecture Summary</h2>
                    <div class="summary-grid">
                        <div class="summary-section">
                            <h3>❤️ Cardiac Cell Properties</h3>
                            <ul>
                                <li>Automaticity, Excitability, Conductivity, Contractility.</li>
                            </ul>
                        </div>
                        <div class="summary-section">
                            <h3>⚡ Conduction System</h3>
                            <ul>
                                <li>SA Node (pacemaker), AV Node (delay), His-Purkinje system (distribution).</li>
                            </ul>
                        </div>
                        <div class="summary-section">
                            <h3>📈 Action Potentials</h3>
                            <ul>
                                <li>Pacemaker potential in SA node.</li>
                                <li>Phases of ventricular action potential (to be covered more).</li>
                            </ul>
                        </div>
                        <div class="summary-section">
                            <h3>📊 ECG Basics</h3>
                            <ul>
                                <li>P wave (atrial depolarization).</li>
                                <li>QRS complex (ventricular depolarization).</li>
                                <li>T wave (ventricular repolarization).</li>
                            </ul>
                        </div>
                    </div>
                    <div class="key-takeaway">
                        <p><strong>Key Takeaway:</strong> The heart's coordinated electrical activity, originating from the SA node and spreading through the conduction system, is fundamental to its pumping function and is reflected in the ECG waveform.</p>
                    </div>
                </div>
            </div>

            <!-- Slide 7: Next Lecture -->
            <div class="slide" data-slide="7">
                <div class="slide-content">
                    <h2>🔜 Next Up: Lecture 3.2 - ECG Lead Systems</h2>
                    <p>In the next lecture, we will delve into:</p>
                    <ul>
                        <li>Standard ECG limb leads and precordial leads.</li>
                        <li>Einthoven's triangle and vector analysis.</li>
                        <li>Practical aspects of ECG recording.</li>
                    </ul>
                    <img src="../../images/placeholder_ecg_leads.png" alt="ECG Lead Systems Preview" style="display:block; margin:20px auto; max-width:300px;">
                    <br>
                    <a href="lecture3-2.html" class="next-btn">➡️ Continue to Lecture 3.2 (Coming Soon)</a>
                </div>
            </div>

        </div>
    </main>

    <footer class="lecture-footer">
        <div class="container">
            <div class="footer-content">
                <div class="author-info">
                    <p><strong>Dr. Mohammed Yagoub Esmail</strong> | SUST - BME 2025</p>
                    <p>📧 <EMAIL> | 📱 +249912867327, +966538076790</p>
                </div>
                <div class="copyright">
                    <p>&copy; 2025 Dr. Mohammed Yagoub Esmail - All rights reserved</p>
                </div>
            </div>
        </div>
    </footer>

    <script src="lecture-slides.js"></script>
    <script src="lecture3-1-interactive.js"></script>
</body>
</html>