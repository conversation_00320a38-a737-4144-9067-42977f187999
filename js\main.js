// Medical Instrumentation Course - Main JavaScript File

document.addEventListener('DOMContentLoaded', function() {
    // Initialize the page
    initializeNavigation();
    initializeAnimations();
    initializeProjectCards();
    initializeBackToTopButton(); // Added this line

    // Initialize hero canvas animation
    initHeroAnimation();

    // Initialize animation controls
    initAnimationControls();

    console.log('Medical Instrumentation Course website loaded successfully with enhancements!');
});

// Navigation functionality
function initializeNavigation() {
    const navLinks = document.querySelectorAll('nav a[href^="#"]');

    navLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();

            const targetId = this.getAttribute('href').substring(1);
            const targetElement = document.getElementById(targetId);

            if (targetElement) {
                // Smooth scroll to target
                targetElement.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });

                // Update active navigation state
                updateActiveNavigation(this);
            }
        });
    });

    // Update navigation on scroll
    window.addEventListener('scroll', updateNavigationOnScroll);
}

// Update active navigation item
function updateActiveNavigation(activeLink) {
    const navLinks = document.querySelectorAll('nav a');
    navLinks.forEach(link => link.classList.remove('active'));
    activeLink.classList.add('active');
}

// Update navigation based on scroll position
function updateNavigationOnScroll() {
    const sections = document.querySelectorAll('.section, footer');
    const navLinks = document.querySelectorAll('nav a[href^="#"]');

    let currentSection = '';

    sections.forEach(section => {
        const sectionTop = section.offsetTop - 100;
        const sectionHeight = section.offsetHeight;

        if (window.scrollY >= sectionTop && window.scrollY < sectionTop + sectionHeight) {
            currentSection = section.getAttribute('id');
        }
    });

    navLinks.forEach(link => {
        link.classList.remove('active');
        if (link.getAttribute('href') === `#${currentSection}`) {
            link.classList.add('active');
        }
    });
}

// Initialize animations
function initializeAnimations() {
    // Fade in animation for cards
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateY(0)';
            }
        });
    }, observerOptions);

    // Observe all cards
    const cards = document.querySelectorAll('.feature-card, .project-card, .resource-item');
    cards.forEach(card => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(20px)';
        card.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
        observer.observe(card);
    });
}

// Project card interactions
function initializeProjectCards() {
    const projectCards = document.querySelectorAll('.project-card');

    projectCards.forEach(card => {
        // Add hover effects
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-5px)';
            this.style.boxShadow = '0 8px 15px rgba(0,0,0,0.15)';
        });

        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
            this.style.boxShadow = '0 4px 6px rgba(0,0,0,0.1)';
        });

        // Add click tracking for analytics (if needed)
        const projectLinks = card.querySelectorAll('.btn');
        projectLinks.forEach(link => {
            link.addEventListener('click', function() {
                const projectName = card.querySelector('h3').textContent;
                console.log(`Project accessed: ${projectName}`);
                // Here you could add analytics tracking
            });
        });
    });
}

// Utility function to show loading state
function showLoading(element) {
    element.innerHTML = '<div class="loading">Loading...</div>';
}

// Utility function to hide loading state
function hideLoading(element, originalContent) {
    element.innerHTML = originalContent;
}

// Function to dynamically load project data (for future enhancement)
function loadProjectData(projectPath) {
    // This function could be used to dynamically load project information
    // from JSON files or APIs in the future
    console.log(`Loading data for project: ${projectPath}`);
}

// Add CSS for active navigation state
const style = document.createElement('style');
style.textContent = `
    nav a.active {
        background-color: #2c3e50 !important;
        font-weight: bold;
    }

    .loading {
        text-align: center;
        padding: 2rem;
        color: #7f8c8d;
    }

    .project-card {
        transition: transform 0.3s ease, box-shadow 0.3s ease;
    }
`;
document.head.appendChild(style);

// Hero Canvas Animation
function initHeroAnimation() {
    const canvas = document.getElementById('heroCanvas');
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    let animationId;
    let isPlaying = true;
    let time = 0;

    // Animation state
    window.heroAnimation = {
        isPlaying: isPlaying,
        time: time,
        canvas: canvas,
        ctx: ctx
    };

    function drawSignalWave() {
        ctx.clearRect(0, 0, canvas.width, canvas.height);

        // Draw background grid
        drawGrid(ctx, canvas.width, canvas.height);

        // Draw ECG-like signal
        drawECGSignal(ctx, canvas.width, canvas.height, time);

        // Draw EMG signal
        drawEMGSignal(ctx, canvas.width, canvas.height, time);

        // Draw EEG signal
        drawEEGSignal(ctx, canvas.width, canvas.height, time);

        // Update time
        if (isPlaying) {
            time += 0.02;
        }

        // Continue animation
        animationId = requestAnimationFrame(drawSignalWave);
    }

    function drawGrid(ctx, width, height) {
        ctx.strokeStyle = 'rgba(255,255,255,0.1)';
        ctx.lineWidth = 0.5;

        // Vertical lines
        for (let x = 0; x < width; x += 20) {
            ctx.beginPath();
            ctx.moveTo(x, 0);
            ctx.lineTo(x, height);
            ctx.stroke();
        }

        // Horizontal lines
        for (let y = 0; y < height; y += 20) {
            ctx.beginPath();
            ctx.moveTo(0, y);
            ctx.lineTo(width, y);
            ctx.stroke();
        }
    }

    function drawECGSignal(ctx, width, height, t) {
        ctx.strokeStyle = '#e74c3c';
        ctx.lineWidth = 3;
        ctx.beginPath();

        const baseY = height * 0.3;
        const amplitude = 40;

        for (let x = 0; x < width; x++) {
            const normalizedX = (x / width) * 4 * Math.PI + t;
            let y = baseY;

            // ECG pattern simulation
            const heartbeat = Math.sin(normalizedX * 0.8);
            if (heartbeat > 0.7) {
                y += amplitude * (1 - Math.abs(normalizedX % (Math.PI * 2.5) - Math.PI * 1.25) / (Math.PI * 0.1));
            } else {
                y += amplitude * 0.1 * Math.sin(normalizedX * 3);
            }

            if (x === 0) {
                ctx.moveTo(x, y);
            } else {
                ctx.lineTo(x, y);
            }
        }
        ctx.stroke();

        // Add label
        ctx.fillStyle = '#e74c3c';
        ctx.font = 'bold 14px Arial';
        ctx.fillText('💓 ECG', 10, baseY - 50);
    }

    function drawEMGSignal(ctx, width, height, t) {
        ctx.strokeStyle = '#27ae60';
        ctx.lineWidth = 2;
        ctx.beginPath();

        const baseY = height * 0.6;
        const amplitude = 25;

        for (let x = 0; x < width; x++) {
            const normalizedX = (x / width) * 8 * Math.PI + t;
            const noise = (Math.random() - 0.5) * amplitude * 0.8;
            const signal = amplitude * 0.3 * Math.sin(normalizedX * 2) * Math.sin(normalizedX * 0.5);
            const y = baseY + signal + noise;

            if (x === 0) {
                ctx.moveTo(x, y);
            } else {
                ctx.lineTo(x, y);
            }
        }
        ctx.stroke();

        // Add label
        ctx.fillStyle = '#27ae60';
        ctx.font = 'bold 14px Arial';
        ctx.fillText('💪 EMG', 10, baseY - 30);
    }

    function drawEEGSignal(ctx, width, height, t) {
        ctx.strokeStyle = '#3498db';
        ctx.lineWidth = 2;
        ctx.beginPath();

        const baseY = height * 0.85;
        const amplitude = 15;

        for (let x = 0; x < width; x++) {
            const normalizedX = (x / width) * 6 * Math.PI + t;
            const alpha = amplitude * 0.6 * Math.sin(normalizedX * 1.2);
            const beta = amplitude * 0.3 * Math.sin(normalizedX * 2.5);
            const y = baseY + alpha + beta;

            if (x === 0) {
                ctx.moveTo(x, y);
            } else {
                ctx.lineTo(x, y);
            }
        }
        ctx.stroke();

        // Add label
        ctx.fillStyle = '#3498db';
        ctx.font = 'bold 14px Arial';
        ctx.fillText('🧠 EEG', 10, baseY - 20);
    }

    // Start animation
    drawSignalWave();

    // Store animation functions globally
    window.heroAnimation.start = function() {
        isPlaying = true;
        window.heroAnimation.isPlaying = true;
        drawSignalWave();
    };

    window.heroAnimation.pause = function() {
        isPlaying = false;
        window.heroAnimation.isPlaying = false;
        if (animationId) {
            cancelAnimationFrame(animationId);
        }
    };

    window.heroAnimation.reset = function() {
        time = 0;
        window.heroAnimation.time = 0;
        drawSignalWave();
    };
}

function initAnimationControls() {
    const playBtn = document.getElementById('playAnimation');
    const pauseBtn = document.getElementById('pauseAnimation');
    const resetBtn = document.getElementById('resetAnimation');

    if (playBtn) {
        playBtn.addEventListener('click', function() {
            if (window.heroAnimation) {
                window.heroAnimation.start();
            }
        });
    }

    if (pauseBtn) {
        pauseBtn.addEventListener('click', function() {
            if (window.heroAnimation) {
                window.heroAnimation.pause();
            }
        });
    }

    if (resetBtn) {
        resetBtn.addEventListener('click', function() {
            if (window.heroAnimation) {
                window.heroAnimation.reset();
            }
        });
    }
}

// Export functions for potential use in other scripts
window.MedicalInstrumentationCourse = {
    updateActiveNavigation,
    loadProjectData,
    showLoading,
    hideLoading,
    heroAnimation: window.heroAnimation
};

// Back to Top Button Functionality
function initializeBackToTopButton() {
    const backToTopBtn = document.getElementById('backToTopBtn');

    if (!backToTopBtn) return;

    window.addEventListener('scroll', () => {
        if (window.pageYOffset > 300) { // Show button after scrolling 300px
            backToTopBtn.style.display = 'block';
        } else {
            backToTopBtn.style.display = 'none';
        }
    });

    backToTopBtn.addEventListener('click', (e) => {
        e.preventDefault();
        window.scrollTo({
            top: 0,
            behavior: 'smooth'
        });
    });
}
