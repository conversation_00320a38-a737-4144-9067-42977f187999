// Lecture 2.4 Interactive Elements - ADC Systems

// Global variables for animations
let samplingAnimation = null;
let isAnimating = false;

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    console.log('Lecture 2.4 interactive elements loading...');
    
    // Initialize all interactive elements
    initializeADCVisualizations();
    initializeSamplingDemo();
    initializeQuantizationDemo();
    initializeSelectionGuide();
    
    console.log('Lecture 2.4 interactive elements loaded successfully');
});

// Initialize ADC visualizations
function initializeADCVisualizations() {
    // Animate conversion flow
    const flowSteps = document.querySelectorAll('.signal-type, .process-step');
    const arrows = document.querySelectorAll('.arrow');
    
    // Animate flow sequence
    flowSteps.forEach((step, index) => {
        setTimeout(() => {
            if (typeof gsap !== 'undefined') {
                gsap.from(step, {
                    scale: 0,
                    duration: 0.8,
                    ease: "back.out(1.7)"
                });
            }
        }, index * 150);
    });
    
    arrows.forEach((arrow, index) => {
        setTimeout(() => {
            if (typeof gsap !== 'undefined') {
                gsap.from(arrow, {
                    x: -20,
                    opacity: 0,
                    duration: 0.5,
                    ease: "power2.out"
                });
            }
        }, (index + 1) * 150 + 75);
    });
}

// Initialize sampling demonstration
function initializeSamplingDemo() {
    const canvas = document.getElementById('samplingCanvas');
    if (!canvas) return;
    
    const ctx = canvas.getContext('2d');
    
    // Set up control event listeners
    const signalFreqSlider = document.getElementById('signalFreq');
    const samplingFreqSlider = document.getElementById('samplingFreq');
    
    if (signalFreqSlider) {
        signalFreqSlider.addEventListener('input', updateSamplingDisplay);
    }
    if (samplingFreqSlider) {
        samplingFreqSlider.addEventListener('input', updateSamplingDisplay);
    }
    
    // Initial display
    updateSamplingDisplay();
}

function updateSamplingDisplay() {
    const signalFreq = parseFloat(document.getElementById('signalFreq')?.value) || 10;
    const samplingFreq = parseFloat(document.getElementById('samplingFreq')?.value) || 50;
    
    // Update display values
    const signalFreqValue = document.getElementById('signalFreqValue');
    const samplingFreqValue = document.getElementById('samplingFreqValue');
    
    if (signalFreqValue) signalFreqValue.textContent = signalFreq;
    if (samplingFreqValue) samplingFreqValue.textContent = samplingFreq;
    
    // Check Nyquist criterion
    const nyquistIndicator = document.getElementById('nyquistIndicator');
    const indicatorStatus = nyquistIndicator?.querySelector('.indicator-status');
    
    if (indicatorStatus) {
        if (samplingFreq >= 2 * signalFreq) {
            indicatorStatus.textContent = '✅ Satisfied';
            indicatorStatus.style.color = '#27ae60';
        } else {
            indicatorStatus.textContent = '❌ Violated (Aliasing!)';
            indicatorStatus.style.color = '#e74c3c';
        }
    }
    
    // Draw sampling visualization
    const canvas = document.getElementById('samplingCanvas');
    if (canvas) {
        drawSamplingVisualization(canvas.getContext('2d'), canvas.width, canvas.height, signalFreq, samplingFreq);
    }
}

function drawSamplingVisualization(ctx, width, height, signalFreq, samplingFreq) {
    ctx.clearRect(0, 0, width, height);
    
    // Draw background
    ctx.fillStyle = '#f8f9fa';
    ctx.fillRect(0, 0, width, height);
    
    // Draw axes
    const margin = 40;
    const plotWidth = width - 2 * margin;
    const plotHeight = height - 2 * margin;
    
    ctx.strokeStyle = '#2c3e50';
    ctx.lineWidth = 2;
    ctx.beginPath();
    ctx.moveTo(margin, height - margin);
    ctx.lineTo(width - margin, height - margin);
    ctx.moveTo(margin, margin);
    ctx.lineTo(margin, height - margin);
    ctx.stroke();
    
    // Draw continuous signal
    ctx.strokeStyle = '#3498db';
    ctx.lineWidth = 2;
    ctx.beginPath();
    
    for (let x = 0; x <= plotWidth; x += 2) {
        const t = x / plotWidth * 4; // 4 time units
        const signal = Math.sin(2 * Math.PI * signalFreq * t / 10); // Normalize frequency
        const y = margin + plotHeight/2 - signal * plotHeight/4;
        
        if (x === 0) {
            ctx.moveTo(margin + x, y);
        } else {
            ctx.lineTo(margin + x, y);
        }
    }
    ctx.stroke();
    
    // Draw sample points
    ctx.fillStyle = '#e74c3c';
    const samplePeriod = 10 / samplingFreq; // Normalized sampling period
    
    for (let t = 0; t <= 4; t += samplePeriod) {
        const x = margin + (t / 4) * plotWidth;
        const signal = Math.sin(2 * Math.PI * signalFreq * t / 10);
        const y = margin + plotHeight/2 - signal * plotHeight/4;
        
        ctx.beginPath();
        ctx.arc(x, y, 4, 0, 2 * Math.PI);
        ctx.fill();
        
        // Draw vertical lines to show sampling
        ctx.strokeStyle = '#e74c3c';
        ctx.lineWidth = 1;
        ctx.setLineDash([3, 3]);
        ctx.beginPath();
        ctx.moveTo(x, y);
        ctx.lineTo(x, height - margin);
        ctx.stroke();
        ctx.setLineDash([]);
    }
    
    // Add labels
    ctx.fillStyle = '#2c3e50';
    ctx.font = '12px Arial';
    ctx.fillText('Time', width - 50, height - 10);
    ctx.save();
    ctx.translate(15, height / 2);
    ctx.rotate(-Math.PI / 2);
    ctx.fillText('Amplitude', 0, 0);
    ctx.restore();
    
    // Add legend
    ctx.fillStyle = '#3498db';
    ctx.font = '10px Arial';
    ctx.fillText('Continuous Signal', margin + 10, margin + 15);
    ctx.fillStyle = '#e74c3c';
    ctx.fillText('Sample Points', margin + 10, margin + 30);
}

// Initialize quantization demonstration
function initializeQuantizationDemo() {
    const canvas = document.getElementById('quantizationCanvas');
    if (!canvas) return;
    
    // Initial display
    updateQuantizationDemo();
}

function updateQuantizationDemo() {
    const adcBits = parseInt(document.getElementById('adcBits')?.value) || 8;
    const inputRange = parseFloat(document.getElementById('inputRange')?.value) || 5;
    
    // Calculate metrics
    const levels = Math.pow(2, adcBits);
    const lsb = (2 * inputRange) / levels; // Full scale range / levels
    const snr = 6.02 * adcBits + 1.76; // Theoretical SNR in dB
    const enob = adcBits; // Assuming ideal ADC
    
    // Update display
    const lsbValue = document.getElementById('lsbValue');
    const snrValue = document.getElementById('snrValue');
    const enobValue = document.getElementById('enobValue');
    
    if (lsbValue) lsbValue.textContent = (lsb * 1000).toFixed(1) + ' mV';
    if (snrValue) snrValue.textContent = snr.toFixed(1) + ' dB';
    if (enobValue) enobValue.textContent = enob.toFixed(1) + ' bits';
    
    // Draw quantization visualization
    const canvas = document.getElementById('quantizationCanvas');
    if (canvas) {
        drawQuantizationVisualization(canvas.getContext('2d'), canvas.width, canvas.height, adcBits, inputRange);
    }
}

function drawQuantizationVisualization(ctx, width, height, bits, range) {
    ctx.clearRect(0, 0, width, height);
    
    // Draw background
    ctx.fillStyle = '#f8f9fa';
    ctx.fillRect(0, 0, width, height);
    
    // Draw axes
    const margin = 40;
    const plotWidth = width - 2 * margin;
    const plotHeight = height - 2 * margin;
    
    ctx.strokeStyle = '#2c3e50';
    ctx.lineWidth = 2;
    ctx.beginPath();
    ctx.moveTo(margin, height - margin);
    ctx.lineTo(width - margin, height - margin);
    ctx.moveTo(margin, margin);
    ctx.lineTo(margin, height - margin);
    ctx.stroke();
    
    // Draw quantization levels
    const levels = Math.pow(2, bits);
    const stepSize = plotHeight / levels;
    
    ctx.strokeStyle = '#bdc3c7';
    ctx.lineWidth = 1;
    
    for (let i = 0; i <= levels; i++) {
        const y = margin + i * stepSize;
        ctx.beginPath();
        ctx.moveTo(margin, y);
        ctx.lineTo(width - margin, y);
        ctx.stroke();
    }
    
    // Draw analog input signal
    ctx.strokeStyle = '#3498db';
    ctx.lineWidth = 2;
    ctx.beginPath();
    
    for (let x = 0; x <= plotWidth; x += 2) {
        const t = x / plotWidth * 2 * Math.PI;
        const signal = Math.sin(t) * 0.8; // 80% of full scale
        const y = margin + plotHeight/2 - signal * plotHeight/2;
        
        if (x === 0) {
            ctx.moveTo(margin + x, y);
        } else {
            ctx.lineTo(margin + x, y);
        }
    }
    ctx.stroke();
    
    // Draw quantized signal
    ctx.strokeStyle = '#e74c3c';
    ctx.lineWidth = 3;
    ctx.beginPath();
    
    let prevQuantizedY = null;
    
    for (let x = 0; x <= plotWidth; x += 5) {
        const t = x / plotWidth * 2 * Math.PI;
        const signal = Math.sin(t) * 0.8;
        const analogY = margin + plotHeight/2 - signal * plotHeight/2;
        
        // Quantize the signal
        const levelIndex = Math.round((analogY - margin) / stepSize);
        const quantizedY = margin + levelIndex * stepSize;
        
        if (prevQuantizedY !== null) {
            ctx.moveTo(margin + x - 5, prevQuantizedY);
            ctx.lineTo(margin + x, prevQuantizedY);
            ctx.lineTo(margin + x, quantizedY);
        }
        
        prevQuantizedY = quantizedY;
    }
    ctx.stroke();
    
    // Add labels
    ctx.fillStyle = '#2c3e50';
    ctx.font = '12px Arial';
    ctx.fillText('Input', width - 50, height - 10);
    ctx.save();
    ctx.translate(15, height / 2);
    ctx.rotate(-Math.PI / 2);
    ctx.fillText('Output', 0, 0);
    ctx.restore();
    
    // Add legend
    ctx.fillStyle = '#3498db';
    ctx.font = '10px Arial';
    ctx.fillText('Analog Input', margin + 10, margin + 15);
    ctx.fillStyle = '#e74c3c';
    ctx.fillText('Quantized Output', margin + 10, margin + 30);
    ctx.fillStyle = '#bdc3c7';
    ctx.fillText(`${Math.pow(2, bits)} Levels`, margin + 10, margin + 45);
}

// Initialize selection guide
function initializeSelectionGuide() {
    const canvas = document.getElementById('selectionCanvas');
    if (!canvas) return;
    
    // Initial display
    updateSelectionGuide();
}

function updateSelectionGuide() {
    const application = document.getElementById('applicationSelect')?.value || 'ecg';
    
    // Application requirements
    const requirements = {
        ecg: {
            type: 'Delta-Sigma ADC',
            resolution: '16-24 bits',
            sampleRate: '500-1000 SPS',
            power: '<1 mW',
            reason: 'High resolution needed for small cardiac signals (mV range) with low noise requirements'
        },
        eeg: {
            type: 'Delta-Sigma ADC',
            resolution: '24 bits',
            sampleRate: '256-1000 SPS',
            power: '<1 mW',
            reason: 'Very high resolution needed for small EEG signals (μV range) with excellent noise performance'
        },
        emg: {
            type: 'SAR ADC',
            resolution: '12-16 bits',
            sampleRate: '2-10 kSPS',
            power: '<5 mW',
            reason: 'Moderate resolution with higher bandwidth for muscle signal dynamics'
        },
        ultrasound: {
            type: 'Pipeline ADC',
            resolution: '10-14 bits',
            sampleRate: '10-100 MSPS',
            power: '100-500 mW',
            reason: 'High speed required for ultrasound imaging with moderate resolution'
        }
    };
    
    const req = requirements[application];
    
    // Update recommendation display
    const recommendationDisplay = document.getElementById('recommendationDisplay');
    if (recommendationDisplay) {
        const typeElement = recommendationDisplay.querySelector('.rec-type');
        const specsElement = recommendationDisplay.querySelector('.rec-specs');
        const reasonElement = recommendationDisplay.querySelector('.rec-reason');
        
        if (typeElement) typeElement.textContent = req.type;
        if (specsElement) {
            specsElement.innerHTML = `
                <span class="spec-item">Resolution: ${req.resolution}</span>
                <span class="spec-item">Sample Rate: ${req.sampleRate}</span>
                <span class="spec-item">Power: ${req.power}</span>
            `;
        }
        if (reasonElement) {
            reasonElement.innerHTML = `<strong>Reason:</strong> ${req.reason}`;
        }
    }
    
    // Draw selection matrix
    const canvas = document.getElementById('selectionCanvas');
    if (canvas) {
        drawSelectionMatrix(canvas.getContext('2d'), canvas.width, canvas.height, application);
    }
}

function drawSelectionMatrix(ctx, width, height, application) {
    ctx.clearRect(0, 0, width, height);
    
    // Draw background
    ctx.fillStyle = '#f8f9fa';
    ctx.fillRect(0, 0, width, height);
    
    const margin = 40;
    const plotWidth = width - 2 * margin;
    const plotHeight = height - 2 * margin;
    
    // ADC types and their characteristics
    const adcTypes = [
        { name: 'SAR', speed: 0.6, resolution: 0.5, power: 0.4, x: 0.3, y: 0.3 },
        { name: 'ΔΣ', speed: 0.2, resolution: 0.9, power: 0.2, x: 0.7, y: 0.7 },
        { name: 'Pipeline', speed: 0.9, resolution: 0.4, power: 0.8, x: 0.2, y: 0.6 }
    ];
    
    // Draw axes
    ctx.strokeStyle = '#2c3e50';
    ctx.lineWidth = 2;
    ctx.beginPath();
    ctx.moveTo(margin, height - margin);
    ctx.lineTo(width - margin, height - margin);
    ctx.moveTo(margin, margin);
    ctx.lineTo(margin, height - margin);
    ctx.stroke();
    
    // Draw ADC points
    adcTypes.forEach(adc => {
        const x = margin + adc.x * plotWidth;
        const y = margin + (1 - adc.y) * plotHeight;
        
        // Highlight if this is the recommended type for the application
        const isRecommended = (
            (application === 'ecg' && adc.name === 'ΔΣ') ||
            (application === 'eeg' && adc.name === 'ΔΣ') ||
            (application === 'emg' && adc.name === 'SAR') ||
            (application === 'ultrasound' && adc.name === 'Pipeline')
        );
        
        ctx.fillStyle = isRecommended ? '#e74c3c' : '#3498db';
        ctx.beginPath();
        ctx.arc(x, y, isRecommended ? 8 : 6, 0, 2 * Math.PI);
        ctx.fill();
        
        // Add labels
        ctx.fillStyle = '#2c3e50';
        ctx.font = isRecommended ? 'bold 12px Arial' : '10px Arial';
        ctx.fillText(adc.name, x - 10, y - 10);
    });
    
    // Add axis labels
    ctx.fillStyle = '#2c3e50';
    ctx.font = '12px Arial';
    ctx.fillText('Speed →', width - 60, height - 10);
    ctx.save();
    ctx.translate(15, height / 2);
    ctx.rotate(-Math.PI / 2);
    ctx.fillText('Resolution →', 0, 0);
    ctx.restore();
}

// Interactive functions

function toggleSamplingDemo() {
    if (isAnimating) {
        // Stop animation
        if (samplingAnimation) {
            clearInterval(samplingAnimation);
            samplingAnimation = null;
        }
        isAnimating = false;
    } else {
        // Start animation
        isAnimating = true;
        samplingAnimation = setInterval(() => {
            // Update signal frequency slightly for animation effect
            const signalFreqSlider = document.getElementById('signalFreq');
            if (signalFreqSlider) {
                let currentFreq = parseFloat(signalFreqSlider.value);
                currentFreq += 0.5;
                if (currentFreq > 50) currentFreq = 1;
                signalFreqSlider.value = currentFreq;
                updateSamplingDisplay();
            }
        }, 200);
    }
}

function showOriginalSignal() {
    // Implementation for showing original signal
    console.log('Showing original signal');
}

function showSampledSignal() {
    // Implementation for showing sampled signal
    console.log('Showing sampled signal');
}

function showReconstructed() {
    // Implementation for showing reconstructed signal
    console.log('Showing reconstructed signal');
}

// Add CSS for interactive elements
const style = document.createElement('style');
style.textContent = `
    .conversion-flow {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 1rem;
        margin: 2rem 0;
        flex-wrap: wrap;
    }
    
    .signal-type, .process-step {
        background: linear-gradient(135deg, #3498db, #2980b9);
        color: white;
        padding: 1rem 1.5rem;
        border-radius: 8px;
        font-weight: bold;
        text-align: center;
        min-width: 100px;
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        transition: all 0.3s ease;
    }
    
    .signal-type:hover, .process-step:hover {
        transform: translateY(-3px);
        box-shadow: 0 6px 12px rgba(0,0,0,0.15);
    }
    
    .arrow {
        font-size: 1.5rem;
        color: #e74c3c;
        font-weight: bold;
    }
    
    .sampling-controls, .quantization-controls {
        background: #f8f9fa;
        border-radius: 8px;
        padding: 1.5rem;
        margin: 1rem 0;
        border-left: 4px solid #3498db;
    }
    
    .control-group {
        margin: 1rem 0;
    }
    
    .control-group label {
        display: block;
        font-weight: bold;
        color: #2c3e50;
        margin-bottom: 0.5rem;
    }
    
    .control-group input[type="range"] {
        width: 100%;
        margin: 0.5rem 0;
    }
    
    .control-group select {
        width: 100%;
        padding: 0.5rem;
        border: 2px solid #bdc3c7;
        border-radius: 4px;
        font-size: 1rem;
    }
    
    .nyquist-indicator {
        background: white;
        border-radius: 6px;
        padding: 1rem;
        margin: 1rem 0;
        text-align: center;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    
    .indicator-label {
        font-weight: bold;
        color: #2c3e50;
        margin-right: 1rem;
    }
    
    .indicator-status {
        font-weight: bold;
        font-size: 1.1rem;
    }
    
    .quantization-metrics {
        background: white;
        border-radius: 8px;
        padding: 1.5rem;
        margin: 1rem 0;
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    }
    
    .metrics-display {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
        gap: 1rem;
    }
    
    .metric-item {
        text-align: center;
        padding: 1rem;
        background: #f8f9fa;
        border-radius: 6px;
    }
    
    .metric-label {
        display: block;
        font-weight: bold;
        color: #2c3e50;
        margin-bottom: 0.5rem;
    }
    
    .metric-value {
        display: block;
        font-size: 1.2rem;
        font-weight: bold;
        color: #3498db;
    }
    
    .biomedical-requirements {
        background: #f8f9fa;
        border-radius: 8px;
        padding: 1.5rem;
        margin: 1rem 0;
        border-left: 4px solid #27ae60;
    }
    
    .requirement-list {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1rem;
    }
    
    .req-item {
        background: white;
        border-radius: 6px;
        padding: 1rem;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    
    .req-item h5 {
        color: #27ae60;
        margin-bottom: 0.5rem;
        border-bottom: 2px solid #27ae60;
        padding-bottom: 0.25rem;
    }
    
    .req-item p {
        color: #7f8c8d;
        margin: 0;
        font-size: 0.9rem;
    }
    
    .recommendation-display {
        background: white;
        border-radius: 8px;
        padding: 1.5rem;
        margin: 1rem 0;
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        border-left: 4px solid #e74c3c;
    }
    
    .rec-type {
        font-size: 1.3rem;
        font-weight: bold;
        color: #e74c3c;
        margin-bottom: 1rem;
    }
    
    .rec-specs {
        display: flex;
        gap: 1rem;
        margin: 1rem 0;
        flex-wrap: wrap;
    }
    
    .spec-item {
        background: #e8f5e8;
        color: #27ae60;
        padding: 0.5rem 1rem;
        border-radius: 4px;
        font-size: 0.9rem;
        font-weight: bold;
    }
    
    .rec-reason {
        color: #7f8c8d;
        line-height: 1.5;
        margin-top: 1rem;
    }
    
    .reconstruction-controls, .selection-controls {
        text-align: center;
        margin-top: 1rem;
    }
    
    .reconstruction-controls .demo-btn, .selection-controls .demo-btn {
        margin: 0 0.5rem;
        padding: 0.5rem 1rem;
        background: #3498db;
        color: white;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        font-size: 0.9rem;
        transition: all 0.3s ease;
    }
    
    .reconstruction-controls .demo-btn:hover, .selection-controls .demo-btn:hover {
        background: #2980b9;
        transform: translateY(-2px);
    }
`;
document.head.appendChild(style);
