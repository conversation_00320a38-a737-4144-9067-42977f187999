<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Course Modules Overview - Medical Instrumentation</title>
    <link rel="stylesheet" href="../css/style.css">
    <link rel="stylesheet" href="modules-overview.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/gsap.min.js"></script>
</head>
<body>
    <header>
        <div class="container">
            <h1>Medical Instrumentation Course Modules</h1>
            <p class="subtitle">From Fundamentals to Sophisticated Applications</p>
            <div class="course-info">
                <p class="author">Course Author: <strong>Dr. <PERSON> Esmail</strong></p>
                <p class="institution">SUST - BME 2025</p>
            </div>
        </div>
    </header>

    <nav>
        <div class="container">
            <ul>
                <li><a href="../index.html">Home</a></li>
                <li><a href="#overview">Overview</a></li>
                <li><a href="#modules">All Modules</a></li>
                <li><a href="#progression">Learning Path</a></li>
                <li><a href="#resources">Resources</a></li>
            </ul>
        </div>
    </nav>

    <main>
        <section id="overview" class="section">
            <div class="container">
                <h2>🎓 Complete Course Journey</h2>
                <div class="course-journey-overview">
                    <p>This comprehensive course takes students on a complete journey from understanding the <strong>fundamental origins of physiological signals</strong> to mastering <strong>sophisticated capture, processing, and interpretation techniques</strong>. Each module builds upon previous knowledge while introducing advanced concepts through hands-on virtual laboratory experiences.</p>
                    
                    <div class="journey-visualization">
                        <h3>📈 Learning Progression Path</h3>
                        <div class="progression-timeline">
                            <div class="timeline-stage" data-stage="fundamentals">
                                <div class="stage-icon">🧬</div>
                                <h4>Fundamentals</h4>
                                <p>Signal Origins & Cellular Mechanisms</p>
                                <div class="stage-modules">Modules 1-2</div>
                            </div>
                            <div class="timeline-connector">→</div>
                            <div class="timeline-stage" data-stage="capture">
                                <div class="stage-icon">📡</div>
                                <h4>Capture Techniques</h4>
                                <p>Advanced Transduction & Acquisition</p>
                                <div class="stage-modules">Modules 2-3</div>
                            </div>
                            <div class="timeline-connector">→</div>
                            <div class="timeline-stage" data-stage="processing">
                                <div class="stage-icon">🧠</div>
                                <h4>Processing & Analysis</h4>
                                <p>Sophisticated Signal Processing</p>
                                <div class="stage-modules">Modules 4-5</div>
                            </div>
                            <div class="timeline-connector">→</div>
                            <div class="timeline-stage" data-stage="applications">
                                <div class="stage-icon">🏥</div>
                                <h4>Clinical Applications</h4>
                                <p>Medical Imaging & Systems</p>
                                <div class="stage-modules">Module 6</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <section id="modules" class="section">
            <div class="container">
                <h2>📚 Complete Module Collection</h2>
                <div class="modules-grid-overview">
                    
                    <!-- Module 1 -->
                    <div class="module-overview-card" data-module="1">
                        <div class="module-header">
                            <span class="module-number">Module 1</span>
                            <h3>Fundamentals of Medical Instrumentation</h3>
                            <div class="module-focus">🧬 Signal Origins</div>
                        </div>
                        <div class="module-preview">
                            <div class="animated-preview" id="module1-preview">
                                <div class="cell-animation">
                                    <div class="cell-membrane"></div>
                                    <div class="ion-flow"></div>
                                </div>
                            </div>
                        </div>
                        <div class="module-content">
                            <h4>Key Topics:</h4>
                            <ul>
                                <li>🧬 Cellular origins of bioelectric signals</li>
                                <li>⚡ Action potential generation & propagation</li>
                                <li>🔬 From single cells to organ systems</li>
                                <li>🛡️ Safety standards & regulations</li>
                            </ul>
                            <div class="module-stats">
                                <span class="duration">Duration: 2 weeks</span>
                                <span class="labs">4 Virtual Labs</span>
                                <span class="assessments">3 Assessments</span>
                            </div>
                        </div>
                        <div class="module-actions">
                            <a href="module1/module1-description.html" class="btn-primary">📖 Enter Module</a>
                            <button class="btn-secondary" onclick="previewModule(1)">👁️ Preview</button>
                        </div>
                    </div>

                    <!-- Module 2 -->
                    <div class="module-overview-card" data-module="2">
                        <div class="module-header">
                            <span class="module-number">Module 2</span>
                            <h3>Biomedical Signal Acquisition</h3>
                            <div class="module-focus">📡 Capture Techniques</div>
                        </div>
                        <div class="module-preview">
                            <div class="animated-preview" id="module2-preview">
                                <div class="transducer-animation">
                                    <div class="signal-input">📡</div>
                                    <div class="transducer">🔄</div>
                                    <div class="signal-output">⚡</div>
                                </div>
                            </div>
                        </div>
                        <div class="module-content">
                            <h4>Key Topics:</h4>
                            <ul>
                                <li>🎯 Advanced transduction principles</li>
                                <li>🔬 Sophisticated sensor technologies</li>
                                <li>⚡ Precision signal conditioning</li>
                                <li>💻 High-resolution ADC systems</li>
                            </ul>
                            <div class="module-stats">
                                <span class="duration">Duration: 3 weeks</span>
                                <span class="labs">6 Virtual Labs</span>
                                <span class="assessments">4 Assessments</span>
                            </div>
                        </div>
                        <div class="module-actions">
                            <a href="module2/module2-description.html" class="btn-primary">📖 Enter Module</a>
                            <button class="btn-secondary" onclick="previewModule(2)">👁️ Preview</button>
                        </div>
                    </div>

                    <!-- Module 3 -->
                    <div class="module-overview-card" data-module="3">
                        <div class="module-header">
                            <span class="module-number">Module 3</span>
                            <h3>ECG & Cardiovascular Monitoring</h3>
                            <div class="module-focus">💓 Cardiac Systems</div>
                        </div>
                        <div class="module-preview">
                            <div class="animated-preview" id="module3-preview">
                                <div class="ecg-animation">
                                    <canvas id="ecgPreview" width="200" height="100"></canvas>
                                </div>
                            </div>
                        </div>
                        <div class="module-content">
                            <h4>Key Topics:</h4>
                            <ul>
                                <li>💓 Cardiac electrophysiology & signal path</li>
                                <li>🔬 Advanced multi-lead acquisition</li>
                                <li>🧠 AI-powered arrhythmia detection</li>
                                <li>📊 Sophisticated processing algorithms</li>
                            </ul>
                            <div class="module-stats">
                                <span class="duration">Duration: 3 weeks</span>
                                <span class="labs">5 Virtual Labs</span>
                                <span class="assessments">4 Assessments</span>
                            </div>
                        </div>
                        <div class="module-actions">
                            <button class="btn-primary" onclick="comingSoon(3)">📖 Coming Soon</button>
                            <button class="btn-secondary" onclick="previewModule(3)">👁️ Preview</button>
                        </div>
                    </div>

                    <!-- Module 4 -->
                    <div class="module-overview-card" data-module="4">
                        <div class="module-header">
                            <span class="module-number">Module 4</span>
                            <h3>EMG & Neuromuscular Systems</h3>
                            <div class="module-focus">💪 Muscle Systems</div>
                        </div>
                        <div class="module-preview">
                            <div class="animated-preview" id="module4-preview">
                                <div class="emg-animation">
                                    <div class="muscle-fiber"></div>
                                    <div class="emg-signal"></div>
                                </div>
                            </div>
                        </div>
                        <div class="module-content">
                            <h4>Key Topics:</h4>
                            <ul>
                                <li>💪 Muscle physiology & EMG generation</li>
                                <li>🔬 Surface vs. intramuscular recording</li>
                                <li>📊 Advanced signal processing</li>
                                <li>🚶 Gait analysis & movement assessment</li>
                            </ul>
                            <div class="module-stats">
                                <span class="duration">Duration: 3 weeks</span>
                                <span class="labs">5 Virtual Labs</span>
                                <span class="assessments">4 Assessments</span>
                            </div>
                        </div>
                        <div class="module-actions">
                            <button class="btn-primary" onclick="comingSoon(4)">📖 Coming Soon</button>
                            <button class="btn-secondary" onclick="previewModule(4)">👁️ Preview</button>
                        </div>
                    </div>

                    <!-- Module 5 -->
                    <div class="module-overview-card" data-module="5">
                        <div class="module-header">
                            <span class="module-number">Module 5</span>
                            <h3>EEG & Neurological Monitoring</h3>
                            <div class="module-focus">🧠 Neural Systems</div>
                        </div>
                        <div class="module-preview">
                            <div class="animated-preview" id="module5-preview">
                                <div class="eeg-animation">
                                    <div class="brain-waves"></div>
                                    <div class="electrode-array"></div>
                                </div>
                            </div>
                        </div>
                        <div class="module-content">
                            <h4>Key Topics:</h4>
                            <ul>
                                <li>🧠 Brain electrophysiology</li>
                                <li>🔬 Advanced electrode systems</li>
                                <li>📊 Frequency domain analysis</li>
                                <li>🤖 Brain-computer interfaces</li>
                            </ul>
                            <div class="module-stats">
                                <span class="duration">Duration: 2 weeks</span>
                                <span class="labs">4 Virtual Labs</span>
                                <span class="assessments">3 Assessments</span>
                            </div>
                        </div>
                        <div class="module-actions">
                            <button class="btn-primary" onclick="comingSoon(5)">📖 Coming Soon</button>
                            <button class="btn-secondary" onclick="previewModule(5)">👁️ Preview</button>
                        </div>
                    </div>

                    <!-- Module 6 -->
                    <div class="module-overview-card" data-module="6">
                        <div class="module-header">
                            <span class="module-number">Module 6</span>
                            <h3>Medical Imaging Instrumentation</h3>
                            <div class="module-focus">🏥 Imaging Systems</div>
                        </div>
                        <div class="module-preview">
                            <div class="animated-preview" id="module6-preview">
                                <div class="imaging-animation">
                                    <div class="xray-source"></div>
                                    <div class="patient-phantom"></div>
                                    <div class="detector"></div>
                                </div>
                            </div>
                        </div>
                        <div class="module-content">
                            <h4>Key Topics:</h4>
                            <ul>
                                <li>📡 X-ray & CT imaging systems</li>
                                <li>🌊 Ultrasound principles</li>
                                <li>🧲 MRI & nuclear medicine</li>
                                <li>🖼️ Image processing & enhancement</li>
                            </ul>
                            <div class="module-stats">
                                <span class="duration">Duration: 3 weeks</span>
                                <span class="labs">6 Virtual Labs</span>
                                <span class="assessments">4 Assessments</span>
                            </div>
                        </div>
                        <div class="module-actions">
                            <button class="btn-primary" onclick="comingSoon(6)">📖 Coming Soon</button>
                            <button class="btn-secondary" onclick="previewModule(6)">👁️ Preview</button>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <section id="progression" class="section">
            <div class="container">
                <h2>🎯 Learning Progression & Prerequisites</h2>
                
                <div class="progression-map">
                    <h3>📊 Module Dependencies & Flow</h3>
                    <div class="dependency-diagram">
                        <div class="dependency-level" data-level="1">
                            <div class="level-title">Foundation Level</div>
                            <div class="module-node" data-module="1">
                                <span class="node-number">1</span>
                                <span class="node-title">Fundamentals</span>
                            </div>
                        </div>
                        
                        <div class="dependency-level" data-level="2">
                            <div class="level-title">Acquisition Level</div>
                            <div class="module-node" data-module="2">
                                <span class="node-number">2</span>
                                <span class="node-title">Signal Acquisition</span>
                            </div>
                        </div>
                        
                        <div class="dependency-level" data-level="3">
                            <div class="level-title">Application Level</div>
                            <div class="module-node" data-module="3">
                                <span class="node-number">3</span>
                                <span class="node-title">ECG Systems</span>
                            </div>
                            <div class="module-node" data-module="4">
                                <span class="node-number">4</span>
                                <span class="node-title">EMG Systems</span>
                            </div>
                            <div class="module-node" data-module="5">
                                <span class="node-number">5</span>
                                <span class="node-title">EEG Systems</span>
                            </div>
                        </div>
                        
                        <div class="dependency-level" data-level="4">
                            <div class="level-title">Advanced Level</div>
                            <div class="module-node" data-module="6">
                                <span class="node-number">6</span>
                                <span class="node-title">Medical Imaging</span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="learning-outcomes-summary">
                    <h3>🎓 Overall Learning Outcomes</h3>
                    <div class="outcomes-grid">
                        <div class="outcome-category">
                            <h4>🧬 Fundamental Understanding</h4>
                            <ul>
                                <li>Trace physiological signal origins from cellular to organ level</li>
                                <li>Understand bioelectric, biomechanical, and biochemical signals</li>
                                <li>Apply safety standards and regulatory requirements</li>
                            </ul>
                        </div>
                        
                        <div class="outcome-category">
                            <h4>📡 Technical Mastery</h4>
                            <ul>
                                <li>Design sophisticated signal acquisition systems</li>
                                <li>Implement advanced signal conditioning techniques</li>
                                <li>Optimize ADC performance for biomedical applications</li>
                            </ul>
                        </div>
                        
                        <div class="outcome-category">
                            <h4>🧠 Advanced Processing</h4>
                            <ul>
                                <li>Apply machine learning to biomedical signal analysis</li>
                                <li>Develop real-time processing algorithms</li>
                                <li>Create intelligent diagnostic systems</li>
                            </ul>
                        </div>
                        
                        <div class="outcome-category">
                            <h4>🏥 Clinical Application</h4>
                            <ul>
                                <li>Design complete medical instrumentation systems</li>
                                <li>Integrate multiple signal modalities</li>
                                <li>Develop next-generation medical devices</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <section id="resources" class="section">
            <div class="container">
                <h2>📚 Course Resources & Support</h2>
                
                <div class="resources-overview">
                    <div class="resource-category">
                        <h3>🔬 Virtual Laboratory</h3>
                        <ul>
                            <li>24 Interactive simulation experiments</li>
                            <li>Real-time signal processing tools</li>
                            <li>Virtual instrumentation suite</li>
                            <li>Circuit design and analysis tools</li>
                        </ul>
                    </div>
                    
                    <div class="resource-category">
                        <h3>📖 Learning Materials</h3>
                        <ul>
                            <li>Interactive lecture slides with animations</li>
                            <li>Comprehensive module documentation</li>
                            <li>Video demonstrations and tutorials</li>
                            <li>Downloadable reference materials</li>
                        </ul>
                    </div>
                    
                    <div class="resource-category">
                        <h3>💻 Software Tools</h3>
                        <ul>
                            <li>MATLAB Signal Processing Toolbox</li>
                            <li>Python biomedical libraries</li>
                            <li>Circuit simulation software</li>
                            <li>Data analysis templates</li>
                        </ul>
                    </div>
                    
                    <div class="resource-category">
                        <h3>🎯 Assessment Tools</h3>
                        <ul>
                            <li>Interactive quizzes and tests</li>
                            <li>Virtual lab report templates</li>
                            <li>Project guidelines and rubrics</li>
                            <li>Peer review systems</li>
                        </ul>
                    </div>
                </div>
                
                <div class="progress-tracking">
                    <h3>📊 Progress Tracking</h3>
                    <div class="progress-dashboard">
                        <div class="progress-item">
                            <h4>Overall Progress</h4>
                            <div class="progress-bar">
                                <div class="progress-fill" style="width: 0%"></div>
                            </div>
                            <span class="progress-text">0% Complete</span>
                        </div>
                        
                        <div class="progress-item">
                            <h4>Virtual Labs Completed</h4>
                            <div class="progress-bar">
                                <div class="progress-fill" style="width: 0%"></div>
                            </div>
                            <span class="progress-text">0 of 24 Labs</span>
                        </div>
                        
                        <div class="progress-item">
                            <h4>Assessments Passed</h4>
                            <div class="progress-bar">
                                <div class="progress-fill" style="width: 0%"></div>
                            </div>
                            <span class="progress-text">0 of 22 Assessments</span>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <footer>
        <div class="container">
            <div class="footer-content">
                <div class="author-info">
                    <h3>Course Author</h3>
                    <p><strong>Dr. Mohammed Yagoub Esmail</strong></p>
                    <p>Sudan University of Science and Technology (SUST)</p>
                    <p>Biomedical Engineering Department</p>
                </div>
                
                <div class="contact-info">
                    <h3>Contact Information</h3>
                    <p>📧 Email: <a href="mailto:<EMAIL>"><EMAIL></a></p>
                    <p>📱 Phone: +249912867327, +966538076790</p>
                </div>
            </div>
            
            <div class="copyright">
                <p>&copy; 2025 Dr. Mohammed Yagoub Esmail - SUST BME</p>
                <p>All rights reserved. Email: <EMAIL></p>
            </div>
        </div>
    </footer>

    <script src="modules-overview.js"></script>
</body>
</html>
