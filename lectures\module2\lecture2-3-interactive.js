// Lecture 2.3 Interactive Elements - Signal Conditioning

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    console.log('Lecture 2.3 interactive elements loading...');
    
    // Initialize all interactive elements
    initializeConditioningVisualizations();
    initializeGainCalculator();
    initializeFilterResponse();
    initializeIsolationComparison();
    
    console.log('Lecture 2.3 interactive elements loaded successfully');
});

// Initialize signal conditioning visualizations
function initializeConditioningVisualizations() {
    // Animate signal chain
    const chainSteps = document.querySelectorAll('.chain-step');
    const arrows = document.querySelectorAll('.arrow');
    
    // Animate chain sequence
    chainSteps.forEach((step, index) => {
        setTimeout(() => {
            if (typeof gsap !== 'undefined') {
                gsap.from(step, {
                    scale: 0,
                    duration: 0.8,
                    ease: "back.out(1.7)"
                });
            }
        }, index * 200);
    });
    
    arrows.forEach((arrow, index) => {
        setTimeout(() => {
            if (typeof gsap !== 'undefined') {
                gsap.from(arrow, {
                    x: -20,
                    opacity: 0,
                    duration: 0.5,
                    ease: "power2.out"
                });
            }
        }, (index + 1) * 200 + 100);
    });
    
    // Add hover effects to requirement items
    const requirementItems = document.querySelectorAll('.requirement-item');
    requirementItems.forEach(item => {
        item.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-3px)';
            this.style.boxShadow = '0 6px 12px rgba(0,0,0,0.15)';
        });
        
        item.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
            this.style.boxShadow = '0 4px 8px rgba(0,0,0,0.1)';
        });
    });
}

// Initialize gain calculator
function initializeGainCalculator() {
    // Set up initial calculation
    calculateGain();
    
    // Add event listeners to inputs
    const inputs = ['r1Input', 'r2Input', 'rgInput'];
    inputs.forEach(inputId => {
        const input = document.getElementById(inputId);
        if (input) {
            input.addEventListener('input', calculateGain);
        }
    });
}

function calculateGain() {
    const r1 = parseFloat(document.getElementById('r1Input')?.value) || 10;
    const r2 = parseFloat(document.getElementById('r2Input')?.value) || 10;
    const rg = parseFloat(document.getElementById('rgInput')?.value) || 1000;
    
    // Instrumentation amplifier gain formula
    const firstStageGain = 1 + (2 * r2 * 1000) / rg; // Convert kΩ to Ω
    const secondStageGain = r2 / r1;
    const totalGain = firstStageGain * secondStageGain;
    
    const resultElement = document.querySelector('.result-value');
    if (resultElement) {
        resultElement.textContent = totalGain.toFixed(1);
        
        // Add visual feedback
        if (typeof gsap !== 'undefined') {
            gsap.from(resultElement, {
                scale: 1.2,
                duration: 0.3,
                ease: "back.out(1.7)"
            });
        }
    }
}

// Initialize filter response
function initializeFilterResponse() {
    const canvas = document.getElementById('filterResponseCanvas');
    if (!canvas) return;
    
    const ctx = canvas.getContext('2d');
    
    // Initial plot - low-pass filter
    drawFilterResponse(ctx, canvas.width, canvas.height, 'lowpass', 100, 2);
}

function drawFilterResponse(ctx, width, height, filterType, cutoff, order) {
    ctx.clearRect(0, 0, width, height);
    
    // Draw background
    ctx.fillStyle = '#f8f9fa';
    ctx.fillRect(0, 0, width, height);
    
    // Draw axes
    const margin = 40;
    const plotWidth = width - 2 * margin;
    const plotHeight = height - 2 * margin;
    
    ctx.strokeStyle = '#2c3e50';
    ctx.lineWidth = 2;
    ctx.beginPath();
    ctx.moveTo(margin, height - margin);
    ctx.lineTo(width - margin, height - margin);
    ctx.moveTo(margin, margin);
    ctx.lineTo(margin, height - margin);
    ctx.stroke();
    
    // Draw frequency response
    ctx.strokeStyle = '#e74c3c';
    ctx.lineWidth = 3;
    ctx.beginPath();
    
    for (let x = 0; x <= plotWidth; x += 2) {
        const freq = Math.pow(10, x / plotWidth * 3); // 1 Hz to 1 kHz
        let magnitude = calculateFilterMagnitude(freq, cutoff, order, filterType);
        
        // Convert to dB and normalize
        const magnitudeDB = 20 * Math.log10(Math.abs(magnitude));
        const y = margin + plotHeight * (1 - (magnitudeDB + 60) / 60); // Scale for -60 to 0 dB
        
        if (x === 0) {
            ctx.moveTo(margin + x, Math.max(margin, Math.min(height - margin, y)));
        } else {
            ctx.lineTo(margin + x, Math.max(margin, Math.min(height - margin, y)));
        }
    }
    ctx.stroke();
    
    // Draw cutoff frequency line
    const cutoffX = margin + plotWidth * Math.log10(cutoff) / 3;
    ctx.strokeStyle = '#f39c12';
    ctx.setLineDash([5, 5]);
    ctx.beginPath();
    ctx.moveTo(cutoffX, margin);
    ctx.lineTo(cutoffX, height - margin);
    ctx.stroke();
    ctx.setLineDash([]);
    
    // Add labels
    ctx.fillStyle = '#2c3e50';
    ctx.font = '12px Arial';
    ctx.fillText('Frequency [Hz]', width - 80, height - 10);
    ctx.save();
    ctx.translate(15, height / 2);
    ctx.rotate(-Math.PI / 2);
    ctx.fillText('Magnitude [dB]', 0, 0);
    ctx.restore();
    
    // Add cutoff label
    ctx.fillStyle = '#f39c12';
    ctx.font = '10px Arial';
    ctx.fillText(`fc = ${cutoff} Hz`, cutoffX - 20, margin + 15);
}

function calculateFilterMagnitude(freq, cutoff, order, filterType) {
    const s = freq / cutoff; // Normalized frequency
    
    switch(filterType) {
        case 'lowpass':
            return 1 / Math.pow(Math.sqrt(1 + Math.pow(s, 2 * order)), 1);
        case 'highpass':
            return Math.pow(s, order) / Math.pow(Math.sqrt(1 + Math.pow(s, 2 * order)), 1);
        case 'bandpass':
            const Q = 5; // Quality factor
            const denominator = Math.sqrt(Math.pow(1 - s * s, 2) + Math.pow(s / Q, 2));
            return (s / Q) / denominator;
        case 'notch':
            const Qn = 10; // Notch quality factor
            const numerator = Math.sqrt(Math.pow(1 - s * s, 2));
            const denominatorN = Math.sqrt(Math.pow(1 - s * s, 2) + Math.pow(s / Qn, 2));
            return numerator / denominatorN;
        default:
            return 1;
    }
}

// Initialize isolation comparison
function initializeIsolationComparison() {
    const canvas = document.getElementById('isolationCanvas');
    if (!canvas) return;
    
    const ctx = canvas.getContext('2d');
    
    // Initial plot - bandwidth comparison
    drawIsolationComparison(ctx, canvas.width, canvas.height, 'bandwidth');
}

function drawIsolationComparison(ctx, width, height, metric) {
    ctx.clearRect(0, 0, width, height);
    
    // Draw background
    ctx.fillStyle = '#f8f9fa';
    ctx.fillRect(0, 0, width, height);
    
    const margin = 40;
    const plotWidth = width - 2 * margin;
    const plotHeight = height - 2 * margin;
    
    // Data for different isolation methods
    const isolationData = {
        bandwidth: {
            optical: 10, // MHz
            magnetic: 1, // MHz
            capacitive: 100 // MHz
        },
        voltage: {
            optical: 5000, // V
            magnetic: 2500, // V
            capacitive: 5000 // V
        },
        cost: {
            optical: 3, // Relative scale 1-5
            magnetic: 2,
            capacitive: 4
        }
    };
    
    const data = isolationData[metric];
    const methods = Object.keys(data);
    const values = Object.values(data);
    const maxValue = Math.max(...values);
    
    // Draw bars
    const barWidth = plotWidth / (methods.length * 2);
    const colors = ['#3498db', '#e74c3c', '#27ae60'];
    
    methods.forEach((method, index) => {
        const barHeight = (data[method] / maxValue) * plotHeight * 0.8;
        const x = margin + (index + 0.5) * (plotWidth / methods.length);
        const y = height - margin - barHeight;
        
        ctx.fillStyle = colors[index];
        ctx.fillRect(x - barWidth/2, y, barWidth, barHeight);
        
        // Add value labels
        ctx.fillStyle = '#2c3e50';
        ctx.font = '12px Arial';
        ctx.textAlign = 'center';
        ctx.fillText(data[method].toString(), x, y - 5);
        
        // Add method labels
        ctx.fillText(method.charAt(0).toUpperCase() + method.slice(1), x, height - margin + 20);
    });
    
    // Add title and units
    ctx.fillStyle = '#2c3e50';
    ctx.font = 'bold 14px Arial';
    ctx.textAlign = 'center';
    const units = metric === 'bandwidth' ? 'MHz' : metric === 'voltage' ? 'V' : 'Relative';
    ctx.fillText(`${metric.charAt(0).toUpperCase() + metric.slice(1)} Comparison (${units})`, width/2, margin/2);
}

// Interactive functions

function updateFilterResponse() {
    const filterType = document.getElementById('filterType')?.value || 'lowpass';
    const cutoffFreq = parseFloat(document.getElementById('cutoffFreq')?.value) || 100;
    const filterOrder = parseInt(document.getElementById('filterOrder')?.value) || 2;
    
    const canvas = document.getElementById('filterResponseCanvas');
    if (!canvas) return;
    
    const ctx = canvas.getContext('2d');
    drawFilterResponse(ctx, canvas.width, canvas.height, filterType, cutoffFreq, filterOrder);
    
    // Add visual feedback
    if (typeof gsap !== 'undefined') {
        gsap.from(canvas, {
            scale: 1.05,
            duration: 0.3,
            ease: "back.out(1.7)"
        });
    }
}

function showIsolationBandwidth() {
    const canvas = document.getElementById('isolationCanvas');
    if (!canvas) return;
    
    const ctx = canvas.getContext('2d');
    drawIsolationComparison(ctx, canvas.width, canvas.height, 'bandwidth');
}

function showIsolationVoltage() {
    const canvas = document.getElementById('isolationCanvas');
    if (!canvas) return;
    
    const ctx = canvas.getContext('2d');
    drawIsolationComparison(ctx, canvas.width, canvas.height, 'voltage');
}

function showIsolationCost() {
    const canvas = document.getElementById('isolationCanvas');
    if (!canvas) return;
    
    const ctx = canvas.getContext('2d');
    drawIsolationComparison(ctx, canvas.width, canvas.height, 'cost');
}

// Add CSS for interactive elements
const style = document.createElement('style');
style.textContent = `
    .signal-chain {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 1rem;
        margin: 2rem 0;
        flex-wrap: wrap;
    }
    
    .chain-step {
        background: linear-gradient(135deg, #3498db, #2980b9);
        color: white;
        padding: 1rem 1.5rem;
        border-radius: 8px;
        font-weight: bold;
        text-align: center;
        min-width: 100px;
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        transition: all 0.3s ease;
    }
    
    .chain-step:hover {
        transform: translateY(-3px);
        box-shadow: 0 6px 12px rgba(0,0,0,0.15);
    }
    
    .arrow {
        font-size: 1.5rem;
        color: #e74c3c;
        font-weight: bold;
    }
    
    .requirement-item {
        background: white;
        border-radius: 8px;
        padding: 1.5rem;
        margin: 1rem 0;
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        transition: all 0.3s ease;
        border-left: 4px solid #3498db;
    }
    
    .requirement-item h4 {
        color: #2c3e50;
        margin-bottom: 0.5rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }
    
    .requirement-item p {
        color: #7f8c8d;
        line-height: 1.5;
        margin: 0;
    }
    
    .circuit-diagram {
        background: white;
        border-radius: 8px;
        padding: 1rem;
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        margin: 1rem 0;
        text-align: center;
    }
    
    .gain-calculator {
        background: #f8f9fa;
        border-radius: 8px;
        padding: 1.5rem;
        margin: 1rem 0;
        border-left: 4px solid #27ae60;
    }
    
    .calculator-inputs {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
        gap: 1rem;
        margin-bottom: 1rem;
    }
    
    .input-group {
        display: flex;
        flex-direction: column;
        gap: 0.5rem;
    }
    
    .input-group label {
        font-weight: bold;
        color: #2c3e50;
        font-size: 0.9rem;
    }
    
    .input-group input {
        padding: 0.5rem;
        border: 2px solid #bdc3c7;
        border-radius: 4px;
        font-size: 1rem;
    }
    
    .input-group input:focus {
        border-color: #3498db;
        outline: none;
    }
    
    .gain-result {
        background: white;
        border-radius: 6px;
        padding: 1rem;
        text-align: center;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    
    .result-label {
        font-weight: bold;
        color: #2c3e50;
        margin-right: 1rem;
    }
    
    .result-value {
        font-size: 1.5rem;
        font-weight: bold;
        color: #27ae60;
    }
    
    .filter-category {
        background: white;
        border-radius: 8px;
        padding: 1.5rem;
        margin: 1rem 0;
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        border-left: 4px solid #e74c3c;
    }
    
    .filter-category h4 {
        color: #2c3e50;
        margin-bottom: 1rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }
    
    .filter-details p {
        margin: 0.5rem 0;
        color: #7f8c8d;
    }
    
    .filter-specs {
        display: flex;
        gap: 0.5rem;
        margin-top: 1rem;
        flex-wrap: wrap;
    }
    
    .spec-tag {
        background: #e8f5e8;
        color: #27ae60;
        padding: 0.25rem 0.5rem;
        border-radius: 4px;
        font-size: 0.8rem;
        font-weight: bold;
    }
    
    .filter-controls {
        background: #f8f9fa;
        border-radius: 8px;
        padding: 1rem;
        margin: 1rem 0;
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 1rem;
        align-items: end;
    }
    
    .control-group {
        display: flex;
        flex-direction: column;
        gap: 0.5rem;
    }
    
    .control-group label {
        font-weight: bold;
        color: #2c3e50;
        font-size: 0.9rem;
    }
    
    .control-group select, .control-group input {
        padding: 0.5rem;
        border: 2px solid #bdc3c7;
        border-radius: 4px;
        font-size: 1rem;
    }
    
    .safety-item {
        background: #f8f9fa;
        border-radius: 6px;
        padding: 1rem;
        margin: 1rem 0;
        border-left: 4px solid #e74c3c;
    }
    
    .safety-item h4 {
        color: #e74c3c;
        margin-bottom: 0.5rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }
    
    .isolation-type {
        background: white;
        border-radius: 8px;
        padding: 1.5rem;
        margin: 1rem 0;
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        border-left: 4px solid #9b59b6;
    }
    
    .isolation-type h4 {
        color: #2c3e50;
        margin-bottom: 1rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }
    
    .method-details {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 1rem;
    }
    
    .principle, .characteristics {
        background: #f8f9fa;
        border-radius: 6px;
        padding: 1rem;
    }
    
    .principle h5, .characteristics h5 {
        color: #9b59b6;
        margin-bottom: 0.5rem;
        border-bottom: 2px solid #9b59b6;
        padding-bottom: 0.25rem;
    }
    
    .characteristics ul {
        list-style-type: none;
        padding-left: 0;
        margin: 0;
    }
    
    .characteristics li {
        padding: 0.25rem 0;
        padding-left: 1rem;
        position: relative;
    }
    
    .characteristics li:before {
        content: "•";
        position: absolute;
        left: 0;
        color: #9b59b6;
        font-weight: bold;
    }
    
    .comparison-controls {
        text-align: center;
        margin-top: 1rem;
    }
    
    .comparison-controls .demo-btn {
        margin: 0 0.5rem;
        padding: 0.5rem 1rem;
        background: #9b59b6;
        color: white;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        font-size: 0.9rem;
        transition: all 0.3s ease;
    }
    
    .comparison-controls .demo-btn:hover {
        background: #8e44ad;
        transform: translateY(-2px);
    }
`;
document.head.appendChild(style);
