/* Lecture Slides Styling - Copied from Module 1 and adapted for Module 3 */

/* Base Lecture Mode */
.lecture-mode {
    background: linear-gradient(135deg, #f5f7fa, #c3cfe2);
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    margin: 0;
    padding: 0;
    overflow-x: hidden;
    min-height: 100vh;
}

/* Lecture Header */
.lecture-header {
    background: linear-gradient(135deg, #2c3e50, #34495e);
    color: white;
    padding: 1rem 0;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.lecture-navigation {
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-wrap: wrap;
    gap: 1rem;
}

.nav-btn {
    background: rgba(255,255,255,0.1);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 6px;
    text-decoration: none;
    transition: all 0.3s ease;
    font-size: 0.9rem;
}

.nav-btn:hover {
    background: rgba(255,255,255,0.2);
    transform: translateY(-2px);
}

.lecture-info {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
}

.lecture-title {
    font-weight: bold;
    font-size: 1.1rem;
    margin-bottom: 0.25rem;
}

.slide-counter {
    font-size: 0.9rem;
    opacity: 0.8;
}

.lecture-controls {
    display: flex;
    gap: 0.5rem;
}

.control-btn {
    background: #e74c3c; /* Module 3 Accent Color (Red for Cardiac) */
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.9rem;
}

.control-btn:hover {
    background: #c0392b; /* Darker red */
    transform: translateY(-2px);
}

.control-btn:disabled {
    background: #95a5a6;
    cursor: not-allowed;
    transform: none;
}

/* Main Lecture Content */
.lecture-content {
    margin-top: 100px; /* Adjusted for fixed header */
    min-height: calc(100vh - 150px); /* Header + Footer height */
    padding: 2rem 0;
}

.slides-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
}

/* Individual Slides */
.slide {
    display: none;
    background: white;
    border-radius: 12px;
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
    padding: 3rem;
    margin-bottom: 2rem;
    min-height: 600px; /* Ensure slides have enough height */
    animation: slideIn 0.5s ease-in-out;
}

.slide.active {
    display: block;
}

.slide-content h2 {
    color: #c0392b; /* Module 3 Accent Color */
    border-bottom: 3px solid #e74c3c; /* Module 3 Accent Color */
    padding-bottom: 1rem;
    margin-bottom: 2rem;
    font-size: 2rem;
}

/* Title Slide */
.title-slide {
    text-align: center;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    min-height: 500px;
}

.title-slide h1 {
    font-size: 3rem;
    color: #c0392b; /* Module 3 Accent Color */
    margin-bottom: 1rem;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
}

.title-slide h2 {
    font-size: 1.5rem;
    color: #e74c3c; /* Module 3 Accent Color */
    margin-bottom: 2rem;
    border: none;
}

.title-info {
    background: linear-gradient(135deg, #e74c3c, #c0392b); /* Module 3 Accent Gradient */
    color: white;
    padding: 1.5rem;
    border-radius: 8px;
    margin-bottom: 2rem;
}

.author-info { /* Shared style, keep as is or make subtle M3 tweak if needed */
    background: #f8f9fa;
    padding: 1.5rem;
    border-radius: 8px;
    color: #2c3e50;
}

.animated-cardiac-intro { /* Placeholder for Module 3 intro animation */
    width: 250px;
    height: 200px;
    position: relative;
    margin-top: 2rem;
    border: 2px dashed #e74c3c;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Learning Objectives */
.objectives-list {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.objective-item {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    padding: 1.5rem;
    background: #fff0f0; /* Light red background for M3 */
    border-radius: 8px;
    border-left: 4px solid #e74c3c; /* Module 3 Accent Color */
    transition: all 0.3s ease;
    opacity: 0;
    transform: translateY(20px);
}

.objective-item.animate { /* For JS-driven animation */
    opacity: 1;
    transform: translateY(0);
}

.objective-item:hover {
    background: #ffe0e0; /* Slightly darker red on hover */
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.objective-icon {
    font-size: 2rem;
    min-width: 50px;
    color: #c0392b; /* Module 3 Accent Color */
}

.objective-text h3 {
    color: #c0392b; /* Module 3 Accent Color */
    margin: 0 0 0.5rem 0;
    font-size: 1.1rem;
}

.objective-text p {
    color: #581818; /* Darker red text for readability */
    margin: 0;
    line-height: 1.5;
}

/* Content Grid Layout */
.content-grid {
    display: grid;
    grid-template-columns: 1fr 1fr; /* Default two columns */
    gap: 3rem;
    margin-top: 2rem;
}

.content-grid.single-column { /* For slides needing full width */
    grid-template-columns: 1fr;
}

.content-left, .content-right {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

/* Diagrams and Visualizations */
.diagram-container {
    background: #f8f9fa;
    padding: 1.5rem;
    border-radius: 8px;
    text-align: center;
    border: 1px solid #ddd;
    box-shadow: 0 2px 5px rgba(0,0,0,0.05);
}
.diagram-container img {
    max-width: 100%;
    height: auto;
    border-radius: 4px;
}
.caption {
    font-size: 0.9rem;
    color: #555;
    margin-top: 0.5rem;
    font-style: italic;
}

.interactive-icons-info {
    background-color: #fff5f5;
    padding: 10px;
    border-radius: 5px;
    border: 1px solid #e74c3c;
}

.interactive-icon {
    display: inline-block;
    background-color: #e74c3c;
    color: white;
    padding: 5px 10px;
    margin: 5px;
    border-radius: 15px;
    font-size: 0.9em;
    cursor: pointer;
    transition: background-color 0.3s;
}
.interactive-icon:hover, .interactive-icon.highlighted {
    background-color: #c0392b;
}


/* Key Concepts & Factors */
.key-concepts, .rmp-factors, .channel-types { /* Shared structure */
    background: white;
    padding: 2rem;
    border-radius: 8px;
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}
.key-concepts h3, .rmp-factors h3, .channel-types h3 {
    color: #c0392b;
    margin-top: 0;
}

.concept-item, .factor-item, .channel-type { /* Shared structure */
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #ffe0e0; /* Light red separator */
}
.concept-item:last-child, .factor-item:last-child, .channel-type:last-child {
    border-bottom: none;
    margin-bottom: 0;
}
.concept-item h4, .factor-item h4, .channel-type h4 {
    color: #e74c3c; /* Module 3 Accent Color */
    margin: 0 0 0.5rem 0;
}
.concept-item p, .factor-item p, .channel-properties p {
    color: #333;
    margin: 0.2rem 0;
    line-height: 1.6;
}

/* Tables (if any for M3, keep generic for now) */
.data-table {
    width: 100%;
    border-collapse: collapse;
    background: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    margin-top: 1rem;
}
.data-table th {
    background: #e74c3c; /* Module 3 Accent Color */
    color: white;
    padding: 1rem;
    text-align: left;
    font-weight: bold;
}
.data-table td {
    padding: 1rem;
    border-bottom: 1px solid #ffe0e0; /* Light red separator */
}
.data-table tr:hover {
    background: #fff5f5; /* Very light red on hover */
}

/* Equations (if any for M3, keep generic for now) */
.equation-box {
    background: linear-gradient(135deg, #fff5f5, #ffe0e0); /* Light red gradient */
    padding: 2rem;
    border-radius: 8px;
    border-left: 4px solid #e74c3c; /* Module 3 Accent Color */
    margin: 1rem 0;
}
.equation-main {
    font-size: 1.2rem;
    font-weight: bold;
    color: #c0392b; /* Darker red */
    text-align: center;
    margin-bottom: 1rem;
    font-family: 'Courier New', monospace;
}

/* Interactive Elements & Demo Buttons */
.interactive-controls, .interactive-demo {
    background: #fff0f0; /* Light red background */
    padding: 2rem;
    border-radius: 8px;
    margin-top: 2rem;
    text-align: center;
}

.demo-btn {
    background: #e74c3c; /* Module 3 Accent Color */
    color: white;
    border: none;
    padding: 0.8rem 1.5rem;
    border-radius: 6px;
    cursor: pointer;
    margin: 0.5rem;
    transition: all 0.3s ease;
    font-weight: bold;
}
.demo-btn:hover {
    background: #c0392b; /* Darker red */
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(192, 57, 43, 0.3);
}

/* Summary Section */
.summary-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}
.summary-section {
    background: #fff5f5; /* Very light red */
    padding: 1.5rem;
    border-radius: 8px;
    border-left: 3px solid #e74c3c;
}
.summary-section h3 {
    color: #c0392b;
    font-size: 1.2rem;
    margin-top: 0;
    margin-bottom: 1rem;
}
.summary-section ul {
    list-style-type: disc;
    padding-left: 20px;
    color: #333;
}
.summary-section li {
    margin-bottom: 0.5rem;
}
.key-takeaway {
    background: #e74c3c;
    color: white;
    padding: 1.5rem;
    border-radius: 8px;
    text-align: center;
    font-size: 1.1rem;
    margin-top: 1rem;
}

/* Next Lecture / Button */
.next-lecture {
    text-align: center;
    margin-top: 2rem;
}
.next-lecture h3 {
    color: #c0392b;
}
.next-btn {
    display: inline-block;
    background: #2c3e50;
    color: white;
    padding: 0.8rem 2rem;
    border-radius: 6px;
    text-decoration: none;
    font-weight: bold;
    transition: background 0.3s ease;
}
.next-btn:hover {
    background: #34495e;
}

/* Lecture Footer */
.lecture-footer {
    background: #34495e;
    color: #ecf0f1;
    padding: 1.5rem 0;
    text-align: center;
    font-size: 0.9rem;
    margin-top: auto; /* Pushes footer to bottom if content is short */
}
.lecture-footer .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}
.lecture-footer .footer-content {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}


/* Animations */
@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes cardiacPulse { /* Example for cardiac intro */
    0% { transform: scale(1); opacity: 0.7; }
    50% { transform: scale(1.1); opacity: 1; }
    100% { transform: scale(1); opacity: 0.7; }
}

.animated-cardiac-intro .heart-icon { /* If using an icon */
    font-size: 100px;
    color: #e74c3c;
    animation: cardiacPulse 1.5s ease-in-out infinite;
}


/* Responsive adjustments */
@media (max-width: 992px) {
    .content-grid {
        grid-template-columns: 1fr; /* Stack columns on smaller screens */
    }
    .lecture-navigation {
        flex-direction: column;
        align-items: center;
    }
    .lecture-header {
        position: static; /* Or adjust padding-top of body */
    }
    .lecture-content {
        margin-top: 20px;
    }
}

@media (max-width: 768px) {
    .lecture-title {
        font-size: 1rem;
    }
    .control-btn, .nav-btn {
        padding: 0.4rem 0.8rem;
        font-size: 0.8rem;
    }
    .slide {
        padding: 2rem;
    }
    .slide-content h2 {
        font-size: 1.8rem;
    }
    .title-slide h1 {
        font-size: 2.5rem;
    }
}

@media (max-width: 480px) {
    .lecture-controls {
        flex-direction: column;
        width: 100%;
    }
    .control-btn {
        width: 100%;
        margin-bottom: 0.5rem;
    }
    .slide {
        padding: 1.5rem;
    }
    .slide-content h2 {
        font-size: 1.5rem;
    }
    .title-slide h1 {
        font-size: 2rem;
    }
}

/* Fullscreen Mode */
body.fullscreen-active .lecture-header,
body.fullscreen-active .lecture-footer {
    display: none;
}

body.fullscreen-active .lecture-content {
    margin-top: 0;
    padding: 0;
    height: 100vh;
    overflow-y: auto;
}

body.fullscreen-active .slides-container {
    max-width: none;
    height: 100%;
    padding: 0;
    margin: 0;
}

body.fullscreen-active .slide {
    height: 100%;
    border-radius: 0;
    margin-bottom: 0;
    overflow-y: auto;
    padding: 2rem; /* Adjust padding for fullscreen */
}