// Lecture Slides Navigation and Functionality - Adapted for Module 3

class LectureSlides {
    constructor() {
        this.currentSlide = 1;
        this.totalSlides = 0;
        this.slides = [];
        this.isFullscreen = false;

        this.init();
    }

    init() {
        this.slides = document.querySelectorAll('.slide');
        this.totalSlides = this.slides.length;

        const totalSlidesElement = document.getElementById('totalSlides');
        if (totalSlidesElement) {
            totalSlidesElement.textContent = this.totalSlides;
        }

        this.setupEventListeners();
        this.showSlide(1);
        this.setupKeyboardNavigation();
        this.initializeAnimations(); // GSAP registration

        console.log(`Module 3 Lecture slides initialized: ${this.totalSlides} slides`);
    }

    setupEventListeners() {
        document.getElementById('prevSlide').addEventListener('click', () => this.previousSlide());
        document.getElementById('nextSlide').addEventListener('click', () => this.nextSlide());
        document.getElementById('fullscreenBtn').addEventListener('click', () => this.toggleFullscreen());

        // Optional: click on slide to advance
        this.slides.forEach((slide) => {
            slide.addEventListener('click', (e) => {
                // Advance if clicking on the slide background itself, not interactive elements
                if (e.target === slide || e.target.classList.contains('slide-content')) {
                     // Check if click is not on a button or link
                    if (!e.target.closest('button, a, input, select, .interactive-icon')) {
                        this.nextSlide();
                    }
                }
            });
        });
    }

    setupKeyboardNavigation() {
        document.addEventListener('keydown', (e) => {
            switch(e.key) {
                case 'ArrowRight':
                case ' ':
                case 'PageDown':
                    e.preventDefault();
                    this.nextSlide();
                    break;
                case 'ArrowLeft':
                case 'PageUp':
                    e.preventDefault();
                    this.previousSlide();
                    break;
                case 'Home':
                    e.preventDefault();
                    this.showSlide(1);
                    break;
                case 'End':
                    e.preventDefault();
                    this.showSlide(this.totalSlides);
                    break;
                case 'F11':
                case 'f':
                    e.preventDefault();
                    this.toggleFullscreen();
                    break;
                case 'Escape':
                    if (this.isFullscreen) {
                        this.exitFullscreen();
                    }
                    break;
            }
        });
    }

    showSlide(slideNumber) {
        if (slideNumber < 1 || slideNumber > this.totalSlides) {
            return;
        }

        this.slides.forEach(slide => {
            slide.classList.remove('active');
        });

        const currentSlideElement = document.querySelector(`.slide[data-slide="${slideNumber}"]`);
        if (currentSlideElement) {
            currentSlideElement.classList.add('active');
            this.currentSlide = slideNumber;

            document.getElementById('currentSlide').textContent = slideNumber;
            this.updateNavigationButtons();
            this.triggerSlideAnimations(currentSlideElement);
            window.scrollTo(0, 0);
        }
    }

    nextSlide() {
        if (this.currentSlide < this.totalSlides) {
            this.showSlide(this.currentSlide + 1);
        }
    }

    previousSlide() {
        if (this.currentSlide > 1) {
            this.showSlide(this.currentSlide - 1);
        }
    }

    updateNavigationButtons() {
        const prevBtn = document.getElementById('prevSlide');
        const nextBtn = document.getElementById('nextSlide');
        prevBtn.disabled = this.currentSlide === 1;
        nextBtn.disabled = this.currentSlide === this.totalSlides;
    }

    toggleFullscreen() {
        if (!this.isFullscreen) {
            this.enterFullscreen();
        } else {
            this.exitFullscreen();
        }
    }

    enterFullscreen() {
        const element = document.documentElement;
        if (element.requestFullscreen) element.requestFullscreen();
        else if (element.mozRequestFullScreen) element.mozRequestFullScreen();
        else if (element.webkitRequestFullscreen) element.webkitRequestFullscreen();
        else if (element.msRequestFullscreen) element.msRequestFullscreen();

        document.body.classList.add('fullscreen-active');
        this.isFullscreen = true;
        document.getElementById('fullscreenBtn').textContent = 'Exit Fullscreen';
    }

    exitFullscreen() {
        if (document.exitFullscreen) document.exitFullscreen();
        else if (document.mozCancelFullScreen) document.mozCancelFullScreen();
        else if (document.webkitExitFullscreen) document.webkitExitFullscreen();
        else if (document.msExitFullscreen) document.msExitFullscreen();

        document.body.classList.remove('fullscreen-active');
        this.isFullscreen = false;
        document.getElementById('fullscreenBtn').textContent = '🔍 Fullscreen';
    }

    initializeAnimations() {
        if (typeof gsap !== 'undefined' && gsap.registerPlugin) {
            // Potentially register plugins if needed, e.g., ScrollTrigger, though less relevant for slides
        }
    }

    triggerSlideAnimations(slideElement) {
        const slideNumber = parseInt(slideElement.dataset.slide);

        // Reset animations on elements that might have been animated before
        const animatableElements = slideElement.querySelectorAll('.objective-item, .diagram-container, .content-right h3, .content-right ul li, .summary-section');
        if (typeof gsap !== 'undefined') {
            gsap.set(animatableElements, { opacity: 0, y: 20 }); // Initial state for animation
        }


        switch(slideNumber) {
            case 1: // Title Slide
                this.animateTitleSlide(slideElement);
                break;
            case 2: // Learning Objectives
                this.animateObjectives(slideElement);
                break;
            case 3: // Conduction System
                this.animateConductionSystemSlide(slideElement);
                break;
            case 4: // SA Node
                this.animateSANodeSlide(slideElement);
                break;
            case 5: // ECG Basics
                this.animateECGBasicsSlide(slideElement);
                break;
            case 6: // Summary
                this.animateSummarySlide(slideElement);
                break;
            case 7: // Next Lecture
                this.animateNextLectureSlide(slideElement);
                break;
            default:
                this.animateGenericSlide(slideElement);
        }
    }

    // Animation functions adapted for Module 3
    animateTitleSlide(slide) {
        const title = slide.querySelector('h1');
        const subtitle = slide.querySelector('h2');
        const introAnim = slide.querySelector('.animated-cardiac-intro');
        if (typeof gsap !== 'undefined') {
            gsap.from([title, subtitle, introAnim], {
                duration: 0.8,
                y: 30,
                opacity: 0,
                stagger: 0.2,
                ease: "power2.out"
            });
            // Example: Pulsing heart animation for placeholder
            if (introAnim) {
                 gsap.to(introAnim, { scale: 1.1, duration: 1, repeat: -1, yoyo: true, ease: "sine.inOut"});
            }
        }
    }

    animateObjectives(slide) {
        const objectives = slide.querySelectorAll('.objective-item');
        objectives.forEach((obj, index) => {
            obj.classList.add('animate'); // Ensure class for CSS transitions is added
            if (typeof gsap !== 'undefined') {
                gsap.from(obj, {
                    duration: 0.6,
                    y: 30,
                    opacity: 0,
                    delay: index * 0.15,
                    ease: "power2.out"
                });
            }
        });
    }

    animateConductionSystemSlide(slide) {
        const diagram = slide.querySelector('#conductionSystemBlockDiagram');
        const textContent = slide.querySelectorAll('.content-right h3, .content-right ul li, .interactive-icons-info');
        if (typeof gsap !== 'undefined') {
            gsap.from(diagram, { duration: 0.7, x: -50, opacity: 0, ease: "power2.out" });
            gsap.from(textContent, { duration: 0.7, x: 50, opacity: 0, stagger: 0.1, ease: "power2.out", delay: 0.2 });
        }
    }

    animateSANodeSlide(slide) {
        const diagram = slide.querySelector('#saNodeSchematic');
        const textContent = slide.querySelectorAll('.content-left h3, .content-left ul li, .content-right h3, .content-right ul li');
        const animPlaceholder = slide.querySelector('#pacemakerPotentialAnimation');
        if (typeof gsap !== 'undefined') {
            gsap.from(diagram, { duration: 0.7, scale: 0.8, opacity: 0, ease: "back.out(1.7)" });
            gsap.from(textContent, { duration: 0.5, y: 20, opacity: 0, stagger: 0.1, ease: "power2.out", delay: 0.3 });
            if(animPlaceholder) gsap.from(animPlaceholder, { duration: 0.5, opacity: 0, delay: 0.5, ease: "power2.out" });
        }
    }

    animateECGBasicsSlide(slide) {
        const diagram = slide.querySelector('#heartToEcgDiagram');
        const textContent = slide.querySelectorAll('.slide-content > p, .content-right h3, .content-right ul li');
        const buttons = slide.querySelectorAll('.demo-btn');
        if (typeof gsap !== 'undefined') {
            gsap.from(diagram, { duration: 0.8, opacity: 0, rotationY:90, ease: "power2.out" });
            gsap.from(textContent, { duration: 0.6, y: 20, opacity: 0, stagger: 0.1, ease: "power2.out", delay: 0.3 });
            gsap.from(buttons, { duration: 0.5, scale:0.5, opacity: 0, stagger: 0.1, ease: "back.out(1.7)", delay: 0.6 });
        }
    }

    animateSummarySlide(slide) {
        const sections = slide.querySelectorAll('.summary-section');
        const takeaway = slide.querySelector('.key-takeaway');
        if (typeof gsap !== 'undefined') {
            gsap.from(sections, {
                duration: 0.5,
                y: 30,
                opacity: 0,
                stagger: 0.15,
                ease: "power2.out"
            });
            if(takeaway) gsap.from(takeaway, { duration: 0.7, scale:0.8, opacity: 0, delay: sections.length * 0.15, ease: "elastic.out(1, 0.75)" });
        }
    }
    
    animateNextLectureSlide(slide) {
        const elements = slide.querySelectorAll('h2, p, ul li, img, .next-btn');
         if (typeof gsap !== 'undefined') {
            gsap.from(elements, {
                duration: 0.6,
                y: 20,
                opacity: 0,
                stagger: 0.1,
                ease: "power2.out"
            });
        }
    }


    animateGenericSlide(slide) { // Fallback for uncustomized slides
        const elements = slide.querySelectorAll('.content-grid > *, h3, p, ul li');
        if (typeof gsap !== 'undefined') {
            gsap.from(elements, {
                duration: 0.6,
                y: 20,
                opacity: 0,
                stagger: 0.1,
                ease: "power2.out"
            });
        }
    }
}

// Initialize lecture slides when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    const lectureSlides = new LectureSlides();

    // Placeholder for any Module 3 specific global initializations
    // Interactive elements for specific slides will be in lecture3-1-interactive.js etc.
});