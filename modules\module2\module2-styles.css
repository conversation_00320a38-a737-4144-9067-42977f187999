/* Module 2: Biomedical Signal Acquisition - Styles */
/* Author: Dr. <PERSON> - SUST BME 2025 */

/* Module-specific color scheme */
:root {
    --module2-primary: #3498db;
    --module2-secondary: #2980b9;
    --module2-accent: #e74c3c;
    --module2-success: #27ae60;
    --module2-warning: #f39c12;
    --module2-light: #ecf0f1;
    --module2-dark: #2c3e50;
    --module2-gradient: linear-gradient(135deg, #3498db, #2980b9);
}

/* Header and Navigation Enhancements */
header {
    background: var(--module2-gradient);
    color: white;
    padding: 2rem 0;
    box-shadow: 0 4px 20px rgba(52, 152, 219, 0.3);
}

header h1 {
    font-size: 2.5rem;
    margin-bottom: 0.5rem;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.subtitle {
    font-size: 1.2rem;
    opacity: 0.9;
    margin-bottom: 1rem;
}

.course-info {
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 1px solid rgba(255,255,255,0.3);
}

/* Signal Acquisition Pathway */
.acquisition-pathway {
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    padding: 2rem;
    border-radius: 15px;
    margin: 2rem 0;
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
}

.acquisition-steps {
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-wrap: wrap;
    gap: 1rem;
    margin-top: 1.5rem;
}

.acq-step {
    background: white;
    padding: 1.5rem;
    border-radius: 12px;
    text-align: center;
    flex: 1;
    min-width: 200px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.acq-step:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(52, 152, 219, 0.2);
    border-color: var(--module2-primary);
}

.step-icon {
    font-size: 2.5rem;
    margin-bottom: 1rem;
    display: block;
}

.acq-step h4 {
    color: var(--module2-primary);
    margin-bottom: 0.5rem;
    font-size: 1.2rem;
}

.step-connector {
    font-size: 2rem;
    color: var(--module2-primary);
    font-weight: bold;
    margin: 0 0.5rem;
}

/* Signal Types Grid */
.signal-types-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.signal-type-card {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 6px 20px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    border-left: 5px solid var(--module2-primary);
}

.signal-type-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 12px 30px rgba(0,0,0,0.15);
}

.signal-icon {
    font-size: 3rem;
    text-align: center;
    margin-bottom: 1rem;
    display: block;
}

.signal-type-card h4 {
    color: var(--module2-primary);
    margin-bottom: 1rem;
    font-size: 1.3rem;
}

.signal-specs {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    margin-top: 1rem;
}

.signal-specs span {
    background: var(--module2-light);
    padding: 0.3rem 0.8rem;
    border-radius: 20px;
    font-size: 0.9rem;
    color: var(--module2-dark);
}

/* Lecture Cards */
.lectures-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.lecture-card {
    background: white;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    border: 1px solid #e0e0e0;
}

.lecture-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 15px 40px rgba(52, 152, 219, 0.2);
}

.lecture-header {
    background: var(--module2-gradient);
    color: white;
    padding: 1.5rem;
    position: relative;
}

.lecture-number {
    background: rgba(255,255,255,0.2);
    padding: 0.3rem 0.8rem;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: bold;
}

.lecture-header h3 {
    margin: 1rem 0 0.5rem 0;
    font-size: 1.4rem;
}

.lecture-duration {
    opacity: 0.9;
    font-size: 0.9rem;
}

.lecture-content {
    padding: 2rem;
}

.lecture-preview {
    background: #f8f9fa;
    padding: 1.5rem;
    border-radius: 10px;
    margin-bottom: 1.5rem;
    text-align: center;
}

/* Transducer Animation */
.transducer-animation {
    display: flex;
    align-items: center;
    justify-content: space-around;
    font-size: 2rem;
}

.transducer-box {
    background: var(--module2-primary);
    color: white;
    padding: 1rem;
    border-radius: 10px;
    animation: pulse 2s infinite;
}

/* Electrode Showcase */
.electrode-types {
    display: flex;
    justify-content: space-around;
    align-items: center;
    font-size: 2.5rem;
}

.electrode {
    transition: all 0.3s ease;
    cursor: pointer;
}

.electrode:hover {
    transform: scale(1.2);
}

/* Conditioning Chain */
.conditioning-chain {
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 1.8rem;
}

.stage {
    background: var(--module2-primary);
    color: white;
    padding: 0.8rem;
    border-radius: 8px;
    min-width: 50px;
    text-align: center;
}

.arrow {
    color: var(--module2-primary);
    font-weight: bold;
}

/* Lecture Topics */
.lecture-topics ul {
    list-style: none;
    padding: 0;
}

.lecture-topics li {
    padding: 0.5rem 0;
    border-bottom: 1px solid #eee;
    transition: all 0.3s ease;
}

.lecture-topics li:hover {
    background: #f8f9fa;
    padding-left: 1rem;
}

/* Action Buttons */
.lecture-actions {
    display: flex;
    gap: 1rem;
    margin-top: 1.5rem;
}

.btn-primary, .btn-secondary {
    padding: 0.8rem 1.5rem;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-weight: bold;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
}

.btn-primary {
    background: var(--module2-primary);
    color: white;
}

.btn-primary:hover {
    background: var(--module2-secondary);
    transform: translateY(-2px);
}

.btn-secondary {
    background: var(--module2-light);
    color: var(--module2-dark);
    border: 2px solid var(--module2-primary);
}

.btn-secondary:hover {
    background: var(--module2-primary);
    color: white;
}

/* Block Diagram Styles */
.acquisition-chain {
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-wrap: wrap;
    gap: 1rem;
    padding: 2rem;
    background: #f8f9fa;
    border-radius: 15px;
    margin: 2rem 0;
}

.chain-block {
    background: white;
    padding: 1.5rem;
    border-radius: 12px;
    text-align: center;
    min-width: 120px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    cursor: pointer;
}

.chain-block:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.2);
}

.block-icon {
    font-size: 2rem;
    margin-bottom: 0.5rem;
    display: block;
}

.chain-block h4 {
    color: var(--module2-primary);
    margin: 0.5rem 0;
    font-size: 1rem;
}

.block-specs {
    display: flex;
    flex-direction: column;
    gap: 0.3rem;
    margin-top: 0.8rem;
}

.block-specs span {
    background: var(--module2-light);
    padding: 0.2rem 0.5rem;
    border-radius: 15px;
    font-size: 0.8rem;
    color: var(--module2-dark);
}

.chain-arrow {
    font-size: 2rem;
    color: var(--module2-primary);
    font-weight: bold;
}

/* Schematic Diagram Styles */
.instrumentation-amp {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 6px 20px rgba(0,0,0,0.1);
}

.component-label {
    font-size: 12px;
    fill: #2c3e50;
    font-weight: bold;
}

.component-value {
    font-size: 10px;
    fill: #7f8c8d;
}

.input-label, .output-label {
    font-size: 14px;
    fill: #e74c3c;
    font-weight: bold;
}

.equation {
    font-size: 14px;
    fill: #2c3e50;
    font-family: 'Courier New', monospace;
}

/* Interactive Tools */
.interactive-tools-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.tool-card {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 6px 20px rgba(0,0,0,0.1);
    border-left: 5px solid var(--module2-primary);
}

.tool-preview {
    background: #f8f9fa;
    padding: 1rem;
    border-radius: 10px;
    margin: 1rem 0;
    text-align: center;
}

.tool-controls {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.tool-controls label {
    font-weight: bold;
    color: var(--module2-dark);
}

.tool-controls input[type="range"] {
    width: 100%;
    margin: 0.5rem 0;
}

.tool-controls select {
    padding: 0.5rem;
    border: 2px solid var(--module2-light);
    border-radius: 5px;
    background: white;
}

/* Control Buttons */
.diagram-controls, .tool-controls {
    text-align: center;
    margin-top: 1.5rem;
}

.btn-control {
    background: var(--module2-primary);
    color: white;
    border: none;
    padding: 0.8rem 1.2rem;
    margin: 0.3rem;
    border-radius: 8px;
    cursor: pointer;
    font-weight: bold;
    transition: all 0.3s ease;
}

.btn-control:hover {
    background: var(--module2-secondary);
    transform: translateY(-2px);
}

/* Assessment Section */
.assessment-overview {
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: 3rem;
    margin-top: 2rem;
}

.assessment-breakdown {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 6px 20px rgba(0,0,0,0.1);
    text-align: center;
}

.assessment-details {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.assessment-item {
    background: white;
    padding: 1.5rem;
    border-radius: 12px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    border-left: 5px solid var(--module2-primary);
}

.assessment-item h4 {
    color: var(--module2-primary);
    margin-bottom: 1rem;
}

.assessment-item ul {
    list-style: none;
    padding: 0;
}

.assessment-item li {
    padding: 0.3rem 0;
    color: var(--module2-dark);
}

/* Animations */
@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.animate-slide-up {
    animation: slideInUp 0.6s ease-out;
}

/* Responsive Design */
@media (max-width: 768px) {
    .acquisition-steps {
        flex-direction: column;
    }
    
    .step-connector {
        transform: rotate(90deg);
        margin: 1rem 0;
    }
    
    .acquisition-chain {
        flex-direction: column;
    }
    
    .chain-arrow {
        transform: rotate(90deg);
        margin: 1rem 0;
    }
    
    .assessment-overview {
        grid-template-columns: 1fr;
    }
    
    .lectures-grid {
        grid-template-columns: 1fr;
    }
    
    .interactive-tools-grid {
        grid-template-columns: 1fr;
    }
}

/* Print Styles */
@media print {
    .lecture-actions,
    .diagram-controls,
    .tool-controls {
        display: none;
    }
    
    .lecture-card,
    .tool-card {
        break-inside: avoid;
    }
}
