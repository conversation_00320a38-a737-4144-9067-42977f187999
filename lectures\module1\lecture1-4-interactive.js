// Lecture 1.4 Interactive Elements - Medical Device Safety & Standards

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    console.log('Lecture 1.4 interactive elements loading...');
    
    // Initialize all interactive elements
    initializeSafetyVisualizations();
    initializeClassificationInteraction();
    initializeRiskMatrix();
    initializeStandardsExplorer();
    
    console.log('Lecture 1.4 interactive elements loaded successfully');
});

// Initialize safety visualizations
function initializeSafetyVisualizations() {
    // Animate safety icons
    const safetyIcons = document.querySelectorAll('.safety-icon');
    safetyIcons.forEach((icon, index) => {
        setTimeout(() => {
            if (typeof gsap !== 'undefined') {
                gsap.from(icon, {
                    scale: 0,
                    rotation: 360,
                    duration: 1,
                    ease: "back.out(1.7)"
                });
            }
        }, index * 200);
    });
}

// Initialize device classification interaction
function initializeClassificationInteraction() {
    const classItems = document.querySelectorAll('.class-item');
    
    classItems.forEach(item => {
        item.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-5px)';
            this.style.boxShadow = '0 8px 20px rgba(0,0,0,0.15)';
            
            // Highlight corresponding class badge
            const classBadge = this.querySelector('.class-badge');
            if (classBadge) {
                classBadge.style.transform = 'scale(1.1)';
            }
        });
        
        item.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
            this.style.boxShadow = '0 4px 8px rgba(0,0,0,0.1)';
            
            const classBadge = this.querySelector('.class-badge');
            if (classBadge) {
                classBadge.style.transform = 'scale(1)';
            }
        });
        
        item.addEventListener('click', function() {
            showClassificationDetails(this.dataset.class);
        });
    });
}

function showClassificationDetails(classNumber) {
    const classDetails = {
        '1': {
            title: 'Class I - Low Risk Devices',
            description: 'These devices present minimal potential for harm to the user and are often simpler in design than Class II or Class III devices.',
            examples: [
                'Elastic bandages',
                'Examination gloves',
                'Hand-held surgical instruments',
                'Stethoscopes',
                'Wheelchairs',
                'Reading glasses'
            ],
            controls: 'Subject to general controls only, which include: establishment registration, device listing, premarket notification, labeling requirements, and adherence to Quality System Regulation.',
            timeToMarket: '3-6 months'
        },
        '2': {
            title: 'Class II - Moderate Risk Devices',
            description: 'These devices have a moderate to high risk and require special controls in addition to general controls.',
            examples: [
                'ECG machines',
                'Infusion pumps',
                'Surgical drapes',
                'X-ray machines',
                'CT scanners',
                'Ultrasound equipment'
            ],
            controls: 'Subject to general controls plus special controls such as: performance standards, postmarket surveillance, patient registries, special labeling requirements.',
            timeToMarket: '6-12 months'
        },
        '3': {
            title: 'Class III - High Risk Devices',
            description: 'These devices have the highest risk and are usually life-sustaining or life-supporting devices.',
            examples: [
                'Heart valves',
                'Silicone gel breast implants',
                'Implantable pacemakers',
                'Pulse generators',
                'HIV diagnostic tests',
                'Automated external defibrillators'
            ],
            controls: 'Subject to general controls, special controls, and premarket approval (PMA). Requires clinical data to demonstrate safety and effectiveness.',
            timeToMarket: '1-3 years'
        }
    };
    
    const details = classDetails[classNumber];
    if (details) {
        // Create modal or alert with detailed information
        const modalContent = `
            <h3>${details.title}</h3>
            <p><strong>Description:</strong> ${details.description}</p>
            <p><strong>Examples:</strong> ${details.examples.join(', ')}</p>
            <p><strong>Regulatory Controls:</strong> ${details.controls}</p>
            <p><strong>Typical Time to Market:</strong> ${details.timeToMarket}</p>
        `;
        
        // For now, use alert - in production, would use a proper modal
        alert(modalContent.replace(/<[^>]*>/g, '\n').replace(/&nbsp;/g, ' '));
    }
}

// Initialize risk matrix interaction
function initializeRiskMatrix() {
    const riskCells = document.querySelectorAll('.risk-cell');
    
    riskCells.forEach(cell => {
        cell.addEventListener('mouseenter', function() {
            this.style.transform = 'scale(1.1)';
            this.style.zIndex = '10';
            this.style.boxShadow = '0 4px 8px rgba(0,0,0,0.3)';
            
            // Show risk level tooltip
            showRiskTooltip(this);
        });
        
        cell.addEventListener('mouseleave', function() {
            this.style.transform = 'scale(1)';
            this.style.zIndex = '1';
            this.style.boxShadow = 'none';
            
            // Hide tooltip
            hideRiskTooltip();
        });
    });
}

function showRiskTooltip(cell) {
    const riskLevel = cell.textContent.trim();
    const riskDescriptions = {
        'VL': 'Very Low Risk - Acceptable without additional controls',
        'L': 'Low Risk - Acceptable with basic controls',
        'M': 'Medium Risk - ALARP (As Low As Reasonably Practicable)',
        'H': 'High Risk - Unacceptable, requires risk reduction',
        'VH': 'Very High Risk - Unacceptable, immediate action required'
    };
    
    const description = riskDescriptions[riskLevel];
    if (description) {
        const tooltip = document.createElement('div');
        tooltip.id = 'riskTooltip';
        tooltip.style.cssText = `
            position: absolute;
            background: #2c3e50;
            color: white;
            padding: 0.8rem;
            border-radius: 6px;
            font-size: 0.9rem;
            z-index: 1000;
            pointer-events: none;
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
            max-width: 250px;
        `;
        tooltip.textContent = description;
        
        document.body.appendChild(tooltip);
        
        const rect = cell.getBoundingClientRect();
        tooltip.style.left = (rect.right + 10) + 'px';
        tooltip.style.top = rect.top + 'px';
    }
}

function hideRiskTooltip() {
    const tooltip = document.getElementById('riskTooltip');
    if (tooltip) {
        tooltip.remove();
    }
}

// Initialize standards explorer
function initializeStandardsExplorer() {
    const standardLevels = document.querySelectorAll('.standard-level');
    
    standardLevels.forEach(level => {
        level.addEventListener('click', function() {
            const levelType = this.dataset.level;
            exploreStandardLevel(levelType);
        });
        
        level.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-3px)';
            this.style.boxShadow = '0 6px 12px rgba(0,0,0,0.15)';
        });
        
        level.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
            this.style.boxShadow = '0 4px 8px rgba(0,0,0,0.1)';
        });
    });
}

function exploreStandardLevel(levelType) {
    const standardInfo = {
        'general': {
            title: 'IEC 60601-1: General Requirements',
            description: 'The foundational standard that establishes general requirements for basic safety and essential performance of medical electrical equipment.',
            keyRequirements: [
                'Electrical safety and protection',
                'Mechanical safety requirements',
                'Protection against unwanted radiation',
                'Protection against excessive temperatures',
                'Accuracy of operating data and protection against hazardous output',
                'Abnormal operation and fault conditions',
                'Programmable electrical medical systems (PEMS)',
                'Construction requirements',
                'Environmental conditions',
                'Requirements for medical electrical systems'
            ],
            testingAspects: [
                'Type testing during design phase',
                'Routine testing during production',
                'Testing after repair or modification'
            ]
        },
        'collateral': {
            title: 'IEC 60601-1-X: Collateral Standards',
            description: 'These standards address specific aspects that apply across multiple types of medical electrical equipment.',
            standards: [
                {
                    number: '60601-1-2',
                    title: 'Electromagnetic Disturbances',
                    scope: 'EMC requirements and test methods'
                },
                {
                    number: '60601-1-6',
                    title: 'Usability',
                    scope: 'Usability engineering for medical electrical equipment'
                },
                {
                    number: '60601-1-8',
                    title: 'Alarm Systems',
                    scope: 'General requirements, tests and guidance for alarm systems'
                },
                {
                    number: '60601-1-11',
                    title: 'Home Healthcare Environment',
                    scope: 'Requirements for medical electrical equipment and systems used in home healthcare'
                },
                {
                    number: '60601-1-12',
                    title: 'Emergency Medical Services Environment',
                    scope: 'Requirements for medical electrical equipment and systems used in emergency medical services'
                }
            ]
        },
        'particular': {
            title: 'IEC 60601-2-X: Particular Standards',
            description: 'Device-specific standards that provide detailed requirements for specific types of medical electrical equipment.',
            examples: [
                {
                    number: '60601-2-25',
                    device: 'ECG Equipment',
                    requirements: 'Specific safety and performance requirements for electrocardiographs'
                },
                {
                    number: '60601-2-27',
                    device: 'ECG Monitoring Equipment',
                    requirements: 'Requirements for electrocardiographic monitoring equipment'
                },
                {
                    number: '60601-2-40',
                    device: 'EMG Equipment',
                    requirements: 'Particular requirements for electromyographs and evoked response equipment'
                },
                {
                    number: '60601-2-26',
                    device: 'EEG Equipment',
                    requirements: 'Particular requirements for electroencephalographs'
                },
                {
                    number: '60601-2-47',
                    device: 'Ambulatory ECG Systems',
                    requirements: 'Particular requirements for ambulatory electrocardiographic systems'
                }
            ]
        }
    };
    
    const info = standardInfo[levelType];
    if (info) {
        console.log(`Exploring ${info.title}`);
        // In a real implementation, this would open a detailed modal or navigate to a detailed page
        // For now, we'll just highlight the selected level
        document.querySelectorAll('.standard-level').forEach(level => {
            level.style.backgroundColor = '';
        });
        
        const selectedLevel = document.querySelector(`[data-level="${levelType}"]`);
        if (selectedLevel) {
            selectedLevel.style.backgroundColor = '#e8f5e8';
            
            // Reset after 2 seconds
            setTimeout(() => {
                selectedLevel.style.backgroundColor = '';
            }, 2000);
        }
    }
}

// Interactive functions for demonstrations

function demonstrateRiskAssessment() {
    console.log('Demonstrating risk assessment process...');
    
    const processSteps = document.querySelectorAll('.process-step');
    
    processSteps.forEach((step, index) => {
        setTimeout(() => {
            // Highlight current step
            processSteps.forEach(s => s.style.backgroundColor = '');
            step.style.backgroundColor = '#e8f5e8';
            step.style.transform = 'scale(1.05)';
            
            if (typeof gsap !== 'undefined') {
                gsap.from(step, {
                    x: -20,
                    duration: 0.5,
                    ease: "power2.out"
                });
            }
            
            // Reset after animation
            setTimeout(() => {
                step.style.backgroundColor = '';
                step.style.transform = 'scale(1)';
            }, 1500);
        }, index * 800);
    });
}

function showComplianceChecklist() {
    const checklist = [
        'Device classification determined',
        'Applicable standards identified',
        'Risk management file created',
        'Design controls implemented',
        'Clinical evaluation completed',
        'Quality management system established',
        'Labeling requirements met',
        'Post-market surveillance plan developed'
    ];
    
    let checklistText = 'Medical Device Compliance Checklist:\n\n';
    checklist.forEach((item, index) => {
        checklistText += `${index + 1}. ☐ ${item}\n`;
    });
    
    alert(checklistText);
}

// Add CSS for interactive elements
const style = document.createElement('style');
style.textContent = `
    .safety-icon {
        font-size: 3rem;
        margin: 0 1rem;
        display: inline-block;
        transition: transform 0.3s ease;
    }
    
    .safety-icon:hover {
        transform: scale(1.2);
    }
    
    .class-item {
        background: white;
        border-radius: 8px;
        padding: 1.5rem;
        margin: 1rem 0;
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        transition: all 0.3s ease;
        cursor: pointer;
        border-left: 4px solid transparent;
    }
    
    .class-item[data-class="1"] {
        border-left-color: #27ae60;
    }
    
    .class-item[data-class="2"] {
        border-left-color: #f39c12;
    }
    
    .class-item[data-class="3"] {
        border-left-color: #e74c3c;
    }
    
    .class-badge {
        padding: 0.5rem 1rem;
        border-radius: 20px;
        color: white;
        font-weight: bold;
        font-size: 0.9rem;
        transition: transform 0.3s ease;
    }
    
    .class-i { background: #27ae60; }
    .class-ii { background: #f39c12; }
    .class-iii { background: #e74c3c; }
    
    .standard-level {
        background: white;
        border-radius: 8px;
        padding: 1.5rem;
        margin: 1rem 0;
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        transition: all 0.3s ease;
        cursor: pointer;
    }
    
    .level-badge {
        padding: 0.5rem 1rem;
        border-radius: 15px;
        color: white;
        font-weight: bold;
        font-size: 0.8rem;
        display: inline-block;
        margin-bottom: 1rem;
    }
    
    .general { background: #3498db; }
    .collateral { background: #9b59b6; }
    .particular { background: #e74c3c; }
    
    .risk-table {
        width: 100%;
        border-collapse: collapse;
        margin: 1rem 0;
        font-size: 0.9rem;
    }
    
    .risk-table th, .risk-table td {
        padding: 0.8rem;
        border: 1px solid #dee2e6;
        text-align: center;
    }
    
    .risk-table th {
        background: #3498db;
        color: white;
        font-weight: bold;
    }
    
    .severity-label {
        background: #f8f9fa;
        font-weight: bold;
        text-align: left !important;
    }
    
    .risk-cell {
        font-weight: bold;
        cursor: pointer;
        transition: all 0.3s ease;
    }
    
    .very-low { background: #d5f4e6; color: #27ae60; }
    .low { background: #fff3cd; color: #856404; }
    .medium { background: #ffeaa7; color: #d68910; }
    .high { background: #fadbd8; color: #e74c3c; }
    .very-high { background: #f1948a; color: #922b21; }
    
    .risk-legend {
        margin-top: 1rem;
    }
    
    .legend-item {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        margin: 0.25rem 0;
    }
    
    .legend-color {
        width: 20px;
        height: 20px;
        border-radius: 4px;
        border: 1px solid #dee2e6;
    }
    
    .process-step {
        background: white;
        border-radius: 8px;
        padding: 1.5rem;
        margin: 1rem 0;
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        transition: all 0.3s ease;
        border-left: 4px solid #3498db;
    }
    
    .step-number {
        background: #3498db;
        color: white;
        width: 30px;
        height: 30px;
        border-radius: 50%;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        font-weight: bold;
        margin-right: 1rem;
    }
    
    .requirement-category {
        margin: 1.5rem 0;
    }
    
    .requirement-list {
        display: flex;
        flex-direction: column;
        gap: 0.5rem;
    }
    
    .req-item {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        padding: 0.5rem;
        background: #f8f9fa;
        border-radius: 4px;
    }
    
    .req-icon {
        font-size: 1.2rem;
        min-width: 25px;
    }
    
    .pathway-chart {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 0.5rem;
    }
    
    .pathway-step {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        background: #f8f9fa;
        padding: 1rem;
        border-radius: 6px;
        width: 100%;
        max-width: 250px;
    }
    
    .pathway-arrow {
        font-size: 1.5rem;
        color: #3498db;
    }
    
    .step-icon {
        font-size: 1.5rem;
    }
`;
document.head.appendChild(style);
