/* Medical Instrumentation Course Stylesheet */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f8f9fa;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header Styles */
header {
    background: linear-gradient(135deg, #2c3e50, #3498db);
    color: white;
    padding: 2rem 0;
    text-align: center;
}

header h1 {
    font-size: 2.5rem;
    margin-bottom: 0.5rem;
    font-weight: 300;
}

.subtitle {
    font-size: 1.2rem;
    opacity: 0.9;
    margin-bottom: 1rem;
}

.course-info {
    margin-top: 1.5rem;
    padding-top: 1rem;
    border-top: 1px solid rgba(255,255,255,0.3);
}

.author {
    font-size: 1.1rem;
    margin-bottom: 0.5rem;
}

.institution {
    font-size: 1rem;
    opacity: 0.8;
}

/* Navigation Styles */
nav {
    background-color: #34495e;
    padding: 1rem 0;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

nav ul {
    list-style: none;
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
}

nav li {
    margin: 0 1rem;
}

nav a {
    color: white;
    text-decoration: none;
    padding: 0.5rem 1rem;
    border-radius: 4px;
    transition: background-color 0.3s ease;
}

nav a:hover {
    background-color: #2c3e50;
}

/* Section Styles */
.section {
    padding: 3rem 0;
}

.section:nth-child(even) {
    background-color: white;
}

h2 {
    font-size: 2rem;
    margin-bottom: 1.5rem;
    color: #2c3e50;
    text-align: center;
}

h3 {
    color: #34495e;
    margin-bottom: 1rem;
}

/* Course Journey */
.course-journey {
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    padding: 2rem;
    border-radius: 8px;
    margin: 2rem 0;
    border: 1px solid #dee2e6;
}

.course-journey h3 {
    text-align: center;
    color: #2c3e50;
    margin-bottom: 2rem;
}

.journey-path {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-wrap: wrap;
    gap: 1rem;
}

.journey-step {
    background: white;
    padding: 1.5rem;
    border-radius: 8px;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    text-align: center;
    flex: 1;
    min-width: 200px;
    border-top: 4px solid #3498db;
}

.journey-step h4 {
    color: #2c3e50;
    margin-bottom: 0.5rem;
    font-size: 1.1rem;
}

.journey-step p {
    color: #7f8c8d;
    font-size: 0.9rem;
}

.journey-arrow {
    font-size: 2rem;
    color: #3498db;
    font-weight: bold;
}

/* Course Objectives */
.course-objectives {
    background: white;
    padding: 2rem;
    border-radius: 8px;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    margin: 2rem 0;
    border-left: 4px solid #e74c3c;
}

.course-objectives h3 {
    color: #e74c3c;
    margin-bottom: 1rem;
}

.course-objectives ul {
    list-style-type: none;
    padding-left: 0;
}

.course-objectives li {
    padding: 0.5rem 0;
    padding-left: 1.5rem;
    position: relative;
}

.course-objectives li:before {
    content: "✓";
    position: absolute;
    left: 0;
    color: #27ae60;
    font-weight: bold;
}

/* Hands-On Emphasis */
.hands-on-emphasis {
    background: linear-gradient(135deg, #27ae60, #2ecc71);
    color: white;
    padding: 2rem;
    border-radius: 8px;
    margin: 2rem 0;
    text-align: center;
}

.hands-on-emphasis h3 {
    color: white;
    margin-bottom: 1rem;
}

/* Learning Pathway */
.learning-pathway {
    background: white;
    padding: 2rem;
    border-radius: 8px;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    margin: 2rem 0;
    border-left: 4px solid #9b59b6;
}

.learning-pathway h3 {
    color: #9b59b6;
    text-align: center;
    margin-bottom: 2rem;
}

.pathway-flow {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-wrap: wrap;
    gap: 1rem;
}

.pathway-stage {
    background: linear-gradient(135deg, #9b59b6, #8e44ad);
    color: white;
    padding: 1.5rem;
    border-radius: 8px;
    text-align: center;
    flex: 1;
    min-width: 180px;
}

.stage-number {
    background: rgba(255,255,255,0.2);
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-weight: bold;
    display: inline-block;
    margin-bottom: 1rem;
}

.pathway-stage h4 {
    color: white;
    margin-bottom: 0.5rem;
    font-size: 1.1rem;
}

.pathway-stage p {
    color: rgba(255,255,255,0.9);
    font-size: 0.9rem;
}

.pathway-arrow {
    font-size: 2rem;
    color: #9b59b6;
    font-weight: bold;
}

/* Modules Introduction */
.modules-intro {
    text-align: center;
    font-size: 1.1rem;
    margin-bottom: 2rem;
    color: #2c3e50;
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
}

/* Feature Cards */
.features {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.feature-card {
    background: white;
    padding: 2rem;
    border-radius: 8px;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    text-align: center;
    transition: transform 0.3s ease;
}

.feature-card:hover {
    transform: translateY(-5px);
}

/* Project Grid */
.projects-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.project-card {
    background: white;
    padding: 2rem;
    border-radius: 8px;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    border-left: 4px solid #3498db;
}

.project-links {
    margin: 1rem 0;
}

.btn {
    display: inline-block;
    padding: 0.5rem 1rem;
    background-color: #3498db;
    color: white;
    text-decoration: none;
    border-radius: 4px;
    margin-right: 0.5rem;
    margin-bottom: 0.5rem;
    transition: background-color 0.3s ease;
}

.btn:hover {
    background-color: #2980b9;
}

.btn-secondary {
    background-color: #95a5a6;
}

.btn-secondary:hover {
    background-color: #7f8c8d;
}

.project-files {
    margin-top: 1rem;
}

.file-tag {
    display: inline-block;
    background-color: #ecf0f1;
    color: #2c3e50;
    padding: 0.25rem 0.5rem;
    border-radius: 3px;
    font-size: 0.8rem;
    margin-right: 0.5rem;
    margin-bottom: 0.25rem;
    font-family: 'Courier New', monospace;
}

/* Resources Grid */
.resources-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.resource-item {
    background: white;
    padding: 2rem;
    border-radius: 8px;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
}

.resource-item h4 {
    color: #2c3e50;
    margin-bottom: 1rem;
    border-bottom: 2px solid #3498db;
    padding-bottom: 0.5rem;
}

.resource-item ul {
    list-style-type: none;
    padding-left: 0;
}

.resource-item li {
    padding: 0.5rem 0;
    border-bottom: 1px solid #ecf0f1;
}

.resource-item li:last-child {
    border-bottom: none;
}

.resource-item a {
    color: #3498db;
    text-decoration: none;
}

.resource-item a:hover {
    text-decoration: underline;
}

/* LMS Modules */
.modules-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.module-card {
    background: white;
    border-radius: 8px;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    overflow: hidden;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.module-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 15px rgba(0,0,0,0.15);
}

.module-header {
    background: linear-gradient(135deg, #3498db, #2980b9);
    color: white;
    padding: 1.5rem;
    position: relative;
}

.module-number {
    background: rgba(255,255,255,0.2);
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: bold;
    display: inline-block;
    margin-bottom: 0.5rem;
}

.module-header h3 {
    color: white;
    margin: 0;
    font-size: 1.3rem;
}

.module-content {
    padding: 1.5rem;
}

.module-content ul {
    list-style-type: none;
    padding-left: 0;
    margin-bottom: 1rem;
}

.module-content li {
    padding: 0.5rem 0;
    padding-left: 1.5rem;
    position: relative;
    border-bottom: 1px solid #ecf0f1;
}

.module-content li:before {
    content: "📚";
    position: absolute;
    left: 0;
}

.module-content li:last-child {
    border-bottom: none;
}

.module-duration {
    background: #ecf0f1;
    color: #2c3e50;
    padding: 0.5rem 1rem;
    border-radius: 4px;
    font-weight: bold;
    text-align: center;
    margin-top: 1rem;
}

/* Virtual Lab */
.lab-intro {
    text-align: center;
    font-size: 1.1rem;
    margin-bottom: 2rem;
    color: #2c3e50;
}

.lab-features {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin: 2rem 0;
}

.lab-feature {
    background: white;
    padding: 2rem;
    border-radius: 8px;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    text-align: center;
    border-top: 4px solid #9b59b6;
}

.lab-feature h3 {
    color: #9b59b6;
    margin-bottom: 1rem;
}

.lab-experiments h3 {
    text-align: center;
    margin: 3rem 0 2rem 0;
    color: #2c3e50;
}

.experiments-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1.5rem;
}

.experiment-card {
    background: white;
    padding: 1.5rem;
    border-radius: 8px;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    border-left: 4px solid #9b59b6;
    position: relative;
}

.experiment-card h4 {
    color: #2c3e50;
    margin-bottom: 1rem;
}

.lab-badge {
    position: absolute;
    top: 1rem;
    right: 1rem;
    background: #9b59b6;
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: bold;
}

/* Footer */
footer {
    background-color: #2c3e50;
    color: white;
    padding: 3rem 0 1rem 0;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.footer-content h3 {
    color: #3498db;
    margin-bottom: 1rem;
    border-bottom: 2px solid #3498db;
    padding-bottom: 0.5rem;
}

.footer-content a {
    color: #3498db;
    text-decoration: none;
}

.footer-content a:hover {
    text-decoration: underline;
}

.copyright {
    text-align: center;
    padding-top: 2rem;
    border-top: 1px solid #34495e;
    opacity: 0.8;
}

.copyright p {
    margin: 0.25rem 0;
    font-size: 0.9rem;
}

/* Module Detail Pages */
.module-detail {
    background: white;
    border-radius: 8px;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    overflow: hidden;
    margin-bottom: 2rem;
}

.module-header-detail {
    background: linear-gradient(135deg, #3498db, #2980b9);
    color: white;
    padding: 2rem;
    text-align: center;
}

.module-header-detail .module-number {
    background: rgba(255,255,255,0.2);
    padding: 0.5rem 1rem;
    border-radius: 25px;
    font-size: 1rem;
    font-weight: bold;
    display: inline-block;
    margin-bottom: 1rem;
}

.module-header-detail h2 {
    color: white;
    margin: 0;
    font-size: 2rem;
}

.module-header-detail .module-duration {
    background: rgba(255,255,255,0.1);
    padding: 0.5rem 1rem;
    border-radius: 20px;
    margin-top: 1rem;
    display: inline-block;
}

.module-description {
    padding: 2rem;
}

.learning-outcomes {
    background: #f8f9fa;
    padding: 1.5rem;
    border-radius: 6px;
    border-left: 4px solid #27ae60;
}

.learning-outcomes li:before {
    content: "🎯";
    margin-right: 0.5rem;
}

.topics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
    margin: 1.5rem 0;
}

.topic-item {
    background: #f8f9fa;
    padding: 1.5rem;
    border-radius: 6px;
    border-top: 3px solid #3498db;
}

.topic-item h4 {
    color: #2c3e50;
    margin-bottom: 1rem;
}

.lab-exercises {
    margin: 2rem 0;
}

.lab-item {
    background: #e8f5e8;
    padding: 1.5rem;
    border-radius: 6px;
    margin-bottom: 1rem;
    border-left: 4px solid #27ae60;
}

.lab-item h4 {
    color: #27ae60;
    margin-bottom: 0.5rem;
}

.assessment-list {
    background: #fff3cd;
    padding: 1.5rem;
    border-radius: 6px;
    border-left: 4px solid #ffc107;
}

.assessment-list li {
    padding: 0.5rem 0;
    border-bottom: 1px solid #ffeaa7;
}

.assessment-list li:last-child {
    border-bottom: none;
}

/* Course Overview */
.course-overview {
    text-align: center;
    margin-bottom: 3rem;
}

.course-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.stat-item {
    background: white;
    padding: 2rem;
    border-radius: 8px;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    text-align: center;
}

.stat-item h3 {
    font-size: 3rem;
    color: #3498db;
    margin: 0;
}

.stat-item p {
    color: #7f8c8d;
    margin: 0.5rem 0 0 0;
    font-weight: bold;
}

/* Virtual Lab Progression */
.lab-progression {
    background: white;
    padding: 2rem;
    border-radius: 8px;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    margin: 2rem 0;
    border-left: 4px solid #e74c3c;
}

.lab-progression h3 {
    color: #e74c3c;
    text-align: center;
    margin-bottom: 2rem;
}

.progression-timeline {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
}

.timeline-item {
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    padding: 1.5rem;
    border-radius: 8px;
    border-left: 4px solid #e74c3c;
    position: relative;
}

.timeline-marker {
    background: #e74c3c;
    color: white;
    width: 2rem;
    height: 2rem;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    margin-bottom: 1rem;
}

.timeline-content h4 {
    color: #2c3e50;
    margin-bottom: 0.5rem;
}

.timeline-content p {
    color: #7f8c8d;
    font-size: 0.9rem;
}

/* Hands-On Highlight */
.hands-on-highlight {
    background: linear-gradient(135deg, #3498db, #2980b9);
    color: white;
    padding: 3rem 2rem;
    border-radius: 8px;
    margin: 3rem 0;
}

.hands-on-highlight h3 {
    color: white;
    text-align: center;
    margin-bottom: 2rem;
}

.highlight-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
}

.highlight-item {
    background: rgba(255,255,255,0.1);
    padding: 1.5rem;
    border-radius: 8px;
    text-align: center;
}

.highlight-item h4 {
    color: white;
    margin-bottom: 1rem;
}

.highlight-item p {
    color: rgba(255,255,255,0.9);
    font-size: 0.9rem;
}

/* Virtual Lab Styles */
.lab-capabilities {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin: 2rem 0;
}

/* Lab Journey Overview */
.lab-journey-overview {
    background: white;
    padding: 2rem;
    border-radius: 8px;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    margin: 2rem 0;
    border-left: 4px solid #27ae60;
}

.lab-journey-overview h3 {
    color: #27ae60;
    text-align: center;
    margin-bottom: 2rem;
}

.signal-journey {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-wrap: wrap;
    gap: 1rem;
}

.signal-journey .journey-stage {
    background: linear-gradient(135deg, #27ae60, #2ecc71);
    color: white;
    padding: 1.5rem;
    border-radius: 8px;
    text-align: center;
    flex: 1;
    min-width: 200px;
}

.signal-journey .journey-stage h4 {
    color: white;
    margin-bottom: 0.5rem;
    font-size: 1.1rem;
}

.signal-journey .journey-stage p {
    color: rgba(255,255,255,0.9);
    font-size: 0.9rem;
}

.journey-connector {
    font-size: 2rem;
    color: #27ae60;
    font-weight: bold;
}

.capability-card {
    background: white;
    padding: 2rem;
    border-radius: 8px;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    text-align: center;
    border-top: 4px solid #9b59b6;
}

.instruments-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin: 2rem 0;
}

.instrument-card {
    background: white;
    padding: 2rem;
    border-radius: 8px;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    text-align: center;
    transition: transform 0.3s ease;
}

.instrument-card:hover {
    transform: translateY(-5px);
}

.instrument-image {
    font-size: 3rem;
    margin: 1rem 0;
}

.instrument-card ul {
    text-align: left;
    margin: 1rem 0;
}

.launch-btn, .start-experiment, .launch-simulation {
    background: #3498db;
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 4px;
    cursor: pointer;
    font-weight: bold;
    transition: background-color 0.3s ease;
}

.launch-btn:hover, .start-experiment:hover, .launch-simulation:hover {
    background: #2980b9;
}

.experiments-categories {
    margin: 2rem 0;
}

.category-section {
    margin-bottom: 3rem;
}

.category-section h3 {
    color: #2c3e50;
    border-bottom: 2px solid #3498db;
    padding-bottom: 0.5rem;
    margin-bottom: 1.5rem;
}

.experiments-list {
    display: grid;
    gap: 1.5rem;
}

.experiment-item {
    background: white;
    padding: 2rem;
    border-radius: 8px;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    border-left: 4px solid #27ae60;
}

.experiment-details {
    margin: 1rem 0;
}

.experiment-details span {
    background: #ecf0f1;
    padding: 0.25rem 0.75rem;
    border-radius: 12px;
    font-size: 0.8rem;
    margin-right: 0.5rem;
}

.duration {
    background: #3498db !important;
    color: white !important;
}

.difficulty {
    background: #e74c3c !important;
    color: white !important;
}

.simulations-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin: 2rem 0;
}

.simulation-card {
    background: white;
    padding: 2rem;
    border-radius: 8px;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    text-align: center;
    border-top: 4px solid #e74c3c;
}

.resources-section {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin: 2rem 0;
}

.resource-category {
    background: white;
    padding: 2rem;
    border-radius: 8px;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
}

.resource-category h3 {
    color: #2c3e50;
    border-bottom: 2px solid #3498db;
    padding-bottom: 0.5rem;
    margin-bottom: 1rem;
}

.resource-category ul {
    list-style-type: none;
    padding-left: 0;
}

.resource-category li {
    padding: 0.5rem 0;
    border-bottom: 1px solid #ecf0f1;
}

.resource-category a {
    color: #3498db;
    text-decoration: none;
}

.resource-category a:hover {
    text-decoration: underline;
}

.module-navigation {
    text-align: center;
    background: white;
    padding: 3rem;
    border-radius: 8px;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
}

.nav-buttons {
    margin-top: 2rem;
}

.nav-buttons .btn {
    margin: 0 1rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    header h1 {
        font-size: 2rem;
    }

    nav ul {
        flex-direction: column;
        align-items: center;
    }

    nav li {
        margin: 0.25rem 0;
    }

    .features,
    .projects-grid,
    .resources-grid,
    .modules-grid,
    .instruments-grid,
    .simulations-grid {
        grid-template-columns: 1fr;
    }

    .container {
        padding: 0 15px;
    }

    .course-stats {
        grid-template-columns: repeat(2, 1fr);
    }

    .topics-grid {
        grid-template-columns: 1fr;
    }

    .journey-path,
    .pathway-flow,
    .signal-journey {
        flex-direction: column;
    }

    .journey-arrow,
    .pathway-arrow,
    .journey-connector {
        transform: rotate(90deg);
        margin: 1rem 0;
    }

    .progression-timeline,
    .highlight-grid {
        grid-template-columns: 1fr;
    }
}

/* Lecture Section Styles */
.lectures-intro {
    text-align: center;
    font-size: 1.1rem;
    margin-bottom: 2rem;
    color: #2c3e50;
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
}

.lectures-overview {
    margin: 2rem 0;
}

.lecture-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 2rem;
    margin-bottom: 3rem;
}

.stat-card {
    background: white;
    padding: 2rem;
    border-radius: 12px;
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    text-align: center;
    border-top: 4px solid #3498db;
    transition: transform 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-5px);
}

.stat-card h3 {
    font-size: 2.5rem;
    color: #3498db;
    margin: 0;
    font-weight: bold;
}

.stat-card p {
    color: #7f8c8d;
    margin: 0.5rem 0 0 0;
    font-weight: 500;
}

.lecture-modules-grid {
    display: grid;
    gap: 3rem;
    margin-top: 3rem;
}

.lecture-module-card {
    background: white;
    border-radius: 12px;
    box-shadow: 0 6px 12px rgba(0,0,0,0.1);
    overflow: hidden;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.lecture-module-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 12px 24px rgba(0,0,0,0.15);
}

.lecture-module-header {
    background: linear-gradient(135deg, #3498db, #2980b9);
    color: white;
    padding: 2rem;
    text-align: center;
}

.module-badge {
    background: rgba(255,255,255,0.2);
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: bold;
    display: inline-block;
    margin-bottom: 1rem;
}

.lecture-module-header h3 {
    color: white;
    margin: 0 0 0.5rem 0;
    font-size: 1.5rem;
}

.module-subtitle {
    color: rgba(255,255,255,0.9);
    margin: 0;
    font-size: 1rem;
}

.lecture-list {
    padding: 2rem;
}

.lecture-item {
    display: flex;
    align-items: center;
    gap: 1.5rem;
    padding: 1.5rem;
    border-radius: 8px;
    margin-bottom: 1rem;
    background: #f8f9fa;
    transition: all 0.3s ease;
    border-left: 4px solid transparent;
}

.lecture-item:hover {
    background: #e9ecef;
    border-left-color: #3498db;
    transform: translateX(5px);
}

.lecture-icon {
    font-size: 2rem;
    min-width: 50px;
    text-align: center;
}

.lecture-info {
    flex: 1;
}

.lecture-info h4 {
    color: #2c3e50;
    margin: 0 0 0.5rem 0;
    font-size: 1.1rem;
}

.lecture-info p {
    color: #7f8c8d;
    margin: 0 0 1rem 0;
    font-size: 0.9rem;
}

.lecture-features {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.feature-tag {
    background: #3498db;
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 500;
}

.lecture-btn {
    background: linear-gradient(135deg, #27ae60, #2ecc71);
    color: white;
    padding: 0.8rem 1.5rem;
    border-radius: 6px;
    text-decoration: none;
    font-weight: bold;
    transition: all 0.3s ease;
    display: inline-block;
    min-width: 120px;
    text-align: center;
}

.lecture-btn:hover {
    background: linear-gradient(135deg, #229954, #27ae60);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(39, 174, 96, 0.3);
}

.lecture-modules-preview {
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    padding: 3rem 2rem;
    border-radius: 12px;
    margin-top: 3rem;
    text-align: center;
}

.lecture-modules-preview h3 {
    color: #2c3e50;
    margin-bottom: 2rem;
}

.preview-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
}

.preview-item {
    background: white;
    padding: 2rem;
    border-radius: 8px;
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
}

.preview-item:hover {
    transform: translateY(-5px);
}

.preview-badge {
    background: #95a5a6;
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: bold;
    display: inline-block;
    margin-bottom: 1rem;
}

.preview-item h4 {
    color: #2c3e50;
    margin: 0 0 0.5rem 0;
}

.preview-item p {
    color: #7f8c8d;
    margin: 0;
    font-size: 0.9rem;
}

/* Responsive Design for Lectures */
@media (max-width: 768px) {
    .lecture-stats {
        grid-template-columns: repeat(2, 1fr);
    }

    .lecture-item {
        flex-direction: column;
        text-align: center;
        gap: 1rem;
    }

    .lecture-info {
        text-align: center;
    }

    .lecture-features {
        justify-content: center;
    }

    .preview-grid {
        grid-template-columns: 1fr;
    }
}

/* Hero Header Enhanced Styles */
.hero-header {
    background: linear-gradient(135deg, #2c3e50, #34495e, #3498db);
    color: white;
    padding: 3rem 0;
    min-height: 100vh;
    display: flex;
    align-items: center;
    position: relative;
    overflow: hidden;
}

.hero-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    opacity: 0.3;
}

.hero-header .container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
    position: relative;
    z-index: 1;
}

.hero-content h1 {
    margin: 0 0 1rem 0;
    font-size: 3.5rem;
    font-weight: bold;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
    line-height: 1.2;
}

.hero-description {
    font-size: 1.1rem;
    margin: 0 0 2rem 0;
    opacity: 0.8;
    line-height: 1.6;
}

.hero-stats {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 1rem;
    margin: 2rem 0;
    padding: 2rem;
    background: rgba(255,255,255,0.1);
    border-radius: 12px;
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
}

.stat-item {
    text-align: center;
    padding: 1rem;
}

.stat-number {
    display: block;
    font-size: 2.5rem;
    font-weight: bold;
    color: #f39c12;
    margin-bottom: 0.5rem;
}

.stat-label {
    font-size: 0.9rem;
    opacity: 0.8;
    font-weight: 500;
}

.author-card {
    display: flex;
    align-items: center;
    gap: 1.5rem;
    background: rgba(255,255,255,0.1);
    padding: 2rem;
    border-radius: 12px;
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
}

.author-avatar {
    font-size: 4rem;
    background: rgba(255,255,255,0.2);
    border-radius: 50%;
    width: 80px;
    height: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.author-details p {
    margin: 0.5rem 0;
    font-size: 1rem;
}

.hero-buttons {
    display: flex;
    gap: 1rem;
    margin-top: 2rem;
    flex-wrap: wrap;
}

.btn-primary {
    background: linear-gradient(135deg, #e74c3c, #c0392b);
    color: white;
}

.btn-secondary {
    background: linear-gradient(135deg, #3498db, #2980b9);
    color: white;
}

.btn-accent {
    background: linear-gradient(135deg, #27ae60, #229954);
    color: white;
}

.btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(0,0,0,0.3);
}

.hero-visual {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.signal-animation {
    background: rgba(255,255,255,0.1);
    border-radius: 12px;
    padding: 2rem;
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
}

.animation-controls {
    display: flex;
    gap: 0.5rem;
    margin-top: 1rem;
    justify-content: center;
}

.animation-btn {
    background: rgba(255,255,255,0.2);
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.9rem;
    type: button;
}

.animation-btn:hover {
    background: rgba(255,255,255,0.3);
    transform: translateY(-2px);
}

.hero-features {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
}

.feature-highlight {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    background: rgba(255,255,255,0.1);
    padding: 1rem;
    border-radius: 8px;
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
}

.feature-icon {
    font-size: 1.5rem;
}

.feature-text {
    font-size: 0.9rem;
    font-weight: 500;
}

/* Responsive Design for Hero */
@media (max-width: 768px) {
    .hero-header .container {
        grid-template-columns: 1fr;
        gap: 2rem;
        text-align: center;
    }

    .hero-content h1 {
        font-size: 2.5rem;
    }

    .hero-stats {
        grid-template-columns: repeat(2, 1fr);
    }

    .author-card {
        flex-direction: column;
        text-align: center;
    }

    .hero-buttons {
        justify-content: center;
    }
}

/* Smooth scrolling */
html {
    scroll-behavior: smooth;
}

/* Back to Top Button */
.back-to-top-btn {
    position: fixed;
    bottom: 30px;
    right: 30px;
    background-color: #3498db;
    color: white;
    border: none;
    border-radius: 50%;
    width: 50px;
    height: 50px;
    font-size: 24px;
    text-align: center;
    line-height: 50px;
    cursor: pointer;
    display: none; /* Hidden by default */
    z-index: 1000;
    transition: background-color 0.3s ease, opacity 0.3s ease, visibility 0.3s ease;
    box-shadow: 0 2px 5px rgba(0,0,0,0.2);
}

.back-to-top-btn:hover {
    background-color: #2980b9;
}
