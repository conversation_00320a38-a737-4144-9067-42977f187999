// Lecture 2.1 Interactive Elements - Transduction Principles

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    console.log('Lecture 2.1 interactive elements loading...');
    
    // Initialize all interactive elements
    initializeTransductionVisualizations();
    initializeCharacteristicsPlot();
    initializeTemperaturePlot();
    initializeWheatstoneBridge();
    
    console.log('Lecture 2.1 interactive elements loaded successfully');
});

// Initialize transduction visualizations
function initializeTransductionVisualizations() {
    // Animate energy flow
    const energyTypes = document.querySelectorAll('.energy-type');
    const arrows = document.querySelectorAll('.arrow');
    
    // Animate energy flow sequence
    energyTypes.forEach((type, index) => {
        setTimeout(() => {
            if (typeof gsap !== 'undefined') {
                gsap.from(type, {
                    scale: 0,
                    duration: 0.8,
                    ease: "back.out(1.7)"
                });
            }
        }, index * 300);
    });
    
    arrows.forEach((arrow, index) => {
        setTimeout(() => {
            if (typeof gsap !== 'undefined') {
                gsap.from(arrow, {
                    x: -20,
                    opacity: 0,
                    duration: 0.5,
                    ease: "power2.out"
                });
            }
        }, (index + 1) * 300 + 150);
    });
    
    // Animate mechanism items
    const mechanismItems = document.querySelectorAll('.mechanism-item');
    mechanismItems.forEach(item => {
        item.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-5px) scale(1.02)';
            this.style.boxShadow = '0 8px 20px rgba(0,0,0,0.15)';
            
            const icon = this.querySelector('.mechanism-icon');
            if (icon && typeof gsap !== 'undefined') {
                gsap.to(icon, {
                    rotation: 360,
                    duration: 0.6,
                    ease: "power2.out"
                });
            }
        });
        
        item.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
            this.style.boxShadow = '0 4px 8px rgba(0,0,0,0.1)';
        });
    });
}

// Initialize characteristics plot
function initializeCharacteristicsPlot() {
    const canvas = document.getElementById('characteristicsCanvas');
    if (!canvas) return;
    
    const ctx = canvas.getContext('2d');
    
    // Initial plot - sensitivity
    drawCharacteristicPlot(ctx, canvas.width, canvas.height, 'sensitivity');
}

function drawCharacteristicPlot(ctx, width, height, characteristic) {
    ctx.clearRect(0, 0, width, height);
    
    // Draw background
    ctx.fillStyle = '#f8f9fa';
    ctx.fillRect(0, 0, width, height);
    
    // Draw axes
    const margin = 40;
    const plotWidth = width - 2 * margin;
    const plotHeight = height - 2 * margin;
    
    ctx.strokeStyle = '#2c3e50';
    ctx.lineWidth = 2;
    ctx.beginPath();
    ctx.moveTo(margin, height - margin);
    ctx.lineTo(width - margin, height - margin);
    ctx.moveTo(margin, margin);
    ctx.lineTo(margin, height - margin);
    ctx.stroke();
    
    // Draw characteristic curve based on type
    switch(characteristic) {
        case 'sensitivity':
            drawSensitivityCurve(ctx, margin, plotWidth, plotHeight);
            break;
        case 'linearity':
            drawLinearityCurve(ctx, margin, plotWidth, plotHeight);
            break;
        case 'hysteresis':
            drawHysteresisCurve(ctx, margin, plotWidth, plotHeight);
            break;
        case 'response':
            drawResponseCurve(ctx, margin, plotWidth, plotHeight);
            break;
    }
    
    // Add labels
    ctx.fillStyle = '#2c3e50';
    ctx.font = '12px Arial';
    ctx.fillText('Input', width - 60, height - 10);
    ctx.save();
    ctx.translate(15, height / 2);
    ctx.rotate(-Math.PI / 2);
    ctx.fillText('Output', 0, 0);
    ctx.restore();
}

function drawSensitivityCurve(ctx, margin, plotWidth, plotHeight) {
    ctx.strokeStyle = '#e74c3c';
    ctx.lineWidth = 3;
    ctx.beginPath();
    
    // Linear relationship for ideal sensitivity
    ctx.moveTo(margin, margin + plotHeight);
    ctx.lineTo(margin + plotWidth, margin);
    ctx.stroke();
    
    // Add sensitivity annotation
    ctx.fillStyle = '#e74c3c';
    ctx.font = 'bold 14px Arial';
    ctx.fillText('Slope = Sensitivity', margin + plotWidth/2 - 50, margin + 20);
}

function drawLinearityCurve(ctx, margin, plotWidth, plotHeight) {
    ctx.strokeStyle = '#3498db';
    ctx.lineWidth = 3;
    ctx.beginPath();
    
    // Ideal linear line
    ctx.setLineDash([5, 5]);
    ctx.moveTo(margin, margin + plotHeight);
    ctx.lineTo(margin + plotWidth, margin);
    ctx.stroke();
    ctx.setLineDash([]);
    
    // Actual non-linear curve
    ctx.strokeStyle = '#e74c3c';
    ctx.beginPath();
    ctx.moveTo(margin, margin + plotHeight);
    
    for (let x = 0; x <= plotWidth; x += 5) {
        const normalizedX = x / plotWidth;
        const idealY = plotHeight * (1 - normalizedX);
        const deviation = 10 * Math.sin(normalizedX * Math.PI * 3);
        const actualY = idealY + deviation;
        ctx.lineTo(margin + x, margin + actualY);
    }
    ctx.stroke();
    
    // Add legend
    ctx.fillStyle = '#3498db';
    ctx.font = '12px Arial';
    ctx.fillText('Ideal Linear', margin + 10, margin + 20);
    ctx.fillStyle = '#e74c3c';
    ctx.fillText('Actual Response', margin + 10, margin + 35);
}

function drawHysteresisCurve(ctx, margin, plotWidth, plotHeight) {
    // Upward curve
    ctx.strokeStyle = '#27ae60';
    ctx.lineWidth = 3;
    ctx.beginPath();
    ctx.moveTo(margin, margin + plotHeight);
    
    for (let x = 0; x <= plotWidth; x += 5) {
        const normalizedX = x / plotWidth;
        const y = plotHeight * (1 - normalizedX) + 5;
        ctx.lineTo(margin + x, margin + y);
    }
    ctx.stroke();
    
    // Downward curve
    ctx.strokeStyle = '#e74c3c';
    ctx.beginPath();
    ctx.moveTo(margin + plotWidth, margin);
    
    for (let x = plotWidth; x >= 0; x -= 5) {
        const normalizedX = x / plotWidth;
        const y = plotHeight * (1 - normalizedX) - 5;
        ctx.lineTo(margin + x, margin + y);
    }
    ctx.stroke();
    
    // Add arrows and labels
    ctx.fillStyle = '#27ae60';
    ctx.font = '12px Arial';
    ctx.fillText('Increasing Input ↑', margin + 10, margin + 20);
    ctx.fillStyle = '#e74c3c';
    ctx.fillText('Decreasing Input ↓', margin + 10, margin + 35);
}

function drawResponseCurve(ctx, margin, plotWidth, plotHeight) {
    ctx.strokeStyle = '#9b59b6';
    ctx.lineWidth = 3;
    ctx.beginPath();
    
    // Exponential response curve
    ctx.moveTo(margin, margin + plotHeight);
    
    for (let x = 0; x <= plotWidth; x += 2) {
        const normalizedX = x / plotWidth;
        const y = plotHeight * (1 - (1 - Math.exp(-normalizedX * 5)));
        ctx.lineTo(margin + x, margin + y);
    }
    ctx.stroke();
    
    // Add time constant line
    const tau = plotWidth * 0.2; // 63% point
    ctx.strokeStyle = '#f39c12';
    ctx.setLineDash([3, 3]);
    ctx.beginPath();
    ctx.moveTo(margin + tau, margin);
    ctx.lineTo(margin + tau, margin + plotHeight);
    ctx.stroke();
    ctx.setLineDash([]);
    
    // Add labels
    ctx.fillStyle = '#9b59b6';
    ctx.font = '12px Arial';
    ctx.fillText('Response Curve', margin + 10, margin + 20);
    ctx.fillStyle = '#f39c12';
    ctx.fillText('τ (63%)', margin + tau - 10, margin + plotHeight + 15);
}

// Initialize temperature plot
function initializeTemperaturePlot() {
    const canvas = document.getElementById('temperatureCanvas');
    if (!canvas) return;
    
    const ctx = canvas.getContext('2d');
    drawTemperatureComparison(ctx, canvas.width, canvas.height, 'both');
}

function drawTemperatureComparison(ctx, width, height, type) {
    ctx.clearRect(0, 0, width, height);
    
    // Draw background
    ctx.fillStyle = '#f8f9fa';
    ctx.fillRect(0, 0, width, height);
    
    // Draw axes
    const margin = 40;
    const plotWidth = width - 2 * margin;
    const plotHeight = height - 2 * margin;
    
    ctx.strokeStyle = '#2c3e50';
    ctx.lineWidth = 2;
    ctx.beginPath();
    ctx.moveTo(margin, height - margin);
    ctx.lineTo(width - margin, height - margin);
    ctx.moveTo(margin, margin);
    ctx.lineTo(margin, height - margin);
    ctx.stroke();
    
    // Temperature range: 30-45°C
    const tempMin = 30;
    const tempMax = 45;
    
    if (type === 'thermistor' || type === 'both') {
        // Thermistor curve (exponential)
        ctx.strokeStyle = '#e74c3c';
        ctx.lineWidth = 3;
        ctx.beginPath();
        
        for (let x = 0; x <= plotWidth; x += 2) {
            const temp = tempMin + (tempMax - tempMin) * (x / plotWidth);
            const resistance = 10000 * Math.exp(-0.1 * (temp - 25)); // Simplified thermistor equation
            const normalizedR = Math.log(resistance / 1000) / Math.log(100); // Normalize for display
            const y = plotHeight * (1 - Math.max(0, Math.min(1, normalizedR)));
            
            if (x === 0) {
                ctx.moveTo(margin + x, margin + y);
            } else {
                ctx.lineTo(margin + x, margin + y);
            }
        }
        ctx.stroke();
    }
    
    if (type === 'rtd' || type === 'both') {
        // RTD curve (linear)
        ctx.strokeStyle = '#3498db';
        ctx.lineWidth = 3;
        ctx.beginPath();
        
        for (let x = 0; x <= plotWidth; x += 2) {
            const temp = tempMin + (tempMax - tempMin) * (x / plotWidth);
            const resistance = 100 * (1 + 0.00385 * (temp - 0)); // PT100 RTD
            const normalizedR = (resistance - 110) / (120 - 110); // Normalize for display
            const y = plotHeight * (1 - normalizedR);
            
            if (x === 0) {
                ctx.moveTo(margin + x, margin + y);
            } else {
                ctx.lineTo(margin + x, margin + y);
            }
        }
        ctx.stroke();
    }
    
    // Add labels
    ctx.fillStyle = '#2c3e50';
    ctx.font = '12px Arial';
    ctx.fillText('Temperature (°C)', width/2 - 40, height - 10);
    ctx.save();
    ctx.translate(15, height/2);
    ctx.rotate(-Math.PI / 2);
    ctx.fillText('Resistance', 0, 0);
    ctx.restore();
    
    // Add legend
    if (type === 'both') {
        ctx.fillStyle = '#e74c3c';
        ctx.fillText('Thermistor', margin + 10, margin + 20);
        ctx.fillStyle = '#3498db';
        ctx.fillText('RTD (PT100)', margin + 10, margin + 35);
    }
}

// Initialize Wheatstone bridge
function initializeWheatstoneBridge() {
    const bridgeDiagram = document.getElementById('wheatstoneDiagram');
    if (!bridgeDiagram) return;
    
    // Add interactive hover effects to bridge components
    const resistors = bridgeDiagram.querySelectorAll('rect');
    resistors.forEach(resistor => {
        resistor.addEventListener('mouseenter', function() {
            this.style.filter = 'brightness(1.2)';
            this.style.transform = 'scale(1.1)';
        });
        
        resistor.addEventListener('mouseleave', function() {
            this.style.filter = 'brightness(1)';
            this.style.transform = 'scale(1)';
        });
    });
}

// Interactive functions

function updateCharacteristicPlot() {
    const charSelect = document.getElementById('charSelect');
    const canvas = document.getElementById('characteristicsCanvas');
    
    if (!charSelect || !canvas) return;
    
    const selectedChar = charSelect.value;
    const ctx = canvas.getContext('2d');
    
    drawCharacteristicPlot(ctx, canvas.width, canvas.height, selectedChar);
    
    // Add visual feedback
    if (typeof gsap !== 'undefined') {
        gsap.from(canvas, {
            scale: 1.05,
            duration: 0.3,
            ease: "back.out(1.7)"
        });
    }
}

function showThermistorCurve() {
    const canvas = document.getElementById('temperatureCanvas');
    if (!canvas) return;
    
    const ctx = canvas.getContext('2d');
    drawTemperatureComparison(ctx, canvas.width, canvas.height, 'thermistor');
}

function showRTDCurve() {
    const canvas = document.getElementById('temperatureCanvas');
    if (!canvas) return;
    
    const ctx = canvas.getContext('2d');
    drawTemperatureComparison(ctx, canvas.width, canvas.height, 'rtd');
}

function showBothCurves() {
    const canvas = document.getElementById('temperatureCanvas');
    if (!canvas) return;
    
    const ctx = canvas.getContext('2d');
    drawTemperatureComparison(ctx, canvas.width, canvas.height, 'both');
}

// Add CSS for interactive elements
const style = document.createElement('style');
style.textContent = `
    .energy-flow {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 1rem;
        margin: 2rem 0;
    }
    
    .energy-type {
        background: linear-gradient(135deg, #3498db, #2980b9);
        color: white;
        padding: 1rem 2rem;
        border-radius: 8px;
        font-weight: bold;
        text-align: center;
        min-width: 120px;
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        transition: all 0.3s ease;
    }
    
    .energy-type:hover {
        transform: translateY(-3px);
        box-shadow: 0 6px 12px rgba(0,0,0,0.15);
    }
    
    .arrow {
        font-size: 2rem;
        color: #e74c3c;
        font-weight: bold;
    }
    
    .conversion-step {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 1rem;
    }
    
    .step-box {
        background: white;
        border-radius: 8px;
        padding: 1.5rem;
        text-align: center;
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        min-width: 200px;
        transition: all 0.3s ease;
    }
    
    .step-box:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 15px rgba(0,0,0,0.15);
    }
    
    .physiological { border-left: 4px solid #e74c3c; }
    .mechanical { border-left: 4px solid #f39c12; }
    .electrical { border-left: 4px solid #3498db; }
    .digital { border-left: 4px solid #27ae60; }
    
    .step-icon {
        font-size: 2rem;
        margin-bottom: 0.5rem;
        display: block;
    }
    
    .conversion-arrow {
        font-size: 2rem;
        color: #3498db;
        font-weight: bold;
    }
    
    .mechanism-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1.5rem;
        margin-top: 2rem;
    }
    
    .mechanism-item {
        background: white;
        border-radius: 8px;
        padding: 1.5rem;
        text-align: center;
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        transition: all 0.3s ease;
        cursor: pointer;
    }
    
    .mechanism-icon {
        font-size: 2.5rem;
        margin-bottom: 1rem;
        display: block;
        transition: transform 0.3s ease;
    }
    
    .mechanism-item h4 {
        color: #2c3e50;
        margin: 0.5rem 0;
    }
    
    .mechanism-item p {
        color: #7f8c8d;
        font-size: 0.9rem;
        margin-bottom: 1rem;
    }
    
    .examples {
        display: flex;
        flex-wrap: wrap;
        gap: 0.5rem;
        justify-content: center;
    }
    
    .example-tag {
        background: #ecf0f1;
        color: #2c3e50;
        padding: 0.25rem 0.5rem;
        border-radius: 4px;
        font-size: 0.8rem;
        font-weight: 500;
    }
    
    .characteristic-item {
        background: #f8f9fa;
        border-radius: 8px;
        padding: 1.5rem;
        margin: 1rem 0;
        border-left: 4px solid #3498db;
    }
    
    .char-definition p {
        margin: 0.5rem 0;
        line-height: 1.5;
    }
    
    .plot-container {
        background: white;
        border-radius: 8px;
        padding: 1rem;
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        margin: 1rem 0;
    }
    
    .plot-controls {
        text-align: center;
        margin: 1rem 0;
    }
    
    .plot-controls label {
        display: block;
        margin-bottom: 0.5rem;
        font-weight: bold;
        color: #2c3e50;
    }
    
    .plot-controls select {
        padding: 0.5rem;
        border: 2px solid #bdc3c7;
        border-radius: 4px;
        margin-bottom: 0.5rem;
        font-size: 1rem;
    }
    
    .specs-table {
        width: 100%;
        border-collapse: collapse;
        margin: 1rem 0;
        font-size: 0.9rem;
    }
    
    .specs-table th, .specs-table td {
        padding: 0.8rem;
        border: 1px solid #dee2e6;
        text-align: center;
    }
    
    .specs-table th {
        background: #3498db;
        color: white;
        font-weight: bold;
    }
    
    .specs-table tr:nth-child(even) {
        background: #f8f9fa;
    }
    
    .application-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 1.5rem;
        margin: 2rem 0;
    }
    
    .app-item {
        background: white;
        border-radius: 8px;
        padding: 1.5rem;
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        transition: all 0.3s ease;
    }
    
    .app-item:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 15px rgba(0,0,0,0.15);
    }
    
    .app-icon {
        font-size: 2.5rem;
        text-align: center;
        margin-bottom: 1rem;
    }
    
    .app-item h4 {
        color: #2c3e50;
        text-align: center;
        margin: 0.5rem 0;
    }
    
    .app-item p {
        color: #7f8c8d;
        text-align: center;
        line-height: 1.5;
        margin-bottom: 1rem;
    }
    
    .app-specs {
        display: flex;
        flex-wrap: wrap;
        gap: 0.5rem;
        justify-content: center;
    }
    
    .spec-tag {
        background: #e8f5e8;
        color: #27ae60;
        padding: 0.25rem 0.5rem;
        border-radius: 4px;
        font-size: 0.8rem;
        font-weight: 500;
    }
    
    .temp-controls {
        text-align: center;
        margin-top: 1rem;
    }
    
    .bridge-diagram svg {
        transition: all 0.3s ease;
    }
    
    .bridge-diagram rect {
        transition: all 0.3s ease;
        cursor: pointer;
    }
`;
document.head.appendChild(style);
