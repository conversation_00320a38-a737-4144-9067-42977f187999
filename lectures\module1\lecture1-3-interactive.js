// Lecture 1.3 Interactive Elements - Organ System Signals

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    console.log('Lecture 1.3 interactive elements loading...');
    
    // Initialize all interactive elements
    initializeOrganSystemVisualizations();
    initializeECGFormation();
    initializeEEGVisualization();
    initializeEMGFormation();
    
    console.log('Lecture 1.3 interactive elements loaded successfully');
});

// Initialize organ system visualizations
function initializeOrganSystemVisualizations() {
    // Animate organ system icons
    const systemIcons = document.querySelectorAll('.system-icon');
    systemIcons.forEach((icon, index) => {
        setTimeout(() => {
            if (typeof gsap !== 'undefined') {
                gsap.from(icon, {
                    scale: 0,
                    rotation: 360,
                    duration: 1,
                    ease: "back.out(1.7)"
                });
            }
        }, index * 300);
    });
}

// ECG Formation Visualization
function initializeECGFormation() {
    const canvas = document.getElementById('ecgFormationCanvas');
    if (!canvas) return;
    
    const ctx = canvas.getContext('2d');
    drawECGWaveform(ctx, canvas.width, canvas.height);
}

function drawECGWaveform(ctx, width, height) {
    ctx.clearRect(0, 0, width, height);
    
    // Draw grid
    drawECGGrid(ctx, width, height);
    
    // Draw ECG waveform
    ctx.strokeStyle = '#e74c3c';
    ctx.lineWidth = 3;
    ctx.beginPath();
    
    const baseY = height * 0.6;
    const timeScale = width / 1000; // 1000ms total
    
    // ECG waveform points (time in ms, amplitude in mV scaled to pixels)
    const ecgPoints = [
        {t: 0, v: 0},      // Baseline
        {t: 80, v: 0},     // Start P wave
        {t: 120, v: 15},   // P wave peak
        {t: 160, v: 0},    // End P wave
        {t: 200, v: 0},    // PR segment
        {t: 240, v: -5},   // Q wave
        {t: 260, v: 50},   // R wave peak
        {t: 280, v: -10},  // S wave
        {t: 320, v: 0},    // End QRS
        {t: 400, v: 0},    // ST segment
        {t: 500, v: 20},   // T wave peak
        {t: 600, v: 0},    // End T wave
        {t: 1000, v: 0}    // Baseline
    ];
    
    // Draw the waveform
    ctx.moveTo(0, baseY);
    for (let i = 0; i < ecgPoints.length; i++) {
        const x = ecgPoints[i].t * timeScale;
        const y = baseY - ecgPoints[i].v * 2; // Scale amplitude
        ctx.lineTo(x, y);
    }
    ctx.stroke();
    
    // Add wave labels
    ctx.fillStyle = '#2c3e50';
    ctx.font = 'bold 12px Arial';
    ctx.fillText('P', 120 * timeScale - 5, baseY - 20);
    ctx.fillText('Q', 240 * timeScale - 5, baseY + 15);
    ctx.fillText('R', 260 * timeScale - 5, baseY - 60);
    ctx.fillText('S', 280 * timeScale - 5, baseY + 25);
    ctx.fillText('T', 500 * timeScale - 5, baseY - 30);
}

function drawECGGrid(ctx, width, height) {
    ctx.strokeStyle = 'rgba(231, 76, 60, 0.2)';
    ctx.lineWidth = 0.5;
    
    // Vertical lines (time)
    for (let x = 0; x < width; x += 20) {
        ctx.beginPath();
        ctx.moveTo(x, 0);
        ctx.lineTo(x, height);
        ctx.stroke();
    }
    
    // Horizontal lines (voltage)
    for (let y = 0; y < height; y += 20) {
        ctx.beginPath();
        ctx.moveTo(0, y);
        ctx.lineTo(width, y);
        ctx.stroke();
    }
}

// EEG Visualization
function initializeEEGVisualization() {
    const canvas = document.getElementById('eegVisualizationCanvas');
    if (!canvas) return;
    
    const ctx = canvas.getContext('2d');
    drawEEGSignal(ctx, canvas.width, canvas.height, 'alpha');
}

function drawEEGSignal(ctx, width, height, bandType) {
    ctx.clearRect(0, 0, width, height);
    
    // Draw background
    ctx.fillStyle = '#f8f9fa';
    ctx.fillRect(0, 0, width, height);
    
    // EEG band parameters
    const bands = {
        delta: { freq: 2, amplitude: 30, color: '#9b59b6' },
        theta: { freq: 6, amplitude: 25, color: '#3498db' },
        alpha: { freq: 10, amplitude: 20, color: '#27ae60' },
        beta: { freq: 20, amplitude: 15, color: '#f39c12' },
        gamma: { freq: 40, amplitude: 10, color: '#e74c3c' }
    };
    
    const band = bands[bandType];
    const baseY = height / 2;
    
    ctx.strokeStyle = band.color;
    ctx.lineWidth = 2;
    ctx.beginPath();
    
    for (let x = 0; x < width; x++) {
        const t = (x / width) * 4 * Math.PI; // 4 seconds of data
        const y = baseY + band.amplitude * Math.sin(band.freq * t) * (0.8 + 0.2 * Math.random());
        
        if (x === 0) {
            ctx.moveTo(x, y);
        } else {
            ctx.lineTo(x, y);
        }
    }
    ctx.stroke();
    
    // Add frequency label
    ctx.fillStyle = band.color;
    ctx.font = 'bold 14px Arial';
    ctx.fillText(`${bandType.toUpperCase()} (${band.freq} Hz)`, 10, 20);
    
    // Add amplitude scale
    ctx.fillStyle = '#2c3e50';
    ctx.font = '10px Arial';
    ctx.fillText('50 μV', 5, baseY - band.amplitude - 5);
    ctx.fillText('0 μV', 5, baseY + 5);
    ctx.fillText('-50 μV', 5, baseY + band.amplitude + 15);
}

// EMG Formation Visualization
function initializeEMGFormation() {
    const canvas = document.getElementById('emgFormationCanvas');
    if (!canvas) return;
    
    const ctx = canvas.getContext('2d');
    drawEMGSignal(ctx, canvas.width, canvas.height);
}

function drawEMGSignal(ctx, width, height) {
    ctx.clearRect(0, 0, width, height);
    
    // Draw background
    ctx.fillStyle = '#f8f9fa';
    ctx.fillRect(0, 0, width, height);
    
    const baseY = height / 2;
    
    // Draw multiple motor unit action potentials
    ctx.strokeStyle = '#27ae60';
    ctx.lineWidth = 1.5;
    
    // Simulate EMG signal with multiple motor units
    for (let motorUnit = 0; motorUnit < 3; motorUnit++) {
        ctx.beginPath();
        const yOffset = (motorUnit - 1) * 30;
        const frequency = 15 + motorUnit * 10; // Different firing rates
        
        for (let x = 0; x < width; x++) {
            const t = (x / width) * 2 * Math.PI; // 2 seconds of data
            let y = baseY + yOffset;
            
            // Add motor unit action potentials
            const firingRate = frequency / 60; // Hz
            const spikes = Math.sin(firingRate * t * 2 * Math.PI);
            if (spikes > 0.7) {
                y += 40 * Math.exp(-Math.pow((x % 50 - 25) / 10, 2)); // Gaussian spike
            }
            
            // Add noise
            y += (Math.random() - 0.5) * 10;
            
            if (x === 0) {
                ctx.moveTo(x, y);
            } else {
                ctx.lineTo(x, y);
            }
        }
        ctx.stroke();
    }
    
    // Add labels
    ctx.fillStyle = '#27ae60';
    ctx.font = 'bold 12px Arial';
    ctx.fillText('Motor Unit 1', 10, baseY - 40);
    ctx.fillText('Motor Unit 2', 10, baseY - 10);
    ctx.fillText('Motor Unit 3', 10, baseY + 20);
    
    // Add composite EMG
    ctx.strokeStyle = '#e74c3c';
    ctx.lineWidth = 3;
    ctx.beginPath();
    
    for (let x = 0; x < width; x++) {
        const t = (x / width) * 2 * Math.PI;
        let y = baseY + 80; // Offset for composite signal
        
        // Sum of all motor units
        for (let mu = 0; mu < 3; mu++) {
            const freq = 15 + mu * 10;
            const firingRate = freq / 60;
            const spikes = Math.sin(firingRate * t * 2 * Math.PI);
            if (spikes > 0.7) {
                y += 15 * Math.exp(-Math.pow((x % 50 - 25) / 10, 2));
            }
        }
        
        // Add noise
        y += (Math.random() - 0.5) * 8;
        
        if (x === 0) {
            ctx.moveTo(x, y);
        } else {
            ctx.lineTo(x, y);
        }
    }
    ctx.stroke();
    
    ctx.fillStyle = '#e74c3c';
    ctx.font = 'bold 12px Arial';
    ctx.fillText('Composite EMG', 10, baseY + 100);
}

// Interactive Functions

function animateHeartConduction() {
    console.log('Animating heart conduction sequence...');
    
    const steps = document.querySelectorAll('.sequence-step');
    const nodes = document.querySelectorAll('#heartDiagram circle, #heartDiagram line, #heartDiagram path');
    
    // Reset all elements
    nodes.forEach(node => {
        node.style.opacity = '0.3';
    });
    
    steps.forEach((step, index) => {
        setTimeout(() => {
            // Highlight current step
            steps.forEach(s => s.style.backgroundColor = '');
            step.style.backgroundColor = '#e8f5e8';
            
            // Animate corresponding heart structure
            if (typeof gsap !== 'undefined') {
                gsap.to(step, {
                    scale: 1.05,
                    duration: 0.3,
                    yoyo: true,
                    repeat: 1
                });
            }
            
            // Reset highlighting after animation
            setTimeout(() => {
                step.style.backgroundColor = '';
            }, 1000);
        }, index * 800);
    });
}

function showECGFormation() {
    console.log('Showing ECG formation...');
    
    const canvas = document.getElementById('ecgFormationCanvas');
    if (!canvas) return;
    
    const ctx = canvas.getContext('2d');
    
    // Animate ECG formation step by step
    let currentPoint = 0;
    const animateECG = () => {
        if (currentPoint > 100) return;
        
        drawECGWaveform(ctx, canvas.width, canvas.height);
        
        // Add progress indicator
        const progressX = (currentPoint / 100) * canvas.width;
        ctx.strokeStyle = '#3498db';
        ctx.lineWidth = 2;
        ctx.beginPath();
        ctx.moveTo(progressX, 0);
        ctx.lineTo(progressX, canvas.height);
        ctx.stroke();
        
        currentPoint += 2;
        setTimeout(() => requestAnimationFrame(animateECG), 50);
    };
    
    animateECG();
}

function updateEEGVisualization() {
    const bandSelect = document.getElementById('eegBandSelect');
    const canvas = document.getElementById('eegVisualizationCanvas');
    
    if (!bandSelect || !canvas) return;
    
    const selectedBand = bandSelect.value;
    const ctx = canvas.getContext('2d');
    
    drawEEGSignal(ctx, canvas.width, canvas.height, selectedBand);
    
    // Add visual feedback
    if (typeof gsap !== 'undefined') {
        gsap.from(canvas, {
            scale: 1.05,
            duration: 0.3,
            ease: "back.out(1.7)"
        });
    }
}

function animateMotorUnitRecruitment() {
    console.log('Animating motor unit recruitment...');
    
    const unitTypes = document.querySelectorAll('.unit-type');
    
    unitTypes.forEach((unit, index) => {
        setTimeout(() => {
            unit.style.backgroundColor = '#e8f5e8';
            unit.style.transform = 'scale(1.05)';
            
            if (typeof gsap !== 'undefined') {
                gsap.from(unit, {
                    x: -20,
                    opacity: 0,
                    duration: 0.5,
                    ease: "power2.out"
                });
            }
            
            // Reset after animation
            setTimeout(() => {
                unit.style.backgroundColor = '';
                unit.style.transform = 'scale(1)';
            }, 1500);
        }, index * 600);
    });
}

function demonstrateEMGFormation() {
    console.log('Demonstrating EMG formation...');
    
    const canvas = document.getElementById('emgFormationCanvas');
    if (!canvas) return;
    
    const ctx = canvas.getContext('2d');
    
    // Animate EMG formation
    let animationStep = 0;
    const animateEMG = () => {
        if (animationStep > 100) return;
        
        drawEMGSignal(ctx, canvas.width, canvas.height);
        
        // Add progress indicator
        const progressX = (animationStep / 100) * canvas.width;
        ctx.strokeStyle = '#3498db';
        ctx.lineWidth = 2;
        ctx.setLineDash([5, 5]);
        ctx.beginPath();
        ctx.moveTo(progressX, 0);
        ctx.lineTo(progressX, canvas.height);
        ctx.stroke();
        ctx.setLineDash([]);
        
        animationStep += 1;
        setTimeout(() => requestAnimationFrame(animateEMG), 30);
    };
    
    animateEMG();
}

// Add CSS for interactive elements
const style = document.createElement('style');
style.textContent = `
    .system-icon {
        font-size: 3rem;
        margin: 0 1rem;
        display: inline-block;
        transition: transform 0.3s ease;
    }
    
    .system-icon:hover {
        transform: scale(1.2);
    }
    
    .hierarchy-levels {
        display: flex;
        flex-direction: column;
        gap: 1rem;
        align-items: center;
    }
    
    .level-item {
        display: flex;
        align-items: center;
        gap: 1rem;
        background: white;
        padding: 1.5rem;
        border-radius: 8px;
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        width: 100%;
        max-width: 400px;
        transition: all 0.3s ease;
    }
    
    .level-item:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 15px rgba(0,0,0,0.15);
    }
    
    .level-icon {
        font-size: 2rem;
        min-width: 50px;
        text-align: center;
    }
    
    .level-arrow {
        font-size: 2rem;
        color: #3498db;
        margin: 0.5rem 0;
    }
    
    .sequence-step {
        display: flex;
        align-items: center;
        gap: 1rem;
        padding: 0.8rem;
        margin: 0.5rem 0;
        border-radius: 6px;
        transition: all 0.3s ease;
        cursor: pointer;
    }
    
    .sequence-step:hover {
        background: #f8f9fa;
        transform: translateX(5px);
    }
    
    .step-number {
        background: #3498db;
        color: white;
        width: 30px;
        height: 30px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: bold;
        font-size: 0.9rem;
    }
    
    .rhythm-item {
        display: flex;
        justify-content: space-between;
        padding: 0.5rem;
        margin: 0.25rem 0;
        background: #f8f9fa;
        border-radius: 4px;
        border-left: 4px solid #3498db;
    }
    
    .rhythm-name {
        font-weight: bold;
        color: #2c3e50;
    }
    
    .rhythm-desc {
        color: #7f8c8d;
        font-size: 0.9rem;
    }
    
    .unit-type {
        background: #f8f9fa;
        padding: 1rem;
        margin: 0.5rem 0;
        border-radius: 6px;
        border-left: 4px solid #27ae60;
        transition: all 0.3s ease;
    }
    
    .unit-type h5 {
        color: #27ae60;
        margin: 0 0 0.5rem 0;
    }
    
    .ecg-table, .emg-table {
        width: 100%;
        border-collapse: collapse;
        margin: 1rem 0;
        font-size: 0.9rem;
    }
    
    .ecg-table th, .ecg-table td,
    .emg-table th, .emg-table td {
        padding: 0.5rem;
        border: 1px solid #dee2e6;
        text-align: center;
    }
    
    .ecg-table th, .emg-table th {
        background: #3498db;
        color: white;
        font-weight: bold;
    }
    
    .eeg-controls {
        margin: 1rem 0;
        text-align: center;
    }
    
    .eeg-controls label {
        display: block;
        margin-bottom: 0.5rem;
        font-weight: bold;
        color: #2c3e50;
    }
    
    .eeg-controls select {
        padding: 0.5rem;
        border: 2px solid #bdc3c7;
        border-radius: 4px;
        margin-bottom: 0.5rem;
        font-size: 1rem;
    }
    
    .node-label, .unit-label, .signal-label {
        font-size: 10px;
        fill: #2c3e50;
        font-weight: bold;
    }
`;
document.head.appendChild(style);
