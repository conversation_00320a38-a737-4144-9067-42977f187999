<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Lecture 2.3: Signal Conditioning in Biomedical Instrumentation</title>
    <link rel="stylesheet" href="../../css/style.css">
    <link rel="stylesheet" href="../module1/lecture-slides.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/gsap.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/3.9.1/chart.min.js"></script>
</head>
<body class="lecture-mode">
    <header class="lecture-header">
        <div class="container">
            <div class="lecture-navigation">
                <a href="../../index.html" class="nav-btn">🏠 Home</a>
                <a href="../../modules/module2/module2-description.html" class="nav-btn">📚 Module 2</a>
                <div class="lecture-info">
                    <span class="lecture-title">Lecture 2.3: Signal Conditioning</span>
                    <span class="slide-counter">Slide <span id="currentSlide">1</span> of <span id="totalSlides">14</span></span>
                </div>
                <div class="lecture-controls">
                    <button type="button" id="prevSlide" class="control-btn">⬅️ Previous</button>
                    <button type="button" id="nextSlide" class="control-btn">➡️ Next</button>
                    <button type="button" id="fullscreenBtn" class="control-btn">🔍 Fullscreen</button>
                </div>
            </div>
        </div>
    </header>

    <main class="lecture-content">
        <div class="slides-container" id="slidesContainer">
            
            <!-- Slide 1: Title Slide -->
            <div class="slide active" data-slide="1">
                <div class="slide-content title-slide">
                    <h1>📈 Signal Conditioning in Biomedical Instrumentation</h1>
                    <h2>Module 2 - Lecture 2.3</h2>
                    <div class="title-info">
                        <p><strong>Amplification, Filtering, and Isolation Techniques</strong></p>
                        <p>Preparing weak bioelectric signals for accurate measurement and analysis</p>
                    </div>
                    <div class="author-info">
                        <p><strong>Dr. Mohammed Yagoub Esmail</strong></p>
                        <p>Sudan University of Science and Technology (SUST)</p>
                        <p>Biomedical Engineering Department - 2025</p>
                    </div>
                    <div class="conditioning-preview" id="conditioningPreview">
                        <div class="signal-chain">
                            <div class="chain-step">📡 Electrode</div>
                            <div class="arrow">→</div>
                            <div class="chain-step">📈 Amplify</div>
                            <div class="arrow">→</div>
                            <div class="chain-step">🔧 Filter</div>
                            <div class="arrow">→</div>
                            <div class="chain-step">🛡️ Isolate</div>
                            <div class="arrow">→</div>
                            <div class="chain-step">💻 ADC</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Slide 2: Learning Objectives -->
            <div class="slide" data-slide="2">
                <div class="slide-content">
                    <h2>🎯 Learning Objectives</h2>
                    <div class="objectives-list">
                        <div class="objective-item" data-animate="1">
                            <div class="objective-icon">📈</div>
                            <div class="objective-text">
                                <h3>Master Amplification Techniques</h3>
                                <p>Understand differential amplifiers, instrumentation amplifiers, and gain considerations</p>
                            </div>
                        </div>
                        <div class="objective-item" data-animate="2">
                            <div class="objective-icon">🔧</div>
                            <div class="objective-text">
                                <h3>Design Filtering Systems</h3>
                                <p>Analyze active and passive filters for noise reduction and bandwidth control</p>
                            </div>
                        </div>
                        <div class="objective-item" data-animate="3">
                            <div class="objective-icon">🛡️</div>
                            <div class="objective-text">
                                <h3>Implement Isolation Techniques</h3>
                                <p>Study optical, magnetic, and capacitive isolation for patient safety</p>
                            </div>
                        </div>
                        <div class="objective-item" data-animate="4">
                            <div class="objective-icon">⚙️</div>
                            <div class="objective-text">
                                <h3>Optimize Signal Quality</h3>
                                <p>Learn noise reduction, artifact rejection, and signal-to-noise ratio improvement</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Slide 3: Instrumentation Amplifiers -->
            <div class="slide" data-slide="3">
                <div class="slide-content">
                    <h2>📈 Instrumentation Amplifiers</h2>
                    <div class="content-grid">
                        <div class="content-left">
                            <div class="amplifier-fundamentals">
                                <h3>🎯 Why Instrumentation Amplifiers?</h3>
                                <div class="requirements-list">
                                    <div class="requirement-item">
                                        <h4>🔍 High Input Impedance</h4>
                                        <p>• Minimize loading of signal source<br>
                                           • Typical: >10¹² Ω<br>
                                           • Preserve signal integrity</p>
                                    </div>
                                    <div class="requirement-item">
                                        <h4>🎚️ High CMRR</h4>
                                        <p>• Reject common-mode noise<br>
                                           • Typical: >100 dB<br>
                                           • Enhance differential signals</p>
                                    </div>
                                    <div class="requirement-item">
                                        <h4>📊 Low Noise</h4>
                                        <p>• Preserve weak bioelectric signals<br>
                                           • Typical: <1 μV RMS<br>
                                           • High signal-to-noise ratio</p>
                                    </div>
                                    <div class="requirement-item">
                                        <h4>🎛️ Adjustable Gain</h4>
                                        <p>• Single resistor gain control<br>
                                           • Range: 1-10,000<br>
                                           • Precise and stable</p>
                                    </div>
                                </div>
                                
                                <div class="three-opamp-config">
                                    <h4>🔧 Three Op-Amp Configuration</h4>
                                    <div class="config-advantages">
                                        <ul>
                                            <li>Excellent input impedance matching</li>
                                            <li>High common-mode rejection</li>
                                            <li>Single resistor gain control</li>
                                            <li>Low output impedance</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="content-right">
                            <div class="amplifier-circuit">
                                <h3>🔌 Instrumentation Amplifier Circuit</h3>
                                <div class="circuit-diagram" id="instAmpCircuit">
                                    <svg width="350" height="280" viewBox="0 0 350 280">
                                        <!-- Input stage op-amps -->
                                        <polygon points="50,80 50,120 90,100" fill="#3498db" stroke="#2980b9" stroke-width="2"/>
                                        <polygon points="50,160 50,200 90,180" fill="#3498db" stroke="#2980b9" stroke-width="2"/>
                                        
                                        <!-- Output stage op-amp -->
                                        <polygon points="200,130 200,170 240,150" fill="#e74c3c" stroke="#c0392b" stroke-width="2"/>
                                        
                                        <!-- Gain resistor -->
                                        <rect x="90" y="135" width="30" height="10" fill="#f39c12" stroke="#e67e22" stroke-width="2"/>
                                        <text x="105" y="143" text-anchor="middle" fill="white" font-size="8" font-weight="bold">Rg</text>
                                        
                                        <!-- Feedback resistors -->
                                        <rect x="160" y="115" width="25" height="8" fill="#27ae60" stroke="#229954" stroke-width="2"/>
                                        <rect x="160" y="177" width="25" height="8" fill="#27ae60" stroke="#229954" stroke-width="2"/>
                                        <text x="172" y="121" text-anchor="middle" fill="white" font-size="7">R2</text>
                                        <text x="172" y="183" text-anchor="middle" fill="white" font-size="7">R2</text>
                                        
                                        <!-- Input resistors -->
                                        <rect x="160" y="135" width="25" height="8" fill="#27ae60" stroke="#229954" stroke-width="2"/>
                                        <rect x="160" y="157" width="25" height="8" fill="#27ae60" stroke="#229954" stroke-width="2"/>
                                        <text x="172" y="141" text-anchor="middle" fill="white" font-size="7">R1</text>
                                        <text x="172" y="163" text-anchor="middle" fill="white" font-size="7">R1</text>
                                        
                                        <!-- Connections -->
                                        <!-- Input connections -->
                                        <line x1="20" y1="90" x2="50" y2="90" stroke="#2c3e50" stroke-width="2"/>
                                        <line x1="20" y1="190" x2="50" y2="190" stroke="#2c3e50" stroke-width="2"/>
                                        
                                        <!-- Gain resistor connections -->
                                        <line x1="90" y1="100" x2="90" y2="135" stroke="#2c3e50" stroke-width="2"/>
                                        <line x1="90" y1="180" x2="90" y2="145" stroke="#2c3e50" stroke-width="2"/>
                                        
                                        <!-- To output stage -->
                                        <line x1="90" y1="100" x2="160" y2="100" stroke="#2c3e50" stroke-width="2"/>
                                        <line x1="160" y1="100" x2="160" y2="115" stroke="#2c3e50" stroke-width="2"/>
                                        <line x1="90" y1="180" x2="160" y2="180" stroke="#2c3e50" stroke-width="2"/>
                                        <line x1="160" y1="180" x2="160" y2="185" stroke="#2c3e50" stroke-width="2"/>
                                        
                                        <!-- Output stage connections -->
                                        <line x1="185" y1="139" x2="200" y2="139" stroke="#2c3e50" stroke-width="2"/>
                                        <line x1="185" y1="161" x2="200" y2="161" stroke="#2c3e50" stroke-width="2"/>
                                        
                                        <!-- Feedback -->
                                        <line x1="240" y1="150" x2="260" y2="150" stroke="#2c3e50" stroke-width="2"/>
                                        <line x1="260" y1="150" x2="260" y2="119" stroke="#2c3e50" stroke-width="2"/>
                                        <line x1="260" y1="119" x2="185" y2="119" stroke="#2c3e50" stroke-width="2"/>
                                        
                                        <!-- Output -->
                                        <line x1="240" y1="150" x2="300" y2="150" stroke="#2c3e50" stroke-width="2"/>
                                        
                                        <!-- Labels -->
                                        <text x="10" y="85" font-size="12" font-weight="bold">V+</text>
                                        <text x="10" y="195" font-size="12" font-weight="bold">V-</text>
                                        <text x="310" y="155" font-size="12" font-weight="bold">Vout</text>
                                        
                                        <!-- Op-amp labels -->
                                        <text x="70" y="105" text-anchor="middle" font-size="8" fill="white">A1</text>
                                        <text x="70" y="185" text-anchor="middle" font-size="8" fill="white">A2</text>
                                        <text x="220" y="155" text-anchor="middle" font-size="8" fill="white">A3</text>
                                        
                                        <!-- Gain equation -->
                                        <text x="175" y="250" text-anchor="middle" font-size="14" font-weight="bold">Gain = (1 + 2R2/Rg) × (R2/R1)</text>
                                    </svg>
                                </div>
                                
                                <div class="gain-calculator">
                                    <h4>🧮 Gain Calculator</h4>
                                    <div class="calculator-inputs">
                                        <div class="input-group">
                                            <label for="r1Input">R1 (kΩ):</label>
                                            <input type="number" id="r1Input" value="10" min="1" max="100">
                                        </div>
                                        <div class="input-group">
                                            <label for="r2Input">R2 (kΩ):</label>
                                            <input type="number" id="r2Input" value="10" min="1" max="100">
                                        </div>
                                        <div class="input-group">
                                            <label for="rgInput">Rg (Ω):</label>
                                            <input type="number" id="rgInput" value="1000" min="10" max="10000">
                                        </div>
                                        <button type="button" class="demo-btn" onclick="calculateGain()">Calculate Gain</button>
                                    </div>
                                    <div class="gain-result" id="gainResult">
                                        <span class="result-label">Total Gain:</span>
                                        <span class="result-value">21</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Slide 4: Active Filters -->
            <div class="slide" data-slide="4">
                <div class="slide-content">
                    <h2>🔧 Active Filtering Systems</h2>
                    <div class="content-grid">
                        <div class="content-left">
                            <div class="filter-types">
                                <h3>📊 Filter Classifications</h3>
                                <div class="filter-category">
                                    <h4>🔽 Low-Pass Filters</h4>
                                    <div class="filter-details">
                                        <p><strong>Purpose:</strong> Remove high-frequency noise</p>
                                        <p><strong>Cutoff:</strong> 0.05-500 Hz (biomedical)</p>
                                        <p><strong>Applications:</strong> ECG, EMG baseline</p>
                                        <div class="filter-specs">
                                            <span class="spec-tag">Roll-off: 20-40 dB/decade</span>
                                            <span class="spec-tag">Order: 2-8</span>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="filter-category">
                                    <h4>🔼 High-Pass Filters</h4>
                                    <div class="filter-details">
                                        <p><strong>Purpose:</strong> Remove DC offset and drift</p>
                                        <p><strong>Cutoff:</strong> 0.01-1 Hz</p>
                                        <p><strong>Applications:</strong> AC coupling, baseline wander</p>
                                        <div class="filter-specs">
                                            <span class="spec-tag">Roll-off: 20-40 dB/decade</span>
                                            <span class="spec-tag">Order: 1-4</span>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="filter-category">
                                    <h4>🚫 Notch Filters</h4>
                                    <div class="filter-details">
                                        <p><strong>Purpose:</strong> Remove power line interference</p>
                                        <p><strong>Frequency:</strong> 50/60 Hz ± harmonics</p>
                                        <p><strong>Applications:</strong> Mains rejection</p>
                                        <div class="filter-specs">
                                            <span class="spec-tag">Q-factor: 10-50</span>
                                            <span class="spec-tag">Depth: >40 dB</span>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="filter-category">
                                    <h4>📊 Band-Pass Filters</h4>
                                    <div class="filter-details">
                                        <p><strong>Purpose:</strong> Select specific frequency range</p>
                                        <p><strong>Range:</strong> 0.5-100 Hz (ECG), 10-500 Hz (EMG)</p>
                                        <p><strong>Applications:</strong> Signal conditioning</p>
                                        <div class="filter-specs">
                                            <span class="spec-tag">Bandwidth: 0.1-1000 Hz</span>
                                            <span class="spec-tag">Selectivity: High</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="content-right">
                            <div class="filter-response">
                                <h3>📈 Filter Response Analysis</h3>
                                <div class="response-plot">
                                    <canvas id="filterResponseCanvas" width="320" height="200"></canvas>
                                </div>
                                
                                <div class="filter-controls">
                                    <div class="control-group">
                                        <label for="filterType">Filter Type:</label>
                                        <select id="filterType">
                                            <option value="lowpass">Low-Pass</option>
                                            <option value="highpass">High-Pass</option>
                                            <option value="bandpass">Band-Pass</option>
                                            <option value="notch">Notch</option>
                                        </select>
                                    </div>
                                    <div class="control-group">
                                        <label for="cutoffFreq">Cutoff (Hz):</label>
                                        <input type="number" id="cutoffFreq" value="100" min="1" max="1000">
                                    </div>
                                    <div class="control-group">
                                        <label for="filterOrder">Order:</label>
                                        <select id="filterOrder">
                                            <option value="1">1st Order</option>
                                            <option value="2" selected>2nd Order</option>
                                            <option value="4">4th Order</option>
                                        </select>
                                    </div>
                                    <button type="button" class="demo-btn" onclick="updateFilterResponse()">Update Response</button>
                                </div>
                                
                                <div class="sallen-key-circuit">
                                    <h4>🔧 Sallen-Key Topology</h4>
                                    <div class="circuit-advantages">
                                        <ul>
                                            <li>Unity gain buffer</li>
                                            <li>High input impedance</li>
                                            <li>Low output impedance</li>
                                            <li>Good stability</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Slide 5: Isolation Techniques -->
            <div class="slide" data-slide="5">
                <div class="slide-content">
                    <h2>🛡️ Electrical Isolation for Patient Safety</h2>
                    <div class="content-grid">
                        <div class="content-left">
                            <div class="isolation-importance">
                                <h3>⚠️ Why Isolation is Critical</h3>
                                <div class="safety-requirements">
                                    <div class="safety-item">
                                        <h4>🏥 Patient Safety</h4>
                                        <p>• Prevent electrical shock<br>
                                           • Leakage current <10 μA<br>
                                           • Ground loop elimination</p>
                                    </div>
                                    <div class="safety-item">
                                        <h4>📊 Signal Integrity</h4>
                                        <p>• Common-mode rejection<br>
                                           • Noise reduction<br>
                                           • Ground potential differences</p>
                                    </div>
                                    <div class="safety-item">
                                        <h4>⚡ Equipment Protection</h4>
                                        <p>• Surge protection<br>
                                           • Defibrillator immunity<br>
                                           • EMI/RFI rejection</p>
                                    </div>
                                </div>
                                
                                <div class="isolation-standards">
                                    <h4>📋 Safety Standards</h4>
                                    <div class="standard-item">
                                        <h5>IEC 60601-1</h5>
                                        <p>• Patient leakage: <10 μA<br>
                                           • Earth leakage: <500 μA<br>
                                           • Isolation voltage: >4000 V</p>
                                    </div>
                                    <div class="standard-item">
                                        <h5>UL 2601-1</h5>
                                        <p>• Type CF equipment<br>
                                           • Defibrillator protection<br>
                                           • 4000 V test voltage</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="content-right">
                            <div class="isolation-methods">
                                <h3>🔧 Isolation Technologies</h3>
                                <div class="isolation-type">
                                    <h4>💡 Optical Isolation</h4>
                                    <div class="method-details">
                                        <div class="principle">
                                            <h5>Operating Principle</h5>
                                            <p>LED → Photodetector coupling with complete electrical isolation</p>
                                        </div>
                                        <div class="characteristics">
                                            <h5>Characteristics</h5>
                                            <ul>
                                                <li>Isolation voltage: >5000 V</li>
                                                <li>Bandwidth: DC-10 MHz</li>
                                                <li>Linearity: ±0.1%</li>
                                                <li>Temperature stable</li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="isolation-type">
                                    <h4>🧲 Magnetic Isolation</h4>
                                    <div class="method-details">
                                        <div class="principle">
                                            <h5>Operating Principle</h5>
                                            <p>Transformer coupling with magnetic field transfer</p>
                                        </div>
                                        <div class="characteristics">
                                            <h5>Characteristics</h5>
                                            <ul>
                                                <li>Isolation voltage: >2500 V</li>
                                                <li>Bandwidth: 1 Hz-1 MHz</li>
                                                <li>High CMRR: >100 dB</li>
                                                <li>Low power consumption</li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="isolation-type">
                                    <h4>⚡ Capacitive Isolation</h4>
                                    <div class="method-details">
                                        <div class="principle">
                                            <h5>Operating Principle</h5>
                                            <p>Capacitive coupling through insulating barrier</p>
                                        </div>
                                        <div class="characteristics">
                                            <h5>Characteristics</h5>
                                            <ul>
                                                <li>Isolation voltage: >5000 V</li>
                                                <li>Bandwidth: 1 kHz-100 MHz</li>
                                                <li>Low distortion: <0.01%</li>
                                                <li>Fast response time</li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="isolation-comparison">
                                    <h4>📊 Comparison Matrix</h4>
                                    <canvas id="isolationCanvas" width="300" height="180"></canvas>
                                    <div class="comparison-controls">
                                        <button type="button" class="demo-btn" onclick="showIsolationBandwidth()">Bandwidth</button>
                                        <button type="button" class="demo-btn" onclick="showIsolationVoltage()">Voltage</button>
                                        <button type="button" class="demo-btn" onclick="showIsolationCost()">Cost</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Slide 6: Summary -->
            <div class="slide" data-slide="6">
                <div class="slide-content">
                    <h2>📋 Lecture Summary</h2>
                    <div class="summary-grid">
                        <div class="summary-section">
                            <h3>📈 Amplification</h3>
                            <ul>
                                <li>Instrumentation amplifier design</li>
                                <li>High input impedance requirements</li>
                                <li>Common-mode rejection ratio</li>
                                <li>Gain calculation and control</li>
                            </ul>
                        </div>
                        <div class="summary-section">
                            <h3>🔧 Filtering</h3>
                            <ul>
                                <li>Active filter topologies</li>
                                <li>Frequency response analysis</li>
                                <li>Noise reduction techniques</li>
                                <li>Biomedical filter specifications</li>
                            </ul>
                        </div>
                        <div class="summary-section">
                            <h3>🛡️ Isolation</h3>
                            <ul>
                                <li>Patient safety requirements</li>
                                <li>Optical isolation principles</li>
                                <li>Magnetic coupling methods</li>
                                <li>Safety standard compliance</li>
                            </ul>
                        </div>
                        <div class="summary-section">
                            <h3>⚙️ System Integration</h3>
                            <ul>
                                <li>Signal conditioning chain</li>
                                <li>Noise analysis and reduction</li>
                                <li>Performance optimization</li>
                                <li>Clinical application design</li>
                            </ul>
                        </div>
                    </div>
                    <div class="next-lecture">
                        <h3>🔜 Next Lecture: ADC Systems</h3>
                        <p>We'll explore analog-to-digital conversion, sampling theory, and quantization effects in biomedical signal digitization.</p>
                        <a href="lecture2-4.html" class="next-btn">➡️ Continue to Lecture 2.4</a>
                    </div>
                </div>
            </div>

        </div>
    </main>

    <footer class="lecture-footer">
        <div class="container">
            <div class="footer-content">
                <div class="author-info">
                    <p><strong>Dr. Mohammed Yagoub Esmail</strong> | SUST - BME 2025</p>
                    <p>📧 <EMAIL> | 📱 +249912867327, +966538076790</p>
                </div>
                <div class="copyright">
                    <p>&copy; 2025 Dr. Mohammed Yagoub Esmail - All rights reserved</p>
                </div>
            </div>
        </div>
    </footer>

    <script src="../module1/lecture-slides.js"></script>
    <script src="lecture2-3-interactive.js"></script>
</body>
</html>
