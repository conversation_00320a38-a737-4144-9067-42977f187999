/* Module 1 Specific Styles */

/* Learning Pathway Animation */
.learning-pathway-module1 {
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    padding: 2rem;
    border-radius: 12px;
    margin: 2rem 0;
    border-left: 5px solid #27ae60;
}

.pathway-steps {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-wrap: wrap;
    gap: 1rem;
    margin-top: 2rem;
}

.step-item {
    background: white;
    padding: 1.5rem;
    border-radius: 12px;
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    text-align: center;
    flex: 1;
    min-width: 180px;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    cursor: pointer;
}

.step-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 16px rgba(0,0,0,0.15);
}

.step-icon {
    font-size: 2.5rem;
    margin-bottom: 1rem;
    animation: pulse 2s infinite;
}

.step-connector {
    font-size: 2rem;
    color: #27ae60;
    font-weight: bold;
    animation: slideRight 1s ease-in-out infinite alternate;
}

/* Objectives Grid */
.objectives-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.objective-card {
    background: white;
    padding: 2rem;
    border-radius: 12px;
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    text-align: center;
    transition: transform 0.3s ease;
    border-top: 4px solid #3498db;
}

.objective-card:hover {
    transform: translateY(-5px);
}

.objective-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
    animation: bounce 2s infinite;
}

/* Lecture Cards */
.lectures-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.lecture-card {
    background: white;
    border-radius: 12px;
    box-shadow: 0 6px 12px rgba(0,0,0,0.1);
    overflow: hidden;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.lecture-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 12px 24px rgba(0,0,0,0.15);
}

.lecture-header {
    background: linear-gradient(135deg, #3498db, #2980b9);
    color: white;
    padding: 1.5rem;
    position: relative;
}

.lecture-number {
    background: rgba(255,255,255,0.2);
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: bold;
    display: inline-block;
    margin-bottom: 1rem;
}

.lecture-header h3 {
    color: white;
    margin: 0;
    font-size: 1.3rem;
}

.lecture-duration {
    background: rgba(255,255,255,0.1);
    padding: 0.3rem 0.8rem;
    border-radius: 15px;
    font-size: 0.8rem;
    margin-top: 0.5rem;
    display: inline-block;
}

.lecture-content {
    padding: 2rem;
}

.lecture-preview {
    height: 150px;
    background: #f8f9fa;
    border-radius: 8px;
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
}

/* Animated Cell */
.animated-cell {
    width: 120px;
    height: 80px;
    position: relative;
}

.cell-membrane {
    width: 100%;
    height: 100%;
    border: 3px solid #e74c3c;
    border-radius: 50px;
    position: relative;
    animation: cellPulse 3s ease-in-out infinite;
}

.ion-channel {
    width: 20px;
    height: 20px;
    background: #3498db;
    border-radius: 50%;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    animation: channelGlow 2s ease-in-out infinite;
}

.ions {
    position: absolute;
    width: 8px;
    height: 8px;
    background: #f39c12;
    border-radius: 50%;
    animation: ionFlow 2s linear infinite;
}

/* Action Potential Graph */
.action-potential-graph {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}

#apCanvas {
    border: 1px solid #ddd;
    border-radius: 4px;
}

/* Organ System Diagram */
.organ-system-diagram {
    display: flex;
    align-items: center;
    justify-content: space-around;
    height: 100%;
    font-size: 3rem;
}

.heart-icon, .brain-icon, .muscle-icon {
    animation: organPulse 2s ease-in-out infinite;
    cursor: pointer;
    transition: transform 0.3s ease;
}

.heart-icon:hover, .brain-icon:hover, .muscle-icon:hover {
    transform: scale(1.2);
}

/* Safety Icons */
.safety-icons {
    display: flex;
    align-items: center;
    justify-content: space-around;
    height: 100%;
    font-size: 3rem;
}

.safety-icon, .regulation-icon, .standard-icon {
    animation: safetyBlink 3s ease-in-out infinite;
}

/* Lecture Actions */
.lecture-actions {
    display: flex;
    gap: 1rem;
    margin-top: 1.5rem;
}

.btn-primary, .btn-secondary {
    padding: 0.8rem 1.5rem;
    border: none;
    border-radius: 6px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
}

.btn-primary {
    background: linear-gradient(135deg, #3498db, #2980b9);
    color: white;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #2980b9, #1f5f8b);
    transform: translateY(-2px);
}

.btn-secondary {
    background: linear-gradient(135deg, #95a5a6, #7f8c8d);
    color: white;
}

.btn-secondary:hover {
    background: linear-gradient(135deg, #7f8c8d, #6c7b7d);
    transform: translateY(-2px);
}

/* Block Diagram */
.block-diagram-container {
    background: white;
    padding: 2rem;
    border-radius: 12px;
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    margin: 2rem 0;
}

.interactive-block-diagram {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-wrap: wrap;
    gap: 1rem;
    margin-bottom: 2rem;
}

.block-item {
    background: linear-gradient(135deg, #3498db, #2980b9);
    color: white;
    padding: 1.5rem;
    border-radius: 12px;
    text-align: center;
    min-width: 120px;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
}

.block-item:hover {
    transform: scale(1.05);
    box-shadow: 0 8px 16px rgba(52, 152, 219, 0.3);
}

.block-item.active {
    background: linear-gradient(135deg, #e74c3c, #c0392b);
    animation: blockPulse 1s ease-in-out;
}

.block-icon {
    font-size: 2rem;
    margin-bottom: 0.5rem;
}

.block-arrow {
    font-size: 2rem;
    color: #3498db;
    font-weight: bold;
    animation: arrowFlow 2s ease-in-out infinite;
}

/* Schematic Diagram */
.schematic-diagram-container {
    background: white;
    padding: 2rem;
    border-radius: 12px;
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    margin: 2rem 0;
}

.interactive-schematic {
    display: flex;
    justify-content: center;
    margin-bottom: 2rem;
}

.component {
    cursor: pointer;
    transition: all 0.3s ease;
}

.component:hover {
    transform: scale(1.1);
}

.component-label {
    font-size: 14px;
    font-weight: bold;
    fill: #2c3e50;
}

.component-value {
    font-size: 12px;
    fill: #7f8c8d;
}

.voltage-label {
    font-size: 16px;
    font-weight: bold;
    fill: #e74c3c;
}

/* Interactive Tools */
.interactive-tools-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.tool-card {
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    overflow: hidden;
}

.tool-card h3 {
    background: linear-gradient(135deg, #9b59b6, #8e44ad);
    color: white;
    padding: 1rem;
    margin: 0;
    text-align: center;
}

.tool-preview {
    padding: 2rem;
    background: #f8f9fa;
    height: 200px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.tool-controls {
    padding: 1.5rem;
    background: white;
}

.tool-controls label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: bold;
    color: #2c3e50;
}

.tool-controls input[type="range"] {
    width: 100%;
    margin-bottom: 1rem;
}

.tool-controls button {
    margin-right: 0.5rem;
    margin-bottom: 0.5rem;
}

/* Channel Simulator */
.channel-simulator {
    width: 300px;
    height: 150px;
    position: relative;
    background: linear-gradient(135deg, #ecf0f1, #bdc3c7);
    border-radius: 8px;
    overflow: hidden;
}

.membrane-representation {
    width: 100%;
    height: 100%;
    position: relative;
    border: 2px solid #34495e;
    border-radius: 8px;
}

.ion {
    position: absolute;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 10px;
    font-weight: bold;
    color: white;
}

.na-ion {
    background: #e74c3c;
    top: 20px;
    left: 50px;
    animation: ionMovement 3s ease-in-out infinite;
}

.k-ion {
    background: #9b59b6;
    top: 60px;
    left: 200px;
    animation: ionMovement 3s ease-in-out infinite reverse;
}

.cl-ion {
    background: #f39c12;
    top: 100px;
    left: 120px;
    animation: ionMovement 2s ease-in-out infinite;
}

.channel-gate {
    position: absolute;
    width: 30px;
    height: 10px;
    background: #3498db;
    border-radius: 5px;
    font-size: 8px;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

#naGate {
    top: 40px;
    left: 135px;
}

#kGate {
    top: 80px;
    left: 135px;
}

/* Assessment Styles */
.assessment-overview {
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: 3rem;
    margin: 2rem 0;
}

.assessment-breakdown {
    background: white;
    padding: 2rem;
    border-radius: 12px;
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    text-align: center;
}

.assessment-details {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.assessment-item {
    background: white;
    padding: 1.5rem;
    border-radius: 12px;
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    border-left: 4px solid #3498db;
}

.assessment-item h4 {
    color: #2c3e50;
    margin-bottom: 1rem;
}

.assessment-item ul {
    list-style-type: none;
    padding-left: 0;
}

.assessment-item li {
    padding: 0.3rem 0;
    padding-left: 1.5rem;
    position: relative;
}

.assessment-item li:before {
    content: "✓";
    position: absolute;
    left: 0;
    color: #27ae60;
    font-weight: bold;
}

/* Quick Quiz */
.quick-assessment {
    background: white;
    padding: 2rem;
    border-radius: 12px;
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    margin-top: 2rem;
}

.quiz-container {
    margin: 1.5rem 0;
}

.question h4 {
    color: #2c3e50;
    margin-bottom: 1rem;
}

.options {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1rem;
}

.option {
    padding: 0.8rem 1rem;
    border: 2px solid #bdc3c7;
    background: white;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.option:hover {
    border-color: #3498db;
    background: #ecf0f1;
}

.option.correct {
    border-color: #27ae60;
    background: #d5f4e6;
    color: #27ae60;
}

.option.wrong {
    border-color: #e74c3c;
    background: #fadbd8;
    color: #e74c3c;
}

/* Animations */
@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
    40% { transform: translateY(-10px); }
    60% { transform: translateY(-5px); }
}

@keyframes slideRight {
    0% { transform: translateX(0); }
    100% { transform: translateX(10px); }
}

@keyframes cellPulse {
    0%, 100% { transform: scale(1); border-color: #e74c3c; }
    50% { transform: scale(1.05); border-color: #3498db; }
}

@keyframes channelGlow {
    0%, 100% { box-shadow: 0 0 5px #3498db; }
    50% { box-shadow: 0 0 15px #3498db, 0 0 25px #3498db; }
}

@keyframes ionFlow {
    0% { transform: translateX(-20px); opacity: 0; }
    50% { opacity: 1; }
    100% { transform: translateX(20px); opacity: 0; }
}

@keyframes organPulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

@keyframes safetyBlink {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.6; }
}

@keyframes blockPulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

@keyframes arrowFlow {
    0%, 100% { transform: translateX(0); opacity: 0.7; }
    50% { transform: translateX(10px); opacity: 1; }
}

@keyframes ionMovement {
    0%, 100% { transform: translateX(0); }
    50% { transform: translateX(20px); }
}

/* Responsive Design */
@media (max-width: 768px) {
    .pathway-steps {
        flex-direction: column;
    }
    
    .step-connector {
        transform: rotate(90deg);
        margin: 1rem 0;
    }
    
    .objectives-grid {
        grid-template-columns: 1fr;
    }
    
    .lectures-grid {
        grid-template-columns: 1fr;
    }
    
    .interactive-block-diagram {
        flex-direction: column;
    }
    
    .block-arrow {
        transform: rotate(90deg);
        margin: 1rem 0;
    }
    
    .assessment-overview {
        grid-template-columns: 1fr;
    }
    
    .interactive-tools-grid {
        grid-template-columns: 1fr;
    }
}
