<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Lecture 2.1: Transduction Principles in Biomedical Instrumentation</title>
    <link rel="stylesheet" href="../../css/style.css">
    <link rel="stylesheet" href="../module1/lecture-slides.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/gsap.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/3.9.1/chart.min.js"></script>
</head>
<body class="lecture-mode">
    <header class="lecture-header">
        <div class="container">
            <div class="lecture-navigation">
                <a href="../../index.html" class="nav-btn">🏠 Home</a>
                <a href="../../modules/module2/module2-description.html" class="nav-btn">📚 Module 2</a>
                <div class="lecture-info">
                    <span class="lecture-title">Lecture 2.1: Transduction Principles</span>
                    <span class="slide-counter">Slide <span id="currentSlide">1</span> of <span id="totalSlides">15</span></span>
                </div>
                <div class="lecture-controls">
                    <button type="button" id="prevSlide" class="control-btn">⬅️ Previous</button>
                    <button type="button" id="nextSlide" class="control-btn">➡️ Next</button>
                    <button type="button" id="fullscreenBtn" class="control-btn">🔍 Fullscreen</button>
                </div>
            </div>
        </div>
    </header>

    <main class="lecture-content">
        <div class="slides-container" id="slidesContainer">
            
            <!-- Slide 1: Title Slide -->
            <div class="slide active" data-slide="1">
                <div class="slide-content title-slide">
                    <h1>🔄 Transduction Principles in Biomedical Instrumentation</h1>
                    <h2>Module 2 - Lecture 2.1</h2>
                    <div class="title-info">
                        <p><strong>Energy Conversion in Medical Sensing Systems</strong></p>
                        <p>From physiological phenomena to measurable electrical signals</p>
                    </div>
                    <div class="author-info">
                        <p><strong>Dr. Mohammed Yagoub Esmail</strong></p>
                        <p>Sudan University of Science and Technology (SUST)</p>
                        <p>Biomedical Engineering Department - 2025</p>
                    </div>
                    <div class="transduction-preview" id="transductionPreview">
                        <div class="energy-flow">
                            <div class="energy-type">🫀 Physiological</div>
                            <div class="arrow">→</div>
                            <div class="energy-type">🔄 Transducer</div>
                            <div class="arrow">→</div>
                            <div class="energy-type">⚡ Electrical</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Slide 2: Learning Objectives -->
            <div class="slide" data-slide="2">
                <div class="slide-content">
                    <h2>🎯 Learning Objectives</h2>
                    <div class="objectives-list">
                        <div class="objective-item" data-animate="1">
                            <div class="objective-icon">🔄</div>
                            <div class="objective-text">
                                <h3>Understand Transduction Fundamentals</h3>
                                <p>Master the principles of energy conversion from physiological phenomena to electrical signals</p>
                            </div>
                        </div>
                        <div class="objective-item" data-animate="2">
                            <div class="objective-icon">📊</div>
                            <div class="objective-text">
                                <h3>Analyze Transducer Characteristics</h3>
                                <p>Study sensitivity, linearity, hysteresis, and frequency response of biomedical transducers</p>
                            </div>
                        </div>
                        <div class="objective-item" data-animate="3">
                            <div class="objective-icon">🔬</div>
                            <div class="objective-text">
                                <h3>Explore Transducer Types</h3>
                                <p>Examine resistive, capacitive, inductive, and piezoelectric transduction mechanisms</p>
                            </div>
                        </div>
                        <div class="objective-item" data-animate="4">
                            <div class="objective-icon">⚙️</div>
                            <div class="objective-text">
                                <h3>Apply Selection Criteria</h3>
                                <p>Learn to select appropriate transducers for specific biomedical applications</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Slide 3: Transduction Fundamentals -->
            <div class="slide" data-slide="3">
                <div class="slide-content">
                    <h2>🔄 Fundamentals of Biomedical Transduction</h2>
                    <div class="content-grid">
                        <div class="content-left">
                            <div class="transduction-concept">
                                <h3>🎯 What is Transduction?</h3>
                                <div class="definition-box">
                                    <p><strong>Transduction:</strong> The conversion of one form of energy into another, specifically converting physiological phenomena into measurable electrical signals for medical instrumentation.</p>
                                </div>
                                
                                <div class="energy-conversion-chain">
                                    <h4>🔗 Energy Conversion Chain</h4>
                                    <div class="conversion-step">
                                        <div class="step-box physiological">
                                            <span class="step-icon">🫀</span>
                                            <h5>Physiological Phenomenon</h5>
                                            <p>Pressure, temperature, displacement, flow, electrical activity</p>
                                        </div>
                                        <div class="conversion-arrow">⬇️</div>
                                        <div class="step-box mechanical">
                                            <span class="step-icon">⚙️</span>
                                            <h5>Mechanical/Physical Change</h5>
                                            <p>Deformation, movement, resistance change</p>
                                        </div>
                                        <div class="conversion-arrow">⬇️</div>
                                        <div class="step-box electrical">
                                            <span class="step-icon">⚡</span>
                                            <h5>Electrical Signal</h5>
                                            <p>Voltage, current, resistance, capacitance</p>
                                        </div>
                                        <div class="conversion-arrow">⬇️</div>
                                        <div class="step-box digital">
                                            <span class="step-icon">💻</span>
                                            <h5>Digital Processing</h5>
                                            <p>ADC, signal processing, display</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="content-right">
                            <div class="transduction-types">
                                <h3>🔬 Primary Transduction Mechanisms</h3>
                                <div class="mechanism-grid">
                                    <div class="mechanism-item" data-type="resistive">
                                        <div class="mechanism-icon">🔧</div>
                                        <h4>Resistive</h4>
                                        <p>Change in electrical resistance</p>
                                        <div class="examples">
                                            <span class="example-tag">Strain gauges</span>
                                            <span class="example-tag">Thermistors</span>
                                            <span class="example-tag">RTDs</span>
                                        </div>
                                    </div>
                                    
                                    <div class="mechanism-item" data-type="capacitive">
                                        <div class="mechanism-icon">⚡</div>
                                        <h4>Capacitive</h4>
                                        <p>Change in capacitance</p>
                                        <div class="examples">
                                            <span class="example-tag">Pressure sensors</span>
                                            <span class="example-tag">Displacement</span>
                                            <span class="example-tag">Proximity</span>
                                        </div>
                                    </div>
                                    
                                    <div class="mechanism-item" data-type="inductive">
                                        <div class="mechanism-icon">🌀</div>
                                        <h4>Inductive</h4>
                                        <p>Change in inductance</p>
                                        <div class="examples">
                                            <span class="example-tag">LVDT</span>
                                            <span class="example-tag">Position sensors</span>
                                            <span class="example-tag">Flow meters</span>
                                        </div>
                                    </div>
                                    
                                    <div class="mechanism-item" data-type="piezoelectric">
                                        <div class="mechanism-icon">💎</div>
                                        <h4>Piezoelectric</h4>
                                        <p>Mechanical stress → voltage</p>
                                        <div class="examples">
                                            <span class="example-tag">Ultrasound</span>
                                            <span class="example-tag">Accelerometers</span>
                                            <span class="example-tag">Force sensors</span>
                                        </div>
                                    </div>
                                    
                                    <div class="mechanism-item" data-type="optical">
                                        <div class="mechanism-icon">💡</div>
                                        <h4>Optical</h4>
                                        <p>Light intensity/wavelength change</p>
                                        <div class="examples">
                                            <span class="example-tag">Pulse oximetry</span>
                                            <span class="example-tag">Fiber optic</span>
                                            <span class="example-tag">Spectroscopy</span>
                                        </div>
                                    </div>
                                    
                                    <div class="mechanism-item" data-type="electrochemical">
                                        <div class="mechanism-icon">🧪</div>
                                        <h4>Electrochemical</h4>
                                        <p>Chemical → electrical</p>
                                        <div class="examples">
                                            <span class="example-tag">pH electrodes</span>
                                            <span class="example-tag">Ion sensors</span>
                                            <span class="example-tag">Glucose meters</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Slide 4: Transducer Characteristics -->
            <div class="slide" data-slide="4">
                <div class="slide-content">
                    <h2>📊 Transducer Performance Characteristics</h2>
                    <div class="content-grid">
                        <div class="content-left">
                            <div class="characteristics-overview">
                                <h3>🔍 Key Performance Parameters</h3>
                                <div class="characteristic-item">
                                    <h4>📈 Sensitivity</h4>
                                    <div class="char-definition">
                                        <p><strong>Definition:</strong> Output change per unit input change</p>
                                        <p><strong>Formula:</strong> S = ΔOutput / ΔInput</p>
                                        <p><strong>Units:</strong> V/mmHg, mV/°C, Ω/strain</p>
                                    </div>
                                </div>
                                
                                <div class="characteristic-item">
                                    <h4>📏 Linearity</h4>
                                    <div class="char-definition">
                                        <p><strong>Definition:</strong> Degree to which output follows input linearly</p>
                                        <p><strong>Measure:</strong> Maximum deviation from best-fit line</p>
                                        <p><strong>Expression:</strong> % of full scale</p>
                                    </div>
                                </div>
                                
                                <div class="characteristic-item">
                                    <h4>🔄 Hysteresis</h4>
                                    <div class="char-definition">
                                        <p><strong>Definition:</strong> Output difference for same input (up vs down)</p>
                                        <p><strong>Cause:</strong> Internal friction, magnetic domains</p>
                                        <p><strong>Impact:</strong> Measurement uncertainty</p>
                                    </div>
                                </div>
                                
                                <div class="characteristic-item">
                                    <h4>⏱️ Response Time</h4>
                                    <div class="char-definition">
                                        <p><strong>Definition:</strong> Time to reach 63% of final value</p>
                                        <p><strong>Types:</strong> Rise time, settling time</p>
                                        <p><strong>Importance:</strong> Dynamic measurements</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="content-right">
                            <div class="characteristics-visualization">
                                <h3>📊 Interactive Characteristic Plots</h3>
                                <div class="plot-container">
                                    <canvas id="characteristicsCanvas" width="350" height="250"></canvas>
                                </div>
                                
                                <div class="plot-controls">
                                    <label for="charSelect">Select Characteristic:</label>
                                    <select id="charSelect">
                                        <option value="sensitivity">Sensitivity</option>
                                        <option value="linearity">Linearity</option>
                                        <option value="hysteresis">Hysteresis</option>
                                        <option value="response">Response Time</option>
                                    </select>
                                    <button type="button" class="demo-btn" onclick="updateCharacteristicPlot()">Update Plot</button>
                                </div>
                                
                                <div class="specification-table">
                                    <h4>📋 Typical Specifications</h4>
                                    <table class="specs-table">
                                        <thead>
                                            <tr>
                                                <th>Parameter</th>
                                                <th>Typical Range</th>
                                                <th>Desired</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td>Sensitivity</td>
                                                <td>1-100 mV/unit</td>
                                                <td>High</td>
                                            </tr>
                                            <tr>
                                                <td>Linearity</td>
                                                <td>±0.1-5% FS</td>
                                                <td>±0.1% FS</td>
                                            </tr>
                                            <tr>
                                                <td>Hysteresis</td>
                                                <td>±0.05-2% FS</td>
                                                <td>Minimal</td>
                                            </tr>
                                            <tr>
                                                <td>Response Time</td>
                                                <td>1ms-10s</td>
                                                <td>Fast</td>
                                            </tr>
                                            <tr>
                                                <td>Drift</td>
                                                <td>±0.01-1%/°C</td>
                                                <td>Low</td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Slide 5: Resistive Transducers -->
            <div class="slide" data-slide="5">
                <div class="slide-content">
                    <h2>🔧 Resistive Transducers</h2>
                    <div class="content-grid">
                        <div class="content-left">
                            <div class="resistive-principles">
                                <h3>⚡ Operating Principles</h3>
                                <div class="principle-explanation">
                                    <p>Resistive transducers operate on the principle that the electrical resistance of a material changes in response to physical stimuli such as strain, temperature, or pressure.</p>
                                </div>
                                
                                <div class="resistance-equation">
                                    <h4>📐 Fundamental Equation</h4>
                                    <div class="equation-box">
                                        <div class="equation-main">R = ρL/A</div>
                                        <div class="equation-variables">
                                            <p>R = Resistance (Ω)</p>
                                            <p>ρ = Resistivity (Ω·m)</p>
                                            <p>L = Length (m)</p>
                                            <p>A = Cross-sectional area (m²)</p>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="strain-gauge-detail">
                                    <h4>📏 Strain Gauge Analysis</h4>
                                    <div class="gauge-factor">
                                        <p><strong>Gauge Factor (GF):</strong></p>
                                        <div class="equation-box">
                                            <div class="equation-main">GF = (ΔR/R) / (ΔL/L)</div>
                                            <p>Typical values: 2-4 for metallic gauges</p>
                                        </div>
                                    </div>
                                    
                                    <div class="wheatstone-bridge">
                                        <h5>🌉 Wheatstone Bridge Configuration</h5>
                                        <div class="bridge-diagram" id="wheatstoneDiagram">
                                            <svg width="200" height="200" viewBox="0 0 200 200">
                                                <!-- Bridge resistors -->
                                                <rect x="80" y="20" width="40" height="15" fill="#e74c3c" stroke="#c0392b" stroke-width="2"/>
                                                <text x="100" y="32" text-anchor="middle" fill="white" font-size="10" font-weight="bold">R1</text>
                                                
                                                <rect x="150" y="80" width="15" height="40" fill="#3498db" stroke="#2980b9" stroke-width="2"/>
                                                <text x="157" y="103" text-anchor="middle" fill="white" font-size="10" font-weight="bold">R2</text>
                                                
                                                <rect x="80" y="165" width="40" height="15" fill="#27ae60" stroke="#229954" stroke-width="2"/>
                                                <text x="100" y="177" text-anchor="middle" fill="white" font-size="10" font-weight="bold">R3</text>
                                                
                                                <rect x="35" y="80" width="15" height="40" fill="#f39c12" stroke="#e67e22" stroke-width="2"/>
                                                <text x="42" y="103" text-anchor="middle" fill="white" font-size="10" font-weight="bold">R4</text>
                                                
                                                <!-- Connections -->
                                                <line x1="100" y1="35" x2="100" y2="80" stroke="#2c3e50" stroke-width="2"/>
                                                <line x1="100" y1="120" x2="100" y2="165" stroke="#2c3e50" stroke-width="2"/>
                                                <line x1="50" y1="100" x2="80" y2="100" stroke="#2c3e50" stroke-width="2"/>
                                                <line x1="120" y1="100" x2="150" y2="100" stroke="#2c3e50" stroke-width="2"/>
                                                
                                                <!-- Voltage points -->
                                                <circle cx="100" cy="80" r="3" fill="#2c3e50"/>
                                                <circle cx="100" cy="120" r="3" fill="#2c3e50"/>
                                                <text x="110" y="85" font-size="10">A</text>
                                                <text x="110" y="125" font-size="10">B</text>
                                                
                                                <!-- Supply voltage -->
                                                <text x="20" y="15" font-size="12" font-weight="bold">+Vs</text>
                                                <text x="20" y="195" font-size="12" font-weight="bold">-Vs</text>
                                                
                                                <!-- Output -->
                                                <text x="170" y="105" font-size="10" font-weight="bold">Vout</text>
                                            </svg>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="content-right">
                            <div class="resistive-applications">
                                <h3>🏥 Biomedical Applications</h3>
                                <div class="application-grid">
                                    <div class="app-item">
                                        <div class="app-icon">🫁</div>
                                        <h4>Respiratory Monitoring</h4>
                                        <p>Strain gauges on chest belts measure breathing patterns and respiratory rate</p>
                                        <div class="app-specs">
                                            <span class="spec-tag">Range: ±20% strain</span>
                                            <span class="spec-tag">Sensitivity: 2-4 GF</span>
                                        </div>
                                    </div>
                                    
                                    <div class="app-item">
                                        <div class="app-icon">🩸</div>
                                        <h4>Blood Pressure</h4>
                                        <p>Pressure-sensitive resistors in cuff-based sphygmomanometers</p>
                                        <div class="app-specs">
                                            <span class="spec-tag">Range: 0-300 mmHg</span>
                                            <span class="spec-tag">Accuracy: ±2 mmHg</span>
                                        </div>
                                    </div>
                                    
                                    <div class="app-item">
                                        <div class="app-icon">🌡️</div>
                                        <h4>Temperature Sensing</h4>
                                        <p>Thermistors and RTDs for body temperature monitoring</p>
                                        <div class="app-specs">
                                            <span class="spec-tag">Range: 30-45°C</span>
                                            <span class="spec-tag">Accuracy: ±0.1°C</span>
                                        </div>
                                    </div>
                                    
                                    <div class="app-item">
                                        <div class="app-icon">🦴</div>
                                        <h4>Force/Load Cells</h4>
                                        <p>Strain gauge-based force measurement in prosthetics and rehabilitation</p>
                                        <div class="app-specs">
                                            <span class="spec-tag">Range: 0-1000N</span>
                                            <span class="spec-tag">Resolution: 0.1N</span>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="temperature-characteristics">
                                    <h4>🌡️ Temperature Sensor Comparison</h4>
                                    <canvas id="temperatureCanvas" width="300" height="200"></canvas>
                                    <div class="temp-controls">
                                        <button type="button" class="demo-btn" onclick="showThermistorCurve()">Thermistor</button>
                                        <button type="button" class="demo-btn" onclick="showRTDCurve()">RTD</button>
                                        <button type="button" class="demo-btn" onclick="showBothCurves()">Compare</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Continue with more slides... -->
            <!-- For brevity, I'll add a summary slide -->

            <!-- Slide 6: Summary -->
            <div class="slide" data-slide="6">
                <div class="slide-content">
                    <h2>📋 Lecture Summary</h2>
                    <div class="summary-grid">
                        <div class="summary-section">
                            <h3>🔄 Transduction Fundamentals</h3>
                            <ul>
                                <li>Energy conversion principles</li>
                                <li>Physiological to electrical signals</li>
                                <li>Multi-stage conversion process</li>
                                <li>Primary transduction mechanisms</li>
                            </ul>
                        </div>
                        <div class="summary-section">
                            <h3>📊 Performance Characteristics</h3>
                            <ul>
                                <li>Sensitivity and linearity</li>
                                <li>Hysteresis and drift</li>
                                <li>Response time and bandwidth</li>
                                <li>Accuracy and precision</li>
                            </ul>
                        </div>
                        <div class="summary-section">
                            <h3>🔧 Resistive Transducers</h3>
                            <ul>
                                <li>Strain gauge principles</li>
                                <li>Wheatstone bridge circuits</li>
                                <li>Temperature compensation</li>
                                <li>Biomedical applications</li>
                            </ul>
                        </div>
                        <div class="summary-section">
                            <h3>🏥 Clinical Applications</h3>
                            <ul>
                                <li>Respiratory monitoring</li>
                                <li>Blood pressure measurement</li>
                                <li>Temperature sensing</li>
                                <li>Force and displacement</li>
                            </ul>
                        </div>
                    </div>
                    <div class="next-lecture">
                        <h3>🔜 Next Lecture: Electrode Systems</h3>
                        <p>We'll explore the critical interface between biological tissues and electronic instrumentation through various electrode technologies and their applications.</p>
                        <a href="lecture2-2.html" class="next-btn">➡️ Continue to Lecture 2.2</a>
                    </div>
                </div>
            </div>

        </div>
    </main>

    <footer class="lecture-footer">
        <div class="container">
            <div class="footer-content">
                <div class="author-info">
                    <p><strong>Dr. Mohammed Yagoub Esmail</strong> | SUST - BME 2025</p>
                    <p>📧 <EMAIL> | 📱 +249912867327, +966538076790</p>
                </div>
                <div class="copyright">
                    <p>&copy; 2025 Dr. Mohammed Yagoub Esmail - All rights reserved</p>
                </div>
            </div>
        </div>
    </footer>

    <script src="../module1/lecture-slides.js"></script>
    <script src="lecture2-1-interactive.js"></script>
</body>
</html>
