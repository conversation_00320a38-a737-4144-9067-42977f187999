<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Lecture 1.3: From Cellular Signals to Organ System Electrical Activity</title>
    <link rel="stylesheet" href="../../css/style.css">
    <link rel="stylesheet" href="lecture-slides.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/gsap.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/3.9.1/chart.min.js"></script>
</head>
<body class="lecture-mode">
    <header class="lecture-header">
        <div class="container">
            <div class="lecture-navigation">
                <a href="../../index.html" class="nav-btn">🏠 Home</a>
                <a href="../../modules/module1/module1-description.html" class="nav-btn">📚 Module 1</a>
                <div class="lecture-info">
                    <span class="lecture-title">Lecture 1.3: Organ System Signals</span>
                    <span class="slide-counter">Slide <span id="currentSlide">1</span> of <span id="totalSlides">10</span></span>
                </div>
                <div class="lecture-controls">
                    <button type="button" id="prevSlide" class="control-btn">⬅️ Previous</button>
                    <button type="button" id="nextSlide" class="control-btn">➡️ Next</button>
                    <button type="button" id="fullscreenBtn" class="control-btn">🔍 Fullscreen</button>
                </div>
            </div>
        </div>
    </header>

    <main class="lecture-content">
        <div class="slides-container" id="slidesContainer">

            <!-- Slide 1: Title Slide -->
            <div class="slide active" data-slide="1">
                <div class="slide-content title-slide">
                    <h1>🧠 From Cellular Signals to Organ System Electrical Activity</h1>
                    <h2>Module 1 - Lecture 1.3</h2>
                    <div class="title-info">
                        <p><strong>Bridging the Gap: Single Cells to Measurable Bioelectric Signals</strong></p>
                        <p>Understanding how individual action potentials combine to create organ-level electrical activity</p>
                    </div>
                    <div class="author-info">
                        <p><strong>Dr. Mohammed Yagoub Esmail</strong></p>
                        <p>Sudan University of Science and Technology (SUST)</p>
                        <p>Biomedical Engineering Department - 2025</p>
                    </div>
                    <div class="organ-systems-preview" id="organPreview">
                        <div class="system-icon">💓</div>
                        <div class="system-icon">🧠</div>
                        <div class="system-icon">💪</div>
                    </div>
                </div>
            </div>

            <!-- Slide 2: Learning Objectives -->
            <div class="slide" data-slide="2">
                <div class="slide-content">
                    <h2>🎯 Learning Objectives</h2>
                    <div class="objectives-list">
                        <div class="objective-item" data-animate="1">
                            <div class="objective-icon">💓</div>
                            <div class="objective-text">
                                <h3>Understand Cardiac Electrophysiology</h3>
                                <p>Learn how individual cardiac myocytes coordinate to produce the ECG signal measurable on the body surface</p>
                            </div>
                        </div>
                        <div class="objective-item" data-animate="2">
                            <div class="objective-icon">🧠</div>
                            <div class="objective-text">
                                <h3>Explore Neural Signal Integration</h3>
                                <p>Discover how millions of neurons generate synchronized electrical activity detectable as EEG</p>
                            </div>
                        </div>
                        <div class="objective-item" data-animate="3">
                            <div class="objective-icon">💪</div>
                            <div class="objective-text">
                                <h3>Analyze Muscle Electrical Activity</h3>
                                <p>Understand how motor unit recruitment creates the EMG signals used in medical diagnostics</p>
                            </div>
                        </div>
                        <div class="objective-item" data-animate="4">
                            <div class="objective-icon">📊</div>
                            <div class="objective-text">
                                <h3>Connect to Medical Instrumentation</h3>
                                <p>Relate organ-level electrical activity to the signals captured by medical devices</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Slide 3: Signal Integration Concept -->
            <div class="slide" data-slide="3">
                <div class="slide-content">
                    <h2>🔗 From Single Cells to Organ Systems</h2>
                    <div class="content-grid">
                        <div class="content-left">
                            <div class="integration-diagram">
                                <h3>📈 Signal Integration Hierarchy</h3>
                                <div class="hierarchy-levels">
                                    <div class="level-item" data-level="1">
                                        <div class="level-icon">🧬</div>
                                        <div class="level-content">
                                            <h4>Single Cell</h4>
                                            <p>Individual action potentials<br>Duration: 1-5 ms<br>Amplitude: 100 mV</p>
                                        </div>
                                    </div>

                                    <div class="level-arrow">⬇️</div>

                                    <div class="level-item" data-level="2">
                                        <div class="level-icon">🔗</div>
                                        <div class="level-content">
                                            <h4>Cell Groups</h4>
                                            <p>Synchronized activity<br>Spatial summation<br>Temporal integration</p>
                                        </div>
                                    </div>

                                    <div class="level-arrow">⬇️</div>

                                    <div class="level-item" data-level="3">
                                        <div class="level-icon">🏢</div>
                                        <div class="level-content">
                                            <h4>Tissue Level</h4>
                                            <p>Coordinated waves<br>Conduction pathways<br>Functional units</p>
                                        </div>
                                    </div>

                                    <div class="level-arrow">⬇️</div>

                                    <div class="level-item" data-level="4">
                                        <div class="level-icon">🫀</div>
                                        <div class="level-content">
                                            <h4>Organ System</h4>
                                            <p>Measurable signals<br>Surface electrodes<br>Medical diagnostics</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="content-right">
                            <div class="key-principles">
                                <h3>🔑 Key Integration Principles</h3>
                                <div class="principle-item">
                                    <h4>Spatial Summation</h4>
                                    <p>• Multiple cells firing simultaneously<br>
                                       • Electrical fields add constructively<br>
                                       • Larger detectable signals</p>
                                </div>
                                <div class="principle-item">
                                    <h4>Temporal Synchronization</h4>
                                    <p>• Coordinated timing of activation<br>
                                       • Prevents signal cancellation<br>
                                       • Maintains signal coherence</p>
                                </div>
                                <div class="principle-item">
                                    <h4>Volume Conduction</h4>
                                    <p>• Electrical current flow through tissues<br>
                                       • Signal propagation to body surface<br>
                                       • Enables non-invasive measurement</p>
                                </div>
                                <div class="principle-item">
                                    <h4>Signal Filtering</h4>
                                    <p>• Tissue acts as low-pass filter<br>
                                       • High-frequency components attenuated<br>
                                       • Smooth, measurable waveforms</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Slide 4: Cardiac Electrophysiology -->
            <div class="slide" data-slide="4">
                <div class="slide-content">
                    <h2>💓 Cardiac Electrophysiology: From Myocytes to ECG</h2>
                    <div class="content-grid">
                        <div class="content-left">
                            <div class="cardiac-system">
                                <h3>🫀 Cardiac Conduction System</h3>
                                <div class="heart-diagram" id="heartDiagram">
                                    <svg width="350" height="300" viewBox="0 0 350 300">
                                        <!-- Heart outline -->
                                        <path d="M175 50 C150 30, 100 30, 100 80 C100 130, 175 200, 175 200 C175 200, 250 130, 250 80 C250 30, 200 30, 175 50 Z"
                                              fill="#ffebee" stroke="#e74c3c" stroke-width="3"/>

                                        <!-- SA Node -->
                                        <circle cx="200" cy="90" r="8" fill="#f39c12" stroke="#e67e22" stroke-width="2"/>
                                        <text x="220" y="95" class="node-label">SA Node</text>

                                        <!-- AV Node -->
                                        <circle cx="175" cy="130" r="6" fill="#9b59b6" stroke="#8e44ad" stroke-width="2"/>
                                        <text x="190" y="135" class="node-label">AV Node</text>

                                        <!-- Bundle of His -->
                                        <line x1="175" y1="140" x2="175" y2="160" stroke="#3498db" stroke-width="4"/>
                                        <text x="180" y="155" class="node-label">Bundle of His</text>

                                        <!-- Bundle Branches -->
                                        <line x1="175" y1="160" x2="150" y2="180" stroke="#27ae60" stroke-width="3"/>
                                        <line x1="175" y1="160" x2="200" y2="180" stroke="#27ae60" stroke-width="3"/>
                                        <text x="120" y="185" class="node-label">Left Bundle</text>
                                        <text x="205" y="185" class="node-label">Right Bundle</text>

                                        <!-- Purkinje Fibers -->
                                        <path d="M150 180 Q140 190, 130 200 Q120 210, 140 220" stroke="#e74c3c" stroke-width="2" fill="none"/>
                                        <path d="M200 180 Q210 190, 220 200 Q230 210, 210 220" stroke="#e74c3c" stroke-width="2" fill="none"/>
                                        <text x="100" y="225" class="node-label">Purkinje Fibers</text>
                                    </svg>
                                </div>

                                <div class="conduction-sequence">
                                    <h4>⚡ Conduction Sequence</h4>
                                    <div class="sequence-step" data-step="1">
                                        <span class="step-number">1</span>
                                        <span class="step-text">SA Node initiates (60-100 bpm)</span>
                                    </div>
                                    <div class="sequence-step" data-step="2">
                                        <span class="step-number">2</span>
                                        <span class="step-text">Atrial depolarization (P wave)</span>
                                    </div>
                                    <div class="sequence-step" data-step="3">
                                        <span class="step-number">3</span>
                                        <span class="step-text">AV Node delay (120 ms)</span>
                                    </div>
                                    <div class="sequence-step" data-step="4">
                                        <span class="step-number">4</span>
                                        <span class="step-text">Ventricular depolarization (QRS)</span>
                                    </div>
                                    <div class="sequence-step" data-step="5">
                                        <span class="step-number">5</span>
                                        <span class="step-text">Ventricular repolarization (T wave)</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="content-right">
                            <div class="ecg-formation">
                                <h3>📊 ECG Signal Formation</h3>
                                <canvas id="ecgFormationCanvas" width="300" height="200"></canvas>

                                <div class="ecg-characteristics">
                                    <h4>🔍 ECG Wave Characteristics</h4>
                                    <table class="ecg-table">
                                        <thead>
                                            <tr>
                                                <th>Wave</th>
                                                <th>Duration</th>
                                                <th>Amplitude</th>
                                                <th>Represents</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td>P</td>
                                                <td>80-120 ms</td>
                                                <td>0.1-0.3 mV</td>
                                                <td>Atrial depolarization</td>
                                            </tr>
                                            <tr>
                                                <td>QRS</td>
                                                <td>80-120 ms</td>
                                                <td>0.5-2.0 mV</td>
                                                <td>Ventricular depolarization</td>
                                            </tr>
                                            <tr>
                                                <td>T</td>
                                                <td>160-200 ms</td>
                                                <td>0.1-0.5 mV</td>
                                                <td>Ventricular repolarization</td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>

                                <div class="interactive-controls">
                                    <button type="button" class="demo-btn" onclick="animateHeartConduction()">⚡ Animate Conduction</button>
                                    <button type="button" class="demo-btn" onclick="showECGFormation()">📊 Show ECG Formation</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Slide 5: Neural Signal Integration -->
            <div class="slide" data-slide="5">
                <div class="slide-content">
                    <h2>🧠 Neural Signal Integration: From Neurons to EEG</h2>
                    <div class="content-grid">
                        <div class="content-left">
                            <div class="brain-activity">
                                <h3>🧠 Brain Electrical Activity</h3>
                                <div class="brain-layers">
                                    <div class="layer-item">
                                        <h4>Cortical Pyramidal Cells</h4>
                                        <p>• Primary source of EEG signals<br>
                                           • Aligned perpendicular to cortex<br>
                                           • Synchronized postsynaptic potentials</p>
                                    </div>
                                    <div class="layer-item">
                                        <h4>Spatial Organization</h4>
                                        <p>• Millions of neurons in columns<br>
                                           • Parallel orientation creates dipoles<br>
                                           • Constructive signal summation</p>
                                    </div>
                                    <div class="layer-item">
                                        <h4>Temporal Synchronization</h4>
                                        <p>• Rhythmic oscillations (1-100 Hz)<br>
                                           • Alpha, beta, theta, delta waves<br>
                                           • Functional brain states</p>
                                    </div>
                                </div>

                                <div class="eeg-rhythms">
                                    <h4>🌊 EEG Frequency Bands</h4>
                                    <div class="rhythm-item">
                                        <span class="rhythm-name">Delta (0.5-4 Hz)</span>
                                        <span class="rhythm-desc">Deep sleep, unconsciousness</span>
                                    </div>
                                    <div class="rhythm-item">
                                        <span class="rhythm-name">Theta (4-8 Hz)</span>
                                        <span class="rhythm-desc">Drowsiness, meditation</span>
                                    </div>
                                    <div class="rhythm-item">
                                        <span class="rhythm-name">Alpha (8-13 Hz)</span>
                                        <span class="rhythm-desc">Relaxed wakefulness</span>
                                    </div>
                                    <div class="rhythm-item">
                                        <span class="rhythm-name">Beta (13-30 Hz)</span>
                                        <span class="rhythm-desc">Active thinking, concentration</span>
                                    </div>
                                    <div class="rhythm-item">
                                        <span class="rhythm-name">Gamma (30-100 Hz)</span>
                                        <span class="rhythm-desc">Cognitive processing</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="content-right">
                            <div class="eeg-visualization">
                                <h3>📈 EEG Signal Characteristics</h3>
                                <canvas id="eegVisualizationCanvas" width="300" height="250"></canvas>

                                <div class="eeg-controls">
                                    <label>Select Frequency Band:</label>
                                    <select id="eegBandSelect" title="Select EEG frequency band">
                                        <option value="delta">Delta (0.5-4 Hz)</option>
                                        <option value="theta">Theta (4-8 Hz)</option>
                                        <option value="alpha" selected>Alpha (8-13 Hz)</option>
                                        <option value="beta">Beta (13-30 Hz)</option>
                                        <option value="gamma">Gamma (30-100 Hz)</option>
                                    </select>
                                    <button type="button" class="demo-btn" onclick="updateEEGVisualization()">Update Display</button>
                                </div>

                                <div class="volume-conduction">
                                    <h4>🌐 Volume Conduction to Scalp</h4>
                                    <p>• Electrical activity spreads through brain tissue<br>
                                       • CSF, skull, and scalp act as volume conductor<br>
                                       • Signal attenuation: ~100x reduction<br>
                                       • Spatial blurring: ~1-2 cm resolution</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Slide 6: Muscle Electrical Activity -->
            <div class="slide" data-slide="6">
                <div class="slide-content">
                    <h2>💪 Muscle Electrical Activity: From Motor Units to EMG</h2>
                    <div class="content-grid">
                        <div class="content-left">
                            <div class="motor-unit-concept">
                                <h3>🔗 Motor Unit Organization</h3>
                                <div class="motor-unit-diagram" id="motorUnitDiagram">
                                    <svg width="350" height="250" viewBox="0 0 350 250">
                                        <!-- Motor neuron -->
                                        <circle cx="50" cy="50" r="15" fill="#3498db" stroke="#2980b9" stroke-width="2"/>
                                        <text x="70" y="55" class="unit-label">Motor Neuron</text>

                                        <!-- Axon -->
                                        <line x1="65" y1="50" x2="150" y2="50" stroke="#3498db" stroke-width="4"/>

                                        <!-- Neuromuscular junctions -->
                                        <circle cx="150" cy="40" r="4" fill="#e74c3c"/>
                                        <circle cx="150" cy="50" r="4" fill="#e74c3c"/>
                                        <circle cx="150" cy="60" r="4" fill="#e74c3c"/>

                                        <!-- Muscle fibers -->
                                        <rect x="160" y="35" width="80" height="8" fill="#27ae60" stroke="#229954" stroke-width="1"/>
                                        <rect x="160" y="46" width="80" height="8" fill="#27ae60" stroke="#229954" stroke-width="1"/>
                                        <rect x="160" y="57" width="80" height="8" fill="#27ae60" stroke="#229954" stroke-width="1"/>

                                        <text x="250" y="50" class="unit-label">Muscle Fibers</text>

                                        <!-- Action potential propagation -->
                                        <path d="M160 30 Q170 25, 180 30 Q190 35, 200 30 Q210 25, 220 30"
                                              stroke="#f39c12" stroke-width="2" fill="none"/>
                                        <text x="160" y="20" class="signal-label">Action Potential</text>
                                    </svg>
                                </div>

                                <div class="motor-unit-types">
                                    <h4>🏃‍♂️ Motor Unit Types</h4>
                                    <div class="unit-type">
                                        <h5>Type I (Slow)</h5>
                                        <p>• Small force, fatigue resistant<br>
                                           • Low firing rates (8-25 Hz)<br>
                                           • Postural control, endurance</p>
                                    </div>
                                    <div class="unit-type">
                                        <h5>Type IIa (Fast Fatigue-Resistant)</h5>
                                        <p>• Moderate force, moderate fatigue<br>
                                           • Medium firing rates (25-50 Hz)<br>
                                           • Sustained powerful movements</p>
                                    </div>
                                    <div class="unit-type">
                                        <h5>Type IIx (Fast Fatigable)</h5>
                                        <p>• High force, rapid fatigue<br>
                                           • High firing rates (50-100 Hz)<br>
                                           • Explosive movements</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="content-right">
                            <div class="emg-formation">
                                <h3>📊 EMG Signal Formation</h3>
                                <canvas id="emgFormationCanvas" width="300" height="200"></canvas>

                                <div class="recruitment-pattern">
                                    <h4>📈 Motor Unit Recruitment</h4>
                                    <div class="recruitment-principle">
                                        <h5>Henneman's Size Principle</h5>
                                        <p>• Small motor units recruited first<br>
                                           • Gradual addition of larger units<br>
                                           • Smooth force gradation</p>
                                    </div>

                                    <div class="force-emg-relationship">
                                        <h5>Force-EMG Relationship</h5>
                                        <p>• EMG amplitude ∝ muscle force<br>
                                           • Non-linear relationship<br>
                                           • Individual muscle variations</p>
                                    </div>
                                </div>

                                <div class="emg-characteristics">
                                    <h4>🔍 EMG Signal Properties</h4>
                                    <table class="emg-table">
                                        <thead>
                                            <tr>
                                                <th>Parameter</th>
                                                <th>Surface EMG</th>
                                                <th>Intramuscular EMG</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td>Amplitude</td>
                                                <td>0.1-5 mV</td>
                                                <td>0.1-20 mV</td>
                                            </tr>
                                            <tr>
                                                <td>Frequency</td>
                                                <td>10-500 Hz</td>
                                                <td>10-2000 Hz</td>
                                            </tr>
                                            <tr>
                                                <td>Spatial Resolution</td>
                                                <td>~1 cm</td>
                                                <td>~1 mm</td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>

                                <div class="interactive-controls">
                                    <button type="button" class="demo-btn" onclick="animateMotorUnitRecruitment()">🏃‍♂️ Show Recruitment</button>
                                    <button type="button" class="demo-btn" onclick="demonstrateEMGFormation()">📊 EMG Formation</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Slide 7: Summary -->
            <div class="slide" data-slide="7">
                <div class="slide-content">
                    <h2>📋 Lecture Summary</h2>
                    <div class="summary-grid">
                        <div class="summary-section">
                            <h3>🔗 Signal Integration</h3>
                            <ul>
                                <li>Spatial and temporal summation</li>
                                <li>Volume conduction principles</li>
                                <li>From cellular to organ level</li>
                                <li>Measurable surface signals</li>
                            </ul>
                        </div>
                        <div class="summary-section">
                            <h3>💓 Cardiac Signals</h3>
                            <ul>
                                <li>Conduction system organization</li>
                                <li>ECG wave formation</li>
                                <li>P-QRS-T wave characteristics</li>
                                <li>Clinical significance</li>
                            </ul>
                        </div>
                        <div class="summary-section">
                            <h3>🧠 Neural Signals</h3>
                            <ul>
                                <li>Cortical pyramidal cell alignment</li>
                                <li>EEG frequency bands</li>
                                <li>Synchronized oscillations</li>
                                <li>Brain state monitoring</li>
                            </ul>
                        </div>
                        <div class="summary-section">
                            <h3>💪 Muscle Signals</h3>
                            <ul>
                                <li>Motor unit organization</li>
                                <li>Recruitment patterns</li>
                                <li>EMG signal characteristics</li>
                                <li>Force-EMG relationships</li>
                            </ul>
                        </div>
                    </div>
                    <div class="next-lecture">
                        <h3>🔜 Next Lecture: Medical Device Safety & Standards</h3>
                        <p>We'll explore the critical safety considerations and regulatory standards that govern medical instrumentation design and implementation.</p>
                        <a href="lecture1-4.html" class="next-btn">➡️ Continue to Lecture 1.4</a>
                    </div>
                </div>
            </div>

        </div>
    </main>

    <footer class="lecture-footer">
        <div class="container">
            <div class="footer-content">
                <div class="author-info">
                    <p><strong>Dr. Mohammed Yagoub Esmail</strong> | SUST - BME 2025</p>
                    <p>📧 <EMAIL> | 📱 +249912867327, +966538076790</p>
                </div>
                <div class="copyright">
                    <p>&copy; 2025 Dr. Mohammed Yagoub Esmail - All rights reserved</p>
                </div>
            </div>
        </div>
    </footer>

    <script src="lecture-slides.js"></script>
    <script src="lecture1-3-interactive.js"></script>
</body>
</html>
