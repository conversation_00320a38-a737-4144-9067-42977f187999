<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Lecture 1.2: Action Potential Generation & Propagation</title>
    <link rel="stylesheet" href="../../css/style.css">
    <link rel="stylesheet" href="lecture-slides.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/gsap.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/3.9.1/chart.min.js"></script>
</head>
<body class="lecture-mode">
    <header class="lecture-header">
        <div class="container">
            <div class="lecture-navigation">
                <a href="../../index.html" class="nav-btn">🏠 Home</a>
                <a href="../../modules/module1/module1-description.html" class="nav-btn">📚 Module 1</a>
                <div class="lecture-info">
                    <span class="lecture-title">Lecture 1.2: Action Potential Generation</span>
                    <span class="slide-counter">Slide <span id="currentSlide">1</span> of <span id="totalSlides">20</span></span>
                </div>
                <div class="lecture-controls">
                    <button id="prevSlide" class="control-btn">⬅️ Previous</button>
                    <button id="nextSlide" class="control-btn">➡️ Next</button>
                    <button id="fullscreenBtn" class="control-btn">🔍 Fullscreen</button>
                </div>
            </div>
        </div>
    </header>

    <main class="lecture-content">
        <div class="slides-container" id="slidesContainer">
            
            <!-- Slide 1: Title Slide -->
            <div class="slide active" data-slide="1">
                <div class="slide-content title-slide">
                    <h1>⚡ Action Potential Generation & Propagation</h1>
                    <h2>Module 1 - Lecture 1.2</h2>
                    <div class="title-info">
                        <p><strong>From Cellular Foundations to Signal Transmission</strong></p>
                        <p>Understanding the Electrical Basis of Biomedical Signals</p>
                    </div>
                    <div class="author-info">
                        <p><strong>Dr. Mohammed Yagoub Esmail</strong></p>
                        <p>Sudan University of Science and Technology (SUST)</p>
                        <p>Biomedical Engineering Department - 2025</p>
                    </div>
                    <div class="ap-preview-animation" id="apPreview">
                        <canvas id="apPreviewCanvas" width="300" height="150"></canvas>
                    </div>
                </div>
            </div>

            <!-- Slide 2: Learning Objectives -->
            <div class="slide" data-slide="2">
                <div class="slide-content">
                    <h2>🎯 Learning Objectives</h2>
                    <div class="objectives-list">
                        <div class="objective-item" data-animate="1">
                            <div class="objective-icon">⚡</div>
                            <div class="objective-text">
                                <h3>Understand Action Potential Mechanism</h3>
                                <p>Master the ionic basis of action potential generation, including threshold, depolarization, and repolarization phases</p>
                            </div>
                        </div>
                        <div class="objective-item" data-animate="2">
                            <div class="objective-icon">🌊</div>
                            <div class="objective-text">
                                <h3>Analyze Signal Propagation</h3>
                                <p>Learn how action potentials propagate along axons and the factors affecting conduction velocity</p>
                            </div>
                        </div>
                        <div class="objective-item" data-animate="3">
                            <div class="objective-icon">🔄</div>
                            <div class="objective-text">
                                <h3>Explore Refractory Periods</h3>
                                <p>Understand absolute and relative refractory periods and their role in signal integrity</p>
                            </div>
                        </div>
                        <div class="objective-item" data-animate="4">
                            <div class="objective-icon">🧠</div>
                            <div class="objective-text">
                                <h3>Connect to Medical Instrumentation</h3>
                                <p>Relate action potential properties to measurable bioelectric signals in medical devices</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Slide 3: Action Potential Overview -->
            <div class="slide" data-slide="3">
                <div class="slide-content">
                    <h2>⚡ Action Potential: The Universal Signal</h2>
                    <div class="content-grid">
                        <div class="content-left">
                            <div class="ap-graph-container">
                                <h3>📈 Action Potential Waveform</h3>
                                <canvas id="apMainGraph" width="400" height="300"></canvas>
                                <div class="graph-controls">
                                    <button class="demo-btn" onclick="animateActionPotential()">▶️ Animate AP</button>
                                    <button class="demo-btn" onclick="showPhaseDetails()">📊 Show Phases</button>
                                </div>
                            </div>
                        </div>
                        <div class="content-right">
                            <div class="ap-characteristics">
                                <h3>🔑 Key Characteristics</h3>
                                <div class="characteristic-item">
                                    <h4>Threshold Potential</h4>
                                    <p>• Typically -55 to -50 mV<br>
                                       • Critical voltage for Na⁺ channel activation<br>
                                       • All-or-nothing principle</p>
                                </div>
                                <div class="characteristic-item">
                                    <h4>Amplitude</h4>
                                    <p>• Peak: +30 to +40 mV<br>
                                       • Total swing: ~100-110 mV<br>
                                       • Consistent regardless of stimulus strength</p>
                                </div>
                                <div class="characteristic-item">
                                    <h4>Duration</h4>
                                    <p>• Total duration: 1-5 ms<br>
                                       • Depolarization: 0.5-1 ms<br>
                                       • Repolarization: 1-3 ms</p>
                                </div>
                                <div class="characteristic-item">
                                    <h4>Frequency</h4>
                                    <p>• Maximum: 500-1000 Hz<br>
                                       • Limited by refractory periods<br>
                                       • Encodes information intensity</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Slide 4: Action Potential Phases -->
            <div class="slide" data-slide="4">
                <div class="slide-content">
                    <h2>🔄 Action Potential Phases</h2>
                    <div class="phases-container">
                        <div class="phase-timeline">
                            <div class="phase-item" data-phase="1">
                                <div class="phase-number">1</div>
                                <div class="phase-content">
                                    <h3>Resting State</h3>
                                    <p>Vm = -70 mV</p>
                                    <div class="phase-details">
                                        • Na⁺ channels closed<br>
                                        • K⁺ channels mostly closed<br>
                                        • Na⁺/K⁺ pump active
                                    </div>
                                </div>
                            </div>
                            
                            <div class="phase-item" data-phase="2">
                                <div class="phase-number">2</div>
                                <div class="phase-content">
                                    <h3>Depolarization</h3>
                                    <p>-55 mV → +30 mV</p>
                                    <div class="phase-details">
                                        • Na⁺ channels open rapidly<br>
                                        • Massive Na⁺ influx<br>
                                        • Positive feedback loop
                                    </div>
                                </div>
                            </div>
                            
                            <div class="phase-item" data-phase="3">
                                <div class="phase-number">3</div>
                                <div class="phase-content">
                                    <h3>Repolarization</h3>
                                    <p>+30 mV → -70 mV</p>
                                    <div class="phase-details">
                                        • Na⁺ channels inactivate<br>
                                        • K⁺ channels open<br>
                                        • K⁺ efflux restores negativity
                                    </div>
                                </div>
                            </div>
                            
                            <div class="phase-item" data-phase="4">
                                <div class="phase-number">4</div>
                                <div class="phase-content">
                                    <h3>Hyperpolarization</h3>
                                    <p>-70 mV → -80 mV</p>
                                    <div class="phase-details">
                                        • K⁺ channels slow to close<br>
                                        • Temporary overshoot<br>
                                        • Gradual return to rest
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="ionic-currents">
                            <h3>📊 Ionic Current Flow</h3>
                            <div class="current-diagram" id="currentDiagram">
                                <canvas id="currentCanvas" width="400" height="200"></canvas>
                            </div>
                            <div class="current-controls">
                                <button class="demo-btn" onclick="showSodiumCurrent()">Na⁺ Current</button>
                                <button class="demo-btn" onclick="showPotassiumCurrent()">K⁺ Current</button>
                                <button class="demo-btn" onclick="showBothCurrents()">Both Currents</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Slide 5: Voltage-Gated Channel Kinetics -->
            <div class="slide" data-slide="5">
                <div class="slide-content">
                    <h2>⚙️ Voltage-Gated Channel Kinetics</h2>
                    <div class="content-grid">
                        <div class="content-left">
                            <div class="channel-kinetics">
                                <h3>🔬 Sodium Channel States</h3>
                                <div class="state-diagram" id="naChannelStates">
                                    <svg width="400" height="250" viewBox="0 0 400 250">
                                        <!-- Closed State -->
                                        <g class="channel-state" data-state="closed">
                                            <rect x="50" y="100" width="80" height="50" fill="#e74c3c" stroke="#c0392b" stroke-width="2" rx="10"/>
                                            <text x="90" y="130" text-anchor="middle" fill="white" font-weight="bold">Closed</text>
                                            <text x="90" y="170" text-anchor="middle" class="state-label">Vm < -55mV</text>
                                        </g>
                                        
                                        <!-- Open State -->
                                        <g class="channel-state" data-state="open">
                                            <rect x="200" y="100" width="80" height="50" fill="#27ae60" stroke="#229954" stroke-width="2" rx="10"/>
                                            <text x="240" y="130" text-anchor="middle" fill="white" font-weight="bold">Open</text>
                                            <text x="240" y="170" text-anchor="middle" class="state-label">Vm > -55mV</text>
                                        </g>
                                        
                                        <!-- Inactivated State -->
                                        <g class="channel-state" data-state="inactivated">
                                            <rect x="125" y="200" width="80" height="50" fill="#f39c12" stroke="#e67e22" stroke-width="2" rx="10"/>
                                            <text x="165" y="230" text-anchor="middle" fill="white" font-weight="bold">Inactivated</text>
                                            <text x="165" y="270" text-anchor="middle" class="state-label">Time-dependent</text>
                                        </g>
                                        
                                        <!-- Transitions -->
                                        <path d="M 130 125 L 200 125" stroke="#2c3e50" stroke-width="2" marker-end="url(#arrowhead)"/>
                                        <text x="165" y="120" text-anchor="middle" class="transition-label">Activation</text>
                                        
                                        <path d="M 240 150 L 180 200" stroke="#2c3e50" stroke-width="2" marker-end="url(#arrowhead)"/>
                                        <text x="220" y="180" text-anchor="middle" class="transition-label">Inactivation</text>
                                        
                                        <path d="M 145 200 L 110 150" stroke="#2c3e50" stroke-width="2" marker-end="url(#arrowhead)"/>
                                        <text x="100" y="180" text-anchor="middle" class="transition-label">Recovery</text>
                                        
                                        <!-- Arrow marker definition -->
                                        <defs>
                                            <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                                                <polygon points="0 0, 10 3.5, 0 7" fill="#2c3e50"/>
                                            </marker>
                                        </defs>
                                    </svg>
                                </div>
                            </div>
                        </div>
                        <div class="content-right">
                            <div class="kinetic-parameters">
                                <h3>📊 Kinetic Parameters</h3>
                                <div class="parameter-table">
                                    <table>
                                        <thead>
                                            <tr>
                                                <th>Parameter</th>
                                                <th>Sodium Channel</th>
                                                <th>Potassium Channel</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td>Activation Threshold</td>
                                                <td>-55 mV</td>
                                                <td>-40 mV</td>
                                            </tr>
                                            <tr>
                                                <td>Activation Time</td>
                                                <td>0.1-0.5 ms</td>
                                                <td>1-5 ms</td>
                                            </tr>
                                            <tr>
                                                <td>Inactivation Time</td>
                                                <td>1-2 ms</td>
                                                <td>None</td>
                                            </tr>
                                            <tr>
                                                <td>Recovery Time</td>
                                                <td>2-5 ms</td>
                                                <td>N/A</td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                                
                                <div class="hodgkin-huxley">
                                    <h4>🧮 Hodgkin-Huxley Model</h4>
                                    <div class="equation-box">
                                        <div class="equation-main">
                                            INa = gNa × m³ × h × (V - ENa)
                                        </div>
                                        <div class="equation-main">
                                            IK = gK × n⁴ × (V - EK)
                                        </div>
                                        <div class="equation-variables">
                                            <p>m = activation gate (Na⁺)</p>
                                            <p>h = inactivation gate (Na⁺)</p>
                                            <p>n = activation gate (K⁺)</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Slide 6: Refractory Periods -->
            <div class="slide" data-slide="6">
                <div class="slide-content">
                    <h2>🔒 Refractory Periods</h2>
                    <div class="content-grid">
                        <div class="content-left">
                            <div class="refractory-diagram">
                                <h3>📊 Refractory Period Timeline</h3>
                                <canvas id="refractoryCanvas" width="400" height="250"></canvas>
                                <div class="refractory-controls">
                                    <button class="demo-btn" onclick="showRefractoryPeriods()">Show Periods</button>
                                    <button class="demo-btn" onclick="testStimulation()">Test Stimulation</button>
                                </div>
                            </div>
                        </div>
                        <div class="content-right">
                            <div class="refractory-types">
                                <h3>🔑 Types of Refractory Periods</h3>
                                <div class="refractory-type">
                                    <h4>Absolute Refractory Period</h4>
                                    <div class="type-details">
                                        <p><strong>Duration:</strong> 1-2 ms</p>
                                        <p><strong>Mechanism:</strong> Na⁺ channels inactivated</p>
                                        <p><strong>Effect:</strong> No stimulus can trigger AP</p>
                                        <p><strong>Function:</strong> Ensures unidirectional propagation</p>
                                    </div>
                                </div>
                                
                                <div class="refractory-type">
                                    <h4>Relative Refractory Period</h4>
                                    <div class="type-details">
                                        <p><strong>Duration:</strong> 2-5 ms</p>
                                        <p><strong>Mechanism:</strong> K⁺ channels still open</p>
                                        <p><strong>Effect:</strong> Stronger stimulus needed</p>
                                        <p><strong>Function:</strong> Limits firing frequency</p>
                                    </div>
                                </div>
                                
                                <div class="functional-significance">
                                    <h4>🎯 Functional Significance</h4>
                                    <ul>
                                        <li>Prevents signal collision</li>
                                        <li>Ensures signal integrity</li>
                                        <li>Limits maximum firing rate</li>
                                        <li>Enables frequency coding</li>
                                        <li>Protects against overstimulation</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Slide 7: Signal Propagation -->
            <div class="slide" data-slide="7">
                <div class="slide-content">
                    <h2>🌊 Action Potential Propagation</h2>
                    <div class="propagation-container">
                        <div class="propagation-demo">
                            <h3>📡 Propagation Mechanism</h3>
                            <div class="axon-diagram" id="axonDiagram">
                                <canvas id="propagationCanvas" width="600" height="200"></canvas>
                            </div>
                            <div class="propagation-controls">
                                <button class="demo-btn" onclick="startPropagation()">▶️ Start Propagation</button>
                                <button class="demo-btn" onclick="showLocalCurrents()">🔄 Local Currents</button>
                                <button class="demo-btn" onclick="resetPropagation()">🔄 Reset</button>
                            </div>
                        </div>
                        
                        <div class="conduction-velocity">
                            <h3>⚡ Conduction Velocity Factors</h3>
                            <div class="velocity-factors">
                                <div class="factor-item">
                                    <h4>Axon Diameter</h4>
                                    <p>• Larger diameter = faster conduction<br>
                                       • Squid giant axon: 500 µm, 25 m/s<br>
                                       • Human motor axon: 20 µm, 120 m/s</p>
                                </div>
                                
                                <div class="factor-item">
                                    <h4>Myelination</h4>
                                    <p>• Saltatory conduction<br>
                                       • 10-100x speed increase<br>
                                       • Nodes of Ranvier spacing</p>
                                </div>
                                
                                <div class="factor-item">
                                    <h4>Temperature</h4>
                                    <p>• Q₁₀ ≈ 1.5-3<br>
                                       • Hypothermia slows conduction<br>
                                       • Fever increases conduction</p>
                                </div>
                            </div>
                            
                            <div class="velocity-table">
                                <h4>📊 Typical Conduction Velocities</h4>
                                <table>
                                    <thead>
                                        <tr>
                                            <th>Fiber Type</th>
                                            <th>Diameter (µm)</th>
                                            <th>Myelinated</th>
                                            <th>Velocity (m/s)</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td>Aα (Motor)</td>
                                            <td>12-20</td>
                                            <td>Yes</td>
                                            <td>70-120</td>
                                        </tr>
                                        <tr>
                                            <td>Aβ (Touch)</td>
                                            <td>5-12</td>
                                            <td>Yes</td>
                                            <td>30-70</td>
                                        </tr>
                                        <tr>
                                            <td>Aδ (Pain)</td>
                                            <td>2-5</td>
                                            <td>Yes</td>
                                            <td>12-30</td>
                                        </tr>
                                        <tr>
                                            <td>C (Pain)</td>
                                            <td>0.4-1.2</td>
                                            <td>No</td>
                                            <td>0.5-2</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Slide 8: Summary -->
            <div class="slide" data-slide="8">
                <div class="slide-content">
                    <h2>📋 Lecture Summary</h2>
                    <div class="summary-grid">
                        <div class="summary-section">
                            <h3>⚡ Action Potential Basics</h3>
                            <ul>
                                <li>All-or-nothing principle</li>
                                <li>Threshold activation (-55 mV)</li>
                                <li>Consistent amplitude (~100 mV)</li>
                                <li>Brief duration (1-5 ms)</li>
                            </ul>
                        </div>
                        <div class="summary-section">
                            <h3>🔄 Ionic Mechanisms</h3>
                            <ul>
                                <li>Na⁺ channel activation/inactivation</li>
                                <li>K⁺ channel delayed activation</li>
                                <li>Hodgkin-Huxley kinetics</li>
                                <li>Voltage-dependent gating</li>
                            </ul>
                        </div>
                        <div class="summary-section">
                            <h3>🔒 Refractory Periods</h3>
                            <ul>
                                <li>Absolute refractory period (1-2 ms)</li>
                                <li>Relative refractory period (2-5 ms)</li>
                                <li>Unidirectional propagation</li>
                                <li>Frequency limitation</li>
                            </ul>
                        </div>
                        <div class="summary-section">
                            <h3>🌊 Signal Propagation</h3>
                            <ul>
                                <li>Local current flow mechanism</li>
                                <li>Conduction velocity factors</li>
                                <li>Myelination and saltatory conduction</li>
                                <li>Clinical significance</li>
                            </ul>
                        </div>
                    </div>
                    <div class="next-lecture">
                        <h3>🔜 Next Lecture: From Cells to Organ Systems</h3>
                        <p>We'll explore how individual action potentials combine to create the complex bioelectric signals measured in medical instrumentation, including ECG, EMG, and EEG.</p>
                        <a href="lecture1-3.html" class="next-btn">➡️ Continue to Lecture 1.3</a>
                    </div>
                </div>
            </div>

        </div>
    </main>

    <footer class="lecture-footer">
        <div class="container">
            <div class="footer-content">
                <div class="author-info">
                    <p><strong>Dr. Mohammed Yagoub Esmail</strong> | SUST - BME 2025</p>
                    <p>📧 <EMAIL> | 📱 +249912867327, +966538076790</p>
                </div>
                <div class="copyright">
                    <p>&copy; 2025 Dr. Mohammed Yagoub Esmail - All rights reserved</p>
                </div>
            </div>
        </div>
    </footer>

    <script src="lecture-slides.js"></script>
    <script src="lecture1-2-interactive.js"></script>
</body>
</html>
