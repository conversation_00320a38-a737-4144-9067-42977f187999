// Lecture 2.2 Interactive Elements - Electrode Systems

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    console.log('Lecture 2.2 interactive elements loading...');
    
    // Initialize all interactive elements
    initializeElectrodeVisualizations();
    initializeImpedancePlots();
    initializeSignalQualityDemo();
    initializeElectrodeCircuit();
    
    console.log('Lecture 2.2 interactive elements loaded successfully');
});

// Initialize electrode visualizations
function initializeElectrodeVisualizations() {
    // Animate electrode types
    const electrodeTypes = document.querySelectorAll('.electrode-type');
    electrodeTypes.forEach((type, index) => {
        setTimeout(() => {
            if (typeof gsap !== 'undefined') {
                gsap.from(type, {
                    scale: 0,
                    rotation: 180,
                    duration: 1,
                    ease: "back.out(1.7)"
                });
            }
        }, index * 200);
    });
    
    // Add hover effects to electrode categories
    const electrodeCategories = document.querySelectorAll('.electrode-category');
    electrodeCategories.forEach(category => {
        category.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-5px)';
            this.style.boxShadow = '0 8px 20px rgba(0,0,0,0.15)';
        });
        
        category.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
            this.style.boxShadow = '0 4px 8px rgba(0,0,0,0.1)';
        });
    });
}

// Initialize impedance plots
function initializeImpedancePlots() {
    const canvas = document.getElementById('impedanceCanvas');
    if (!canvas) return;
    
    const ctx = canvas.getContext('2d');
    
    // Initial plot - impedance magnitude
    drawImpedancePlot(ctx, canvas.width, canvas.height, 'magnitude');
}

function drawImpedancePlot(ctx, width, height, plotType) {
    ctx.clearRect(0, 0, width, height);
    
    // Draw background
    ctx.fillStyle = '#f8f9fa';
    ctx.fillRect(0, 0, width, height);
    
    // Draw axes
    const margin = 40;
    const plotWidth = width - 2 * margin;
    const plotHeight = height - 2 * margin;
    
    ctx.strokeStyle = '#2c3e50';
    ctx.lineWidth = 2;
    ctx.beginPath();
    ctx.moveTo(margin, height - margin);
    ctx.lineTo(width - margin, height - margin);
    ctx.moveTo(margin, margin);
    ctx.lineTo(margin, height - margin);
    ctx.stroke();
    
    // Draw plot based on type
    switch(plotType) {
        case 'magnitude':
            drawImpedanceMagnitude(ctx, margin, plotWidth, plotHeight);
            break;
        case 'phase':
            drawImpedancePhase(ctx, margin, plotWidth, plotHeight);
            break;
        case 'nyquist':
            drawNyquistPlot(ctx, margin, plotWidth, plotHeight);
            break;
    }
    
    // Add labels
    ctx.fillStyle = '#2c3e50';
    ctx.font = '12px Arial';
    if (plotType === 'nyquist') {
        ctx.fillText('Real(Z) [Ω]', width - 80, height - 10);
        ctx.save();
        ctx.translate(15, height / 2);
        ctx.rotate(-Math.PI / 2);
        ctx.fillText('Imag(Z) [Ω]', 0, 0);
        ctx.restore();
    } else {
        ctx.fillText('Frequency [Hz]', width - 80, height - 10);
        ctx.save();
        ctx.translate(15, height / 2);
        ctx.rotate(-Math.PI / 2);
        ctx.fillText(plotType === 'magnitude' ? '|Z| [Ω]' : 'Phase [°]', 0, 0);
        ctx.restore();
    }
}

function drawImpedanceMagnitude(ctx, margin, plotWidth, plotHeight) {
    // Ag/AgCl electrode impedance
    ctx.strokeStyle = '#27ae60';
    ctx.lineWidth = 3;
    ctx.beginPath();
    
    for (let x = 0; x <= plotWidth; x += 2) {
        const freq = Math.pow(10, 1 + 3 * x / plotWidth); // 10 Hz to 10 kHz
        const impedance = 1000 / Math.sqrt(1 + Math.pow(freq / 100, 2)); // Simplified model
        const logZ = Math.log10(impedance);
        const y = plotHeight * (1 - (logZ - 1) / 2); // Scale for 10-1000 Ω
        
        if (x === 0) {
            ctx.moveTo(margin + x, margin + y);
        } else {
            ctx.lineTo(margin + x, margin + y);
        }
    }
    ctx.stroke();
    
    // Dry electrode impedance
    ctx.strokeStyle = '#e74c3c';
    ctx.lineWidth = 3;
    ctx.beginPath();
    
    for (let x = 0; x <= plotWidth; x += 2) {
        const freq = Math.pow(10, 1 + 3 * x / plotWidth);
        const impedance = 10000 / Math.sqrt(1 + Math.pow(freq / 50, 2)); // Higher impedance
        const logZ = Math.log10(impedance);
        const y = plotHeight * (1 - (logZ - 1) / 3); // Scale for 10-10000 Ω
        
        if (x === 0) {
            ctx.moveTo(margin + x, margin + y);
        } else {
            ctx.lineTo(margin + x, margin + y);
        }
    }
    ctx.stroke();
    
    // Add legend
    ctx.fillStyle = '#27ae60';
    ctx.font = '12px Arial';
    ctx.fillText('Ag/AgCl', margin + 10, margin + 20);
    ctx.fillStyle = '#e74c3c';
    ctx.fillText('Dry Electrode', margin + 10, margin + 35);
}

function drawImpedancePhase(ctx, margin, plotWidth, plotHeight) {
    // Phase response for capacitive behavior
    ctx.strokeStyle = '#3498db';
    ctx.lineWidth = 3;
    ctx.beginPath();
    
    for (let x = 0; x <= plotWidth; x += 2) {
        const freq = Math.pow(10, 1 + 3 * x / plotWidth);
        const phase = -Math.atan(1 / (freq / 100)) * 180 / Math.PI; // Phase in degrees
        const y = plotHeight * (1 - (phase + 90) / 90); // Scale for -90 to 0 degrees
        
        if (x === 0) {
            ctx.moveTo(margin + x, margin + y);
        } else {
            ctx.lineTo(margin + x, margin + y);
        }
    }
    ctx.stroke();
    
    // Add phase markers
    ctx.fillStyle = '#3498db';
    ctx.font = '10px Arial';
    ctx.fillText('0°', margin - 20, margin + 10);
    ctx.fillText('-45°', margin - 30, margin + plotHeight/2);
    ctx.fillText('-90°', margin - 30, margin + plotHeight);
}

function drawNyquistPlot(ctx, margin, plotWidth, plotHeight) {
    // Nyquist plot for electrode impedance
    ctx.strokeStyle = '#9b59b6';
    ctx.lineWidth = 3;
    ctx.beginPath();
    
    for (let i = 0; i <= 100; i++) {
        const omega = Math.pow(10, i / 25); // Frequency sweep
        const R = 1000; // Resistance
        const C = 1e-6; // Capacitance
        const real = R / (1 + Math.pow(omega * R * C, 2));
        const imag = -omega * R * R * C / (1 + Math.pow(omega * R * C, 2));
        
        const x = margin + (real / 1000) * plotWidth;
        const y = margin + plotHeight + (imag / 500) * plotHeight;
        
        if (i === 0) {
            ctx.moveTo(x, y);
        } else {
            ctx.lineTo(x, y);
        }
    }
    ctx.stroke();
    
    // Add frequency markers
    ctx.fillStyle = '#9b59b6';
    ctx.font = '10px Arial';
    ctx.fillText('1 Hz', margin + plotWidth * 0.9, margin + plotHeight * 0.1);
    ctx.fillText('1 kHz', margin + plotWidth * 0.5, margin + plotHeight * 0.5);
    ctx.fillText('10 kHz', margin + plotWidth * 0.1, margin + plotHeight * 0.9);
}

// Initialize signal quality demonstration
function initializeSignalQualityDemo() {
    const canvas = document.getElementById('signalQualityCanvas');
    if (!canvas) return;
    
    const ctx = canvas.getContext('2d');
    
    // Initial display - good contact
    drawSignalQuality(ctx, canvas.width, canvas.height, 'good');
}

function drawSignalQuality(ctx, width, height, quality) {
    ctx.clearRect(0, 0, width, height);
    
    // Draw background
    ctx.fillStyle = '#f8f9fa';
    ctx.fillRect(0, 0, width, height);
    
    // Draw axes
    const margin = 30;
    const plotWidth = width - 2 * margin;
    const plotHeight = height - 2 * margin;
    
    ctx.strokeStyle = '#2c3e50';
    ctx.lineWidth = 1;
    ctx.beginPath();
    ctx.moveTo(margin, height - margin);
    ctx.lineTo(width - margin, height - margin);
    ctx.moveTo(margin, margin);
    ctx.lineTo(margin, height - margin);
    ctx.stroke();
    
    // Generate signal based on quality
    ctx.strokeStyle = getSignalColor(quality);
    ctx.lineWidth = 2;
    ctx.beginPath();
    
    for (let x = 0; x <= plotWidth; x += 2) {
        const t = x / plotWidth * 4 * Math.PI; // 2 cycles
        let signal = Math.sin(t); // Base ECG-like signal
        
        // Add quality-specific characteristics
        switch(quality) {
            case 'good':
                signal += 0.1 * Math.random(); // Low noise
                break;
            case 'poor':
                signal += 0.5 * Math.random(); // High noise
                signal *= 0.7; // Reduced amplitude
                break;
            case 'motion':
                signal += 0.3 * Math.sin(t * 0.1); // Low frequency artifact
                signal += 0.2 * Math.random(); // Noise
                break;
        }
        
        const y = margin + plotHeight/2 - signal * plotHeight/4;
        
        if (x === 0) {
            ctx.moveTo(margin + x, y);
        } else {
            ctx.lineTo(margin + x, y);
        }
    }
    ctx.stroke();
    
    // Add quality indicator
    ctx.fillStyle = getSignalColor(quality);
    ctx.font = 'bold 14px Arial';
    ctx.fillText(getQualityLabel(quality), margin + 10, margin + 20);
}

function getSignalColor(quality) {
    switch(quality) {
        case 'good': return '#27ae60';
        case 'poor': return '#e74c3c';
        case 'motion': return '#f39c12';
        default: return '#2c3e50';
    }
}

function getQualityLabel(quality) {
    switch(quality) {
        case 'good': return 'Good Contact';
        case 'poor': return 'Poor Contact';
        case 'motion': return 'Motion Artifact';
        default: return 'Signal Quality';
    }
}

// Initialize electrode circuit
function initializeElectrodeCircuit() {
    const circuit = document.getElementById('electrodeCircuit');
    if (!circuit) return;
    
    // Add hover effects to circuit components
    const components = circuit.querySelectorAll('rect, path');
    components.forEach(component => {
        component.addEventListener('mouseenter', function() {
            this.style.filter = 'brightness(1.3)';
            this.style.transform = 'scale(1.1)';
        });
        
        component.addEventListener('mouseleave', function() {
            this.style.filter = 'brightness(1)';
            this.style.transform = 'scale(1)';
        });
    });
}

// Interactive functions

function showImpedanceMagnitude() {
    const canvas = document.getElementById('impedanceCanvas');
    if (!canvas) return;
    
    const ctx = canvas.getContext('2d');
    drawImpedancePlot(ctx, canvas.width, canvas.height, 'magnitude');
    
    // Add visual feedback
    if (typeof gsap !== 'undefined') {
        gsap.from(canvas, {
            scale: 1.05,
            duration: 0.3,
            ease: "back.out(1.7)"
        });
    }
}

function showImpedancePhase() {
    const canvas = document.getElementById('impedanceCanvas');
    if (!canvas) return;
    
    const ctx = canvas.getContext('2d');
    drawImpedancePlot(ctx, canvas.width, canvas.height, 'phase');
    
    if (typeof gsap !== 'undefined') {
        gsap.from(canvas, {
            scale: 1.05,
            duration: 0.3,
            ease: "back.out(1.7)"
        });
    }
}

function showNyquistPlot() {
    const canvas = document.getElementById('impedanceCanvas');
    if (!canvas) return;
    
    const ctx = canvas.getContext('2d');
    drawImpedancePlot(ctx, canvas.width, canvas.height, 'nyquist');
    
    if (typeof gsap !== 'undefined') {
        gsap.from(canvas, {
            scale: 1.05,
            duration: 0.3,
            ease: "back.out(1.7)"
        });
    }
}

function showGoodContact() {
    const canvas = document.getElementById('signalQualityCanvas');
    if (!canvas) return;
    
    const ctx = canvas.getContext('2d');
    drawSignalQuality(ctx, canvas.width, canvas.height, 'good');
}

function showPoorContact() {
    const canvas = document.getElementById('signalQualityCanvas');
    if (!canvas) return;
    
    const ctx = canvas.getContext('2d');
    drawSignalQuality(ctx, canvas.width, canvas.height, 'poor');
}

function showMotionArtifact() {
    const canvas = document.getElementById('signalQualityCanvas');
    if (!canvas) return;
    
    const ctx = canvas.getContext('2d');
    drawSignalQuality(ctx, canvas.width, canvas.height, 'motion');
}

// Add CSS for interactive elements
const style = document.createElement('style');
style.textContent = `
    .electrode-types {
        display: flex;
        justify-content: center;
        gap: 2rem;
        margin: 2rem 0;
    }
    
    .electrode-type {
        background: linear-gradient(135deg, #3498db, #2980b9);
        color: white;
        padding: 1rem 2rem;
        border-radius: 8px;
        font-weight: bold;
        text-align: center;
        min-width: 120px;
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        transition: all 0.3s ease;
    }
    
    .electrode-type:hover {
        transform: translateY(-3px);
        box-shadow: 0 6px 12px rgba(0,0,0,0.15);
    }
    
    .interface-fundamentals {
        background: white;
        border-radius: 8px;
        padding: 2rem;
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    }
    
    .layer-component {
        background: #f8f9fa;
        border-radius: 6px;
        padding: 1rem;
        margin: 1rem 0;
        border-left: 4px solid #3498db;
    }
    
    .thickness {
        background: #e8f5e8;
        color: #27ae60;
        padding: 0.25rem 0.5rem;
        border-radius: 4px;
        font-size: 0.8rem;
        font-weight: bold;
        display: inline-block;
        margin-top: 0.5rem;
    }
    
    .reaction-item {
        background: #f8f9fa;
        border-radius: 6px;
        padding: 1rem;
        margin: 1rem 0;
        border-left: 4px solid #e74c3c;
    }
    
    .characteristic {
        background: #fff3cd;
        color: #856404;
        padding: 0.25rem 0.5rem;
        border-radius: 4px;
        font-size: 0.8rem;
        font-weight: bold;
        display: inline-block;
        margin-top: 0.5rem;
    }
    
    .circuit-diagram {
        background: white;
        border-radius: 8px;
        padding: 1rem;
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        margin: 1rem 0;
        text-align: center;
    }
    
    .circuit-diagram svg {
        transition: all 0.3s ease;
    }
    
    .circuit-diagram rect, .circuit-diagram path {
        transition: all 0.3s ease;
        cursor: pointer;
    }
    
    .electrode-category {
        background: white;
        border-radius: 8px;
        padding: 2rem;
        margin: 1.5rem 0;
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        transition: all 0.3s ease;
        border-left: 4px solid #3498db;
    }
    
    .electrode-details {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1.5rem;
        margin-top: 1rem;
    }
    
    .construction, .advantages, .applications, .types, .characteristics {
        background: #f8f9fa;
        border-radius: 6px;
        padding: 1rem;
    }
    
    .construction h5, .advantages h5, .applications h5, .types h5, .characteristics h5 {
        color: #2c3e50;
        margin-bottom: 0.5rem;
        border-bottom: 2px solid #3498db;
        padding-bottom: 0.25rem;
    }
    
    .construction ul, .advantages ul, .applications ul, .types ul, .characteristics ul {
        list-style-type: none;
        padding-left: 0;
        margin: 0;
    }
    
    .construction li, .advantages li, .applications li, .types li, .characteristics li {
        padding: 0.25rem 0;
        padding-left: 1rem;
        position: relative;
    }
    
    .construction li:before, .advantages li:before, .applications li:before, .types li:before, .characteristics li:before {
        content: "•";
        position: absolute;
        left: 0;
        color: #3498db;
        font-weight: bold;
    }
    
    .electrode-table {
        width: 100%;
        border-collapse: collapse;
        margin: 1rem 0;
        font-size: 0.9rem;
    }
    
    .electrode-table th, .electrode-table td {
        padding: 0.8rem;
        border: 1px solid #dee2e6;
        text-align: center;
    }
    
    .electrode-table th {
        background: #3498db;
        color: white;
        font-weight: bold;
    }
    
    .electrode-table tr:nth-child(even) {
        background: #f8f9fa;
    }
    
    .placement-factors {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1rem;
        margin-top: 1rem;
    }
    
    .factor-item {
        background: #f8f9fa;
        border-radius: 6px;
        padding: 1rem;
        border-left: 4px solid #27ae60;
    }
    
    .factor-item h5 {
        color: #27ae60;
        margin-bottom: 0.5rem;
    }
    
    .factor-item p {
        color: #7f8c8d;
        font-size: 0.9rem;
        line-height: 1.4;
        margin: 0;
    }
    
    .impedance-controls, .quality-controls {
        text-align: center;
        margin-top: 1rem;
    }
    
    .impedance-controls .demo-btn, .quality-controls .demo-btn {
        margin: 0 0.5rem;
        padding: 0.5rem 1rem;
        background: #3498db;
        color: white;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        font-size: 0.9rem;
        transition: all 0.3s ease;
    }
    
    .impedance-controls .demo-btn:hover, .quality-controls .demo-btn:hover {
        background: #2980b9;
        transform: translateY(-2px);
    }
`;
document.head.appendChild(style);
