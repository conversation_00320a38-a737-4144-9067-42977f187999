// Interactive elements for Lecture 3.1: Cardiac Electrophysiology

document.addEventListener('DOMContentLoaded', function() {
    console.log('Lecture 3.1 Interactive JS Loaded');

    // Initialize any specific interactive elements for this lecture
    setupConductionSystemInteractivity();
    setupECGWaveInteractivity();
    setupSANodeInteractivity(); // Added this line

    // Call placeholder animations if their elements exist
    if (document.getElementById('cardiacIntroAnimation')) {
        animateCardiacIntro();
    }
    if (document.getElementById('pacemakerPotentialAnimation')) {
        animatePacemakerPotential();
    }
});

// Placeholder for Intro Animation on Slide 1
function animateCardiacIntro() {
    const introElement = document.getElementById('cardiacIntroAnimation');
    if (introElement && typeof gsap !== 'undefined') {
        // Example: Simple fade in and pulse
        gsap.fromTo(introElement,
            { opacity: 0, scale: 0.8 },
            { opacity: 1, scale: 1, duration: 1, ease: "power2.out" }
        );
        gsap.to(introElement, {
            scale: 1.05,
            duration: 1.5,
            repeat: -1,
            yoyo: true,
            ease: "sine.inOut",
            delay: 1
        });
        console.log("Placeholder: Animate Cardiac Intro called.");
    } else {
        console.log("Cardiac intro animation element not found or GSAP not loaded.");
    }
}

// Placeholder for Conduction Path Animation on Slide 3
function animateConductionPath() {
    console.log("Placeholder: Animate Conduction Path button clicked.");
    alert("Animation: Visualizing the electrical impulse travelling through the heart's conduction system (SA node -> AV node -> Bundle of His -> Purkinje fibers).");
    // Future: Implement GSAP animation on the conduction system diagram
    const conductionDiagram = document.getElementById('conductionSystemBlockDiagram');
    if (conductionDiagram && typeof gsap !== 'undefined') {
        // Example: Flash parts of the diagram in sequence
        const parts = ['SA Node', 'AV Node', 'Bundle of His', 'Purkinje Fibers']; // Match to actual diagram elements if SVG
        let delay = 0;
        parts.forEach(partName => {
            // This is conceptual. Actual implementation depends on diagram structure (SVG elements, etc.)
            // For an img, this is harder. If it were SVG, you'd select elements by ID/class.
            gsap.fromTo(conductionDiagram, {boxShadow: "0 0 0px #e74c3c"}, {
                duration: 0.5,
                boxShadow: "0 0 20px 5px #e74c3c", // Glow effect
                repeat: 1,
                yoyo: true,
                delay: delay,
                onStart: () => console.log(`Animating: ${partName}`)
            });
            delay += 0.7;
        });
    }
}
window.animateConductionPath = animateConductionPath; // Make it globally accessible for inline onclick

// Setup for interactive icons on Conduction System (Slide 3)
function setupConductionSystemInteractivity() {
    const icons = document.querySelectorAll('.interactive-icon[data-target^="svg-"]');
    const svgDiagram = document.getElementById('conductionSystemSvg');

    if (!svgDiagram) {
        console.warn("Conduction system SVG not found for interactivity.");
        return;
    }

    // Store original styles to revert
    const originalStyles = new Map();

    icons.forEach(icon => {
        icon.addEventListener('click', function() {
            const targetId = this.dataset.target;
            console.log(`Interactive icon clicked: Target ID ${targetId}`);

            // Reset styles for all parts
            svgDiagram.querySelectorAll('.conduction-part').forEach(part => {
                const partId = part.id;
                if (originalStyles.has(partId)) {
                    const styleToRestore = originalStyles.get(partId);
                    part.querySelectorAll('ellipse, rect, path').forEach(el => {
                        el.style.fill = styleToRestore.fill;
                        el.style.stroke = styleToRestore.stroke;
                        el.style.strokeWidth = styleToRestore.strokeWidth;
                    });
                } else { // Fallback if not stored, try to reset to a default or remove inline styles
                    part.querySelectorAll('ellipse, rect, path').forEach(el => {
                        el.style.fill = ''; // Let CSS handle it or use a default
                        el.style.stroke = '';
                        el.style.strokeWidth = '';
                    });
                }
            });
            
            // Remove highlight from other icons
            icons.forEach(i => i.classList.remove('highlighted'));
            this.classList.add('highlighted');

            const targetElement = svgDiagram.querySelector(`#${targetId}`);
            if (targetElement) {
                console.log(`Highlighting SVG element: #${targetId}`);
                // Store original styles before changing
                if (!originalStyles.has(targetId)) {
                    const firstShape = targetElement.querySelector('ellipse, rect, path');
                    if (firstShape) {
                        originalStyles.set(targetId, {
                            fill: firstShape.style.fill || getComputedStyle(firstShape).fill,
                            stroke: firstShape.style.stroke || getComputedStyle(firstShape).stroke,
                            strokeWidth: firstShape.style.strokeWidth || getComputedStyle(firstShape).strokeWidth,
                        });
                    }
                }

                // Apply highlight style using GSAP for a temporary effect
                targetElement.querySelectorAll('ellipse, rect, path').forEach(el => {
                    if (typeof gsap !== 'undefined') {
                        gsap.to(el, {
                            fill: "#ff7675", // A bright highlight color
                            stroke: "#d63031",
                            strokeWidth: (parseFloat(getComputedStyle(el).strokeWidth) || 1) + 1, // Increase stroke width
                            duration: 0.3,
                            onComplete: () => {
                                // Optionally revert after a delay, or keep highlighted until another is clicked
                                // For now, it stays highlighted until another icon click resets it.
                            }
                        });
                    } else { // Fallback if GSAP is not available
                        el.style.fill = "#ff7675";
                        el.style.stroke = "#d63031";
                        el.style.strokeWidth = `${(parseFloat(getComputedStyle(el).strokeWidth) || 1) + 1}px`;
                    }
                });
            } else {
                console.warn(`Target SVG element #${targetId} not found.`);
            }
        });
    });
}


// Placeholder for Pacemaker Potential Animation on Slide 4
function animatePacemakerPotential() {
    const animElement = document.getElementById('pacemakerPotentialAnimation');
    if (animElement && typeof gsap !== 'undefined') {
        // Example: Text change or simple visual
        animElement.innerHTML = '<p>Animating Pacemaker Potential...</p>';
        gsap.fromTo(animElement,
            { opacity: 0.5, y: 10 },
            { opacity: 1, y: 0, duration: 1, repeat: -1, yoyo: true, ease: "sine.inOut" }
        );
        console.log("Placeholder: Animate Pacemaker Potential called.");
    } else {
        console.log("Pacemaker potential animation element not found or GSAP not loaded.");
    }
}

// ECG Wave Highlighting and Correlation on Slide 5
function highlightEcgWave(waveType) {
    console.log(`Highlighting ECG Wave: ${waveType}`);
    const svg = document.getElementById('heartEcgSvg');
    if (!svg || typeof gsap === 'undefined') {
        alert(`Cannot highlight ${waveType} wave. SVG or GSAP not found.`);
        return;
    }

    // Reset previous highlights
    svg.querySelectorAll('.ecg-wave, .ecg-segment').forEach(el => {
        gsap.to(el, { strokeWidth: 2, stroke: (el.classList.contains('ecg-wave') ? "#c0392b" : "#7f8c8d"), duration: 0.3 });
    });
    svg.querySelectorAll('.ecg-event-highlight').forEach(el => {
        gsap.to(el, { opacity: 0, fill: "rgba(0,0,0,0)", duration: 0.3 });
    });

    let targetWaveId = '';
    let targetEventId = '';
    let eventFillColor = '';

    switch (waveType.toUpperCase()) {
        case 'P':
            targetWaveId = 'svg-p-wave';
            targetEventId = 'svg-atrial-depol';
            eventFillColor = 'rgba(231, 76, 60, 0.5)'; // Reddish for depolarization
            alert("P Wave: Represents atrial depolarization. The atria are contracting.");
            break;
        case 'QRS':
            targetWaveId = 'svg-qrs-complex';
            targetEventId = 'svg-ventricular-depol';
            eventFillColor = 'rgba(231, 76, 60, 0.6)'; // Stronger Reddish
            alert("QRS Complex: Represents ventricular depolarization. The ventricles are contracting.");
            break;
        case 'T':
            targetWaveId = 'svg-t-wave';
            targetEventId = 'svg-ventricular-repol';
            eventFillColor = 'rgba(46, 204, 113, 0.5)'; // Greenish for repolarization
            alert("T Wave: Represents ventricular repolarization. The ventricles are relaxing.");
            break;
        default:
            console.warn(`Unknown wave type: ${waveType}`);
            return;
    }

    // Highlight the selected ECG wave
    const waveElement = svg.querySelector(`#${targetWaveId}`);
    if (waveElement) {
        gsap.to(waveElement, { strokeWidth: 4, stroke: "#e74c3c", duration: 0.3, yoyo: true, repeat: 1 });
    }

    // Highlight the corresponding cardiac event area
    const eventElement = svg.querySelector(`#${targetEventId}`);
    if (eventElement) {
        gsap.to(eventElement, { opacity: 1, fill: eventFillColor, duration: 0.5, yoyo: true, repeat: 1, delay:0.1 });
    }
}
window.highlightEcgWave = highlightEcgWave; // Make it globally accessible

function setupECGWaveInteractivity() {
    // Buttons are already set up with onclick, this function can be used for more complex SVG interactions if needed later.
    console.log("ECG Wave interactivity setup (buttons use global function).");
}

function setupSANodeInteractivity() {
    const saNodeSvg = document.getElementById('saNodeCellSvg');
    if (!saNodeSvg) {
        console.warn("SA Node Cell SVG not found for interactivity.");
        return;
    }

    const channels = saNodeSvg.querySelectorAll('.sa-channel');
    channels.forEach(channel => {
        channel.style.cursor = 'pointer'; // Make it look clickable
        channel.addEventListener('click', function() {
            const channelName = this.dataset.channelName || "Selected Channel"; // Use data-channel-name from SVG
            const channelId = this.id;
            console.log(`SA Node Channel clicked: ${channelName} (ID: ${channelId})`);

            // Remove existing tooltips to prevent overlap
            document.querySelectorAll('.sa-channel-tooltip').forEach(tip => tip.remove());

            // Create and show a simple tooltip
            const tooltip = document.createElement('div');
            tooltip.className = 'sa-channel-tooltip'; // For styling and removal
            tooltip.style.position = 'absolute';
            tooltip.style.background = 'rgba(44, 62, 80, 0.9)'; // Dark background
            tooltip.style.color = 'white';
            tooltip.style.padding = '8px 12px';
            tooltip.style.borderRadius = '5px';
            tooltip.style.fontSize = '12px';
            tooltip.style.zIndex = '1001'; // Ensure it's above other elements
            tooltip.style.pointerEvents = 'none'; // So it doesn't interfere with other clicks
            tooltip.textContent = getSANodeChannelInfo(channelId);

            document.body.appendChild(tooltip);

            const rect = this.getBoundingClientRect();
            tooltip.style.left = `${rect.left + window.scrollX}px`;
            tooltip.style.top = `${rect.bottom + window.scrollY + 5}px`; // Position below the channel

            // Animate the channel (e.g., brief highlight)
            if (typeof gsap !== 'undefined') {
                const shapes = this.querySelectorAll('rect, ellipse, path'); // Target shapes within the group
                gsap.fromTo(shapes,
                    { scale: 1, fill: gsap.getProperty(shapes[0], "fill") }, // Get current fill if possible
                    {
                        scale: 1.15,
                        fill: "#fdcb6e", // A distinct highlight fill
                        duration: 0.2,
                        yoyo: true,
                        repeat: 1,
                        transformOrigin: "center center",
                        ease: "power1.inOut"
                    }
                );
            }

            // Remove tooltip after a few seconds
            setTimeout(() => {
                tooltip.remove();
            }, 4000);
        });
    });
}

function getSANodeChannelInfo(channelId) {
    switch (channelId) {
        case 'svg-if-channel':
            return "Funny Current (If): Primarily Na⁺ influx. Contributes to early diastolic depolarization. Activated by hyperpolarization.";
        case 'svg-caT-channel':
            return "T-type Ca²⁺ Channel: Transient Ca²⁺ influx. Contributes to late diastolic depolarization, helping reach threshold.";
        case 'svg-caL-channel':
            return "L-type Ca²⁺ Channel: Long-lasting Ca²⁺ influx. Responsible for the upstroke of the SA node action potential.";
        case 'svg-k-channel':
            return "K⁺ Channel: K⁺ efflux. Responsible for repolarization of the SA node action potential.";
        default:
            return "Information about this SA node ion channel.";
    }
}

// Add more functions here for other interactive elements as needed for Lecture 3.1