<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Lecture 1.4: Medical Device Safety & Standards</title>
    <link rel="stylesheet" href="../../css/style.css">
    <link rel="stylesheet" href="lecture-slides.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/gsap.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/3.9.1/chart.min.js"></script>
</head>
<body class="lecture-mode">
    <header class="lecture-header">
        <div class="container">
            <div class="lecture-navigation">
                <a href="../../index.html" class="nav-btn">🏠 Home</a>
                <a href="../../modules/module1/module1-description.html" class="nav-btn">📚 Module 1</a>
                <div class="lecture-info">
                    <span class="lecture-title">Lecture 1.4: Medical Device Safety & Standards</span>
                    <span class="slide-counter">Slide <span id="currentSlide">1</span> of <span id="totalSlides">12</span></span>
                </div>
                <div class="lecture-controls">
                    <button type="button" id="prevSlide" class="control-btn">⬅️ Previous</button>
                    <button type="button" id="nextSlide" class="control-btn">➡️ Next</button>
                    <button type="button" id="fullscreenBtn" class="control-btn">🔍 Fullscreen</button>
                </div>
            </div>
        </div>
    </header>

    <main class="lecture-content">
        <div class="slides-container" id="slidesContainer">

            <!-- Slide 1: Title Slide -->
            <div class="slide active" data-slide="1">
                <div class="slide-content title-slide">
                    <h1>🛡️ Medical Device Safety & Standards</h1>
                    <h2>Module 1 - Lecture 1.4</h2>
                    <div class="title-info">
                        <p><strong>Ensuring Patient Safety in Medical Instrumentation</strong></p>
                        <p>Regulatory frameworks, safety standards, and risk management in biomedical engineering</p>
                    </div>
                    <div class="author-info">
                        <p><strong>Dr. Mohammed Yagoub Esmail</strong></p>
                        <p>Sudan University of Science and Technology (SUST)</p>
                        <p>Biomedical Engineering Department - 2025</p>
                    </div>
                    <div class="safety-icons-preview" id="safetyPreview">
                        <div class="safety-icon">🛡️</div>
                        <div class="safety-icon">⚖️</div>
                        <div class="safety-icon">🔒</div>
                        <div class="safety-icon">✅</div>
                    </div>
                </div>
            </div>

            <!-- Slide 2: Learning Objectives -->
            <div class="slide" data-slide="2">
                <div class="slide-content">
                    <h2>🎯 Learning Objectives</h2>
                    <div class="objectives-list">
                        <div class="objective-item" data-animate="1">
                            <div class="objective-icon">🛡️</div>
                            <div class="objective-text">
                                <h3>Understand Patient Safety Principles</h3>
                                <p>Learn fundamental safety concepts and risk assessment methodologies in medical device design</p>
                            </div>
                        </div>
                        <div class="objective-item" data-animate="2">
                            <div class="objective-icon">⚖️</div>
                            <div class="objective-text">
                                <h3>Explore Regulatory Frameworks</h3>
                                <p>Understand FDA, CE marking, and international standards governing medical device approval</p>
                            </div>
                        </div>
                        <div class="objective-item" data-animate="3">
                            <div class="objective-icon">📋</div>
                            <div class="objective-text">
                                <h3>Master Safety Standards</h3>
                                <p>Study IEC 60601 series and other critical standards for medical electrical equipment</p>
                            </div>
                        </div>
                        <div class="objective-item" data-animate="4">
                            <div class="objective-icon">🔧</div>
                            <div class="objective-text">
                                <h3>Apply Risk Management</h3>
                                <p>Implement ISO 14971 risk management processes in medical device development</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Slide 3: Medical Device Classification -->
            <div class="slide" data-slide="3">
                <div class="slide-content">
                    <h2>📊 Medical Device Classification</h2>
                    <div class="content-grid">
                        <div class="content-left">
                            <div class="classification-system">
                                <h3>🏥 FDA Classification System</h3>
                                <div class="class-hierarchy">
                                    <div class="class-item" data-class="1">
                                        <div class="class-header">
                                            <span class="class-badge class-i">Class I</span>
                                            <h4>Low Risk</h4>
                                        </div>
                                        <div class="class-content">
                                            <p><strong>Examples:</strong> Stethoscopes, bandages, examination gloves</p>
                                            <p><strong>Controls:</strong> General controls only</p>
                                            <p><strong>Premarket:</strong> Most exempt from 510(k)</p>
                                            <p><strong>Risk Level:</strong> Minimal potential for harm</p>
                                        </div>
                                    </div>

                                    <div class="class-item" data-class="2">
                                        <div class="class-header">
                                            <span class="class-badge class-ii">Class II</span>
                                            <h4>Moderate Risk</h4>
                                        </div>
                                        <div class="class-content">
                                            <p><strong>Examples:</strong> ECG machines, X-ray equipment, infusion pumps</p>
                                            <p><strong>Controls:</strong> General + special controls</p>
                                            <p><strong>Premarket:</strong> 510(k) clearance required</p>
                                            <p><strong>Risk Level:</strong> Moderate potential for harm</p>
                                        </div>
                                    </div>

                                    <div class="class-item" data-class="3">
                                        <div class="class-header">
                                            <span class="class-badge class-iii">Class III</span>
                                            <h4>High Risk</h4>
                                        </div>
                                        <div class="class-content">
                                            <p><strong>Examples:</strong> Pacemakers, heart valves, defibrillators</p>
                                            <p><strong>Controls:</strong> General + special + premarket approval</p>
                                            <p><strong>Premarket:</strong> PMA (Premarket Approval) required</p>
                                            <p><strong>Risk Level:</strong> High potential for harm or death</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="content-right">
                            <div class="classification-factors">
                                <h3>🔍 Classification Factors</h3>
                                <div class="factor-item">
                                    <h4>Intended Use</h4>
                                    <p>• Diagnostic vs. therapeutic<br>
                                       • Duration of contact<br>
                                       • Body system affected</p>
                                </div>
                                <div class="factor-item">
                                    <h4>Degree of Risk</h4>
                                    <p>• Potential for harm<br>
                                       • Invasiveness level<br>
                                       • Life-supporting function</p>
                                </div>
                                <div class="factor-item">
                                    <h4>Technological Characteristics</h4>
                                    <p>• Energy source<br>
                                       • Software complexity<br>
                                       • Novel technology</p>
                                </div>
                            </div>

                            <div class="regulatory-pathways">
                                <h3>🛤️ Regulatory Pathways</h3>
                                <div class="pathway-chart">
                                    <div class="pathway-step">
                                        <span class="step-icon">📋</span>
                                        <span class="step-text">Device Classification</span>
                                    </div>
                                    <div class="pathway-arrow">⬇️</div>
                                    <div class="pathway-step">
                                        <span class="step-icon">🔍</span>
                                        <span class="step-text">Predicate Device Search</span>
                                    </div>
                                    <div class="pathway-arrow">⬇️</div>
                                    <div class="pathway-step">
                                        <span class="step-icon">📄</span>
                                        <span class="step-text">510(k) or PMA Submission</span>
                                    </div>
                                    <div class="pathway-arrow">⬇️</div>
                                    <div class="pathway-step">
                                        <span class="step-icon">✅</span>
                                        <span class="step-text">FDA Review & Clearance</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Slide 4: IEC 60601 Standards -->
            <div class="slide" data-slide="4">
                <div class="slide-content">
                    <h2>📋 IEC 60601: Medical Electrical Equipment Standards</h2>
                    <div class="content-grid">
                        <div class="content-left">
                            <div class="iec-structure">
                                <h3>🏗️ IEC 60601 Structure</h3>
                                <div class="standard-hierarchy">
                                    <div class="standard-level" data-level="general">
                                        <div class="level-header">
                                            <span class="level-badge general">General</span>
                                            <h4>IEC 60601-1</h4>
                                        </div>
                                        <div class="level-content">
                                            <p><strong>Scope:</strong> General requirements for basic safety and essential performance</p>
                                            <p><strong>Key Areas:</strong></p>
                                            <ul>
                                                <li>Electrical safety</li>
                                                <li>Mechanical safety</li>
                                                <li>Protection against radiation</li>
                                                <li>Usability engineering</li>
                                                <li>Risk management</li>
                                            </ul>
                                        </div>
                                    </div>

                                    <div class="standard-level" data-level="collateral">
                                        <div class="level-header">
                                            <span class="level-badge collateral">Collateral</span>
                                            <h4>IEC 60601-1-X</h4>
                                        </div>
                                        <div class="level-content">
                                            <p><strong>Examples:</strong></p>
                                            <ul>
                                                <li>60601-1-2: EMC requirements</li>
                                                <li>60601-1-6: Usability</li>
                                                <li>60601-1-8: Alarm systems</li>
                                                <li>60601-1-11: Home healthcare</li>
                                                <li>60601-1-12: Emergency medical services</li>
                                            </ul>
                                        </div>
                                    </div>

                                    <div class="standard-level" data-level="particular">
                                        <div class="level-header">
                                            <span class="level-badge particular">Particular</span>
                                            <h4>IEC 60601-2-X</h4>
                                        </div>
                                        <div class="level-content">
                                            <p><strong>Device-Specific Standards:</strong></p>
                                            <ul>
                                                <li>60601-2-25: ECG equipment</li>
                                                <li>60601-2-27: ECG monitoring</li>
                                                <li>60601-2-40: EMG equipment</li>
                                                <li>60601-2-26: EEG equipment</li>
                                                <li>60601-2-47: Ambulatory ECG</li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="content-right">
                            <div class="safety-requirements">
                                <h3>🛡️ Key Safety Requirements</h3>
                                <div class="requirement-category">
                                    <h4>Electrical Safety</h4>
                                    <div class="requirement-list">
                                        <div class="req-item">
                                            <span class="req-icon">⚡</span>
                                            <span class="req-text">Leakage current limits</span>
                                        </div>
                                        <div class="req-item">
                                            <span class="req-icon">🔌</span>
                                            <span class="req-text">Protective earthing</span>
                                        </div>
                                        <div class="req-item">
                                            <span class="req-icon">🛡️</span>
                                            <span class="req-text">Patient isolation</span>
                                        </div>
                                    </div>
                                </div>

                                <div class="requirement-category">
                                    <h4>Mechanical Safety</h4>
                                    <div class="requirement-list">
                                        <div class="req-item">
                                            <span class="req-icon">🔧</span>
                                            <span class="req-text">Mechanical strength</span>
                                        </div>
                                        <div class="req-item">
                                            <span class="req-icon">🚫</span>
                                            <span class="req-text">Sharp edges protection</span>
                                        </div>
                                        <div class="req-item">
                                            <span class="req-icon">🔒</span>
                                            <span class="req-text">Moving parts safety</span>
                                        </div>
                                    </div>
                                </div>

                                <div class="requirement-category">
                                    <h4>Environmental Safety</h4>
                                    <div class="requirement-list">
                                        <div class="req-item">
                                            <span class="req-icon">🌡️</span>
                                            <span class="req-text">Temperature limits</span>
                                        </div>
                                        <div class="req-item">
                                            <span class="req-icon">💧</span>
                                            <span class="req-text">Ingress protection</span>
                                        </div>
                                        <div class="req-item">
                                            <span class="req-icon">📡</span>
                                            <span class="req-text">EMC compliance</span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="testing-requirements">
                                <h3>🧪 Testing Requirements</h3>
                                <div class="test-types">
                                    <div class="test-item">
                                        <h5>Type Testing</h5>
                                        <p>Comprehensive testing of design</p>
                                    </div>
                                    <div class="test-item">
                                        <h5>Routine Testing</h5>
                                        <p>Production line verification</p>
                                    </div>
                                    <div class="test-item">
                                        <h5>After Repair Testing</h5>
                                        <p>Post-service verification</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Slide 5: Risk Management -->
            <div class="slide" data-slide="5">
                <div class="slide-content">
                    <h2>⚠️ Risk Management: ISO 14971</h2>
                    <div class="content-grid">
                        <div class="content-left">
                            <div class="risk-process">
                                <h3>🔄 Risk Management Process</h3>
                                <div class="process-flow">
                                    <div class="process-step" data-step="1">
                                        <div class="step-header">
                                            <span class="step-number">1</span>
                                            <h4>Risk Analysis</h4>
                                        </div>
                                        <div class="step-content">
                                            <p>• Identify intended use and foreseeable misuse<br>
                                               • Identify hazards and hazardous situations<br>
                                               • Estimate risk for each hazardous situation</p>
                                        </div>
                                    </div>

                                    <div class="process-step" data-step="2">
                                        <div class="step-header">
                                            <span class="step-number">2</span>
                                            <h4>Risk Evaluation</h4>
                                        </div>
                                        <div class="step-content">
                                            <p>• Compare estimated risks to risk criteria<br>
                                               • Determine if risk reduction is required<br>
                                               • Document risk acceptability decisions</p>
                                        </div>
                                    </div>

                                    <div class="process-step" data-step="3">
                                        <div class="step-header">
                                            <span class="step-number">3</span>
                                            <h4>Risk Control</h4>
                                        </div>
                                        <div class="step-content">
                                            <p>• Implement risk control measures<br>
                                               • Verify effectiveness of controls<br>
                                               • Ensure no new risks are introduced</p>
                                        </div>
                                    </div>

                                    <div class="process-step" data-step="4">
                                        <div class="step-header">
                                            <span class="step-number">4</span>
                                            <h4>Residual Risk</h4>
                                        </div>
                                        <div class="step-content">
                                            <p>• Evaluate residual risk acceptability<br>
                                               • Consider benefit-risk analysis<br>
                                               • Document final risk assessment</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="content-right">
                            <div class="risk-matrix">
                                <h3>📊 Risk Assessment Matrix</h3>
                                <div class="matrix-container">
                                    <table class="risk-table">
                                        <thead>
                                            <tr>
                                                <th rowspan="2">Severity</th>
                                                <th colspan="5">Probability of Occurrence</th>
                                            </tr>
                                            <tr>
                                                <th>Very Low</th>
                                                <th>Low</th>
                                                <th>Medium</th>
                                                <th>High</th>
                                                <th>Very High</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td class="severity-label">Catastrophic</td>
                                                <td class="risk-cell medium">M</td>
                                                <td class="risk-cell high">H</td>
                                                <td class="risk-cell high">H</td>
                                                <td class="risk-cell very-high">VH</td>
                                                <td class="risk-cell very-high">VH</td>
                                            </tr>
                                            <tr>
                                                <td class="severity-label">Critical</td>
                                                <td class="risk-cell low">L</td>
                                                <td class="risk-cell medium">M</td>
                                                <td class="risk-cell high">H</td>
                                                <td class="risk-cell high">H</td>
                                                <td class="risk-cell very-high">VH</td>
                                            </tr>
                                            <tr>
                                                <td class="severity-label">Serious</td>
                                                <td class="risk-cell low">L</td>
                                                <td class="risk-cell low">L</td>
                                                <td class="risk-cell medium">M</td>
                                                <td class="risk-cell high">H</td>
                                                <td class="risk-cell high">H</td>
                                            </tr>
                                            <tr>
                                                <td class="severity-label">Minor</td>
                                                <td class="risk-cell very-low">VL</td>
                                                <td class="risk-cell low">L</td>
                                                <td class="risk-cell low">L</td>
                                                <td class="risk-cell medium">M</td>
                                                <td class="risk-cell high">H</td>
                                            </tr>
                                            <tr>
                                                <td class="severity-label">Negligible</td>
                                                <td class="risk-cell very-low">VL</td>
                                                <td class="risk-cell very-low">VL</td>
                                                <td class="risk-cell low">L</td>
                                                <td class="risk-cell low">L</td>
                                                <td class="risk-cell medium">M</td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>

                                <div class="risk-legend">
                                    <h4>Risk Levels</h4>
                                    <div class="legend-item">
                                        <span class="legend-color very-low"></span>
                                        <span>Very Low (VL) - Acceptable</span>
                                    </div>
                                    <div class="legend-item">
                                        <span class="legend-color low"></span>
                                        <span>Low (L) - Acceptable</span>
                                    </div>
                                    <div class="legend-item">
                                        <span class="legend-color medium"></span>
                                        <span>Medium (M) - ALARP</span>
                                    </div>
                                    <div class="legend-item">
                                        <span class="legend-color high"></span>
                                        <span>High (H) - Unacceptable</span>
                                    </div>
                                    <div class="legend-item">
                                        <span class="legend-color very-high"></span>
                                        <span>Very High (VH) - Unacceptable</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Slide 6: Summary -->
            <div class="slide" data-slide="6">
                <div class="slide-content">
                    <h2>📋 Module 1 Complete Summary</h2>
                    <div class="module-summary">
                        <div class="summary-overview">
                            <h3>🎓 What We've Learned</h3>
                            <p>We've completed a comprehensive journey from the fundamental cellular origins of bioelectric signals to the sophisticated safety standards governing medical instrumentation.</p>
                        </div>

                        <div class="summary-grid">
                            <div class="summary-section">
                                <h3>🧬 Lecture 1.1: Cellular Foundations</h3>
                                <ul>
                                    <li>Cell membrane structure and ion channels</li>
                                    <li>Nernst equation and equilibrium potentials</li>
                                    <li>Resting membrane potential mechanisms</li>
                                    <li>Voltage-gated channel kinetics</li>
                                </ul>
                            </div>
                            <div class="summary-section">
                                <h3>⚡ Lecture 1.2: Action Potentials</h3>
                                <ul>
                                    <li>Action potential generation and phases</li>
                                    <li>Hodgkin-Huxley model</li>
                                    <li>Refractory periods and signal integrity</li>
                                    <li>Signal propagation and conduction velocity</li>
                                </ul>
                            </div>
                            <div class="summary-section">
                                <h3>🫀 Lecture 1.3: Organ System Signals</h3>
                                <ul>
                                    <li>Signal integration from cells to organs</li>
                                    <li>Cardiac electrophysiology and ECG</li>
                                    <li>Neural signal integration and EEG</li>
                                    <li>Motor unit recruitment and EMG</li>
                                </ul>
                            </div>
                            <div class="summary-section">
                                <h3>🛡️ Lecture 1.4: Safety & Standards</h3>
                                <ul>
                                    <li>Medical device classification systems</li>
                                    <li>IEC 60601 safety standards</li>
                                    <li>ISO 14971 risk management</li>
                                    <li>Regulatory approval pathways</li>
                                </ul>
                            </div>
                        </div>

                        <div class="next-module">
                            <h3>🔜 Next: Module 2 - Biomedical Signal Acquisition</h3>
                            <p>Now that we understand the origins and safety requirements of bioelectric signals, we'll explore the sophisticated techniques used to capture and condition these signals for medical instrumentation.</p>
                            <div class="module-preview">
                                <div class="preview-topics">
                                    <span class="topic-tag">🔄 Transduction Principles</span>
                                    <span class="topic-tag">🔘 Electrode Systems</span>
                                    <span class="topic-tag">📈 Signal Conditioning</span>
                                    <span class="topic-tag">💻 ADC Systems</span>
                                </div>
                            </div>
                            <a href="../../modules/module2/module2-description.html" class="next-module-btn">➡️ Continue to Module 2</a>
                        </div>
                    </div>
                </div>
            </div>

        </div>
    </main>

    <footer class="lecture-footer">
        <div class="container">
            <div class="footer-content">
                <div class="author-info">
                    <p><strong>Dr. Mohammed Yagoub Esmail</strong> | SUST - BME 2025</p>
                    <p>📧 <EMAIL> | 📱 +249912867327, +966538076790</p>
                </div>
                <div class="copyright">
                    <p>&copy; 2025 Dr. Mohammed Yagoub Esmail - All rights reserved</p>
                </div>
            </div>
        </div>
    </footer>

    <script src="lecture-slides.js"></script>
    <script src="lecture1-4-interactive.js"></script>
</body>
</html>
