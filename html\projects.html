<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Projects - Medical Instrumentation Course</title>
    <link rel="stylesheet" href="../css/style.css">
</head>
<body>
    <header>
        <div class="container">
            <h1>Course Projects</h1>
            <p class="subtitle">Detailed Project Information</p>
        </div>
    </header>

    <nav>
        <div class="container">
            <ul>
                <li><a href="../index.html">Home</a></li>
                <li><a href="#ecg">ECG Analysis</a></li>
                <li><a href="#gait">Gait Analysis</a></li>
                <li><a href="#research">Research Project</a></li>
            </ul>
        </div>
    </nav>

    <main>
        <section id="ecg" class="section">
            <div class="container">
                <h2>ECG Analysis Project</h2>
                <div class="project-detail">
                    <h3>Objective</h3>
                    <p>Analyze electrocardiogram signals to extract heart rate information and detect cardiac abnormalities using MATLAB signal processing techniques.</p>
                    
                    <h3>Key Features</h3>
                    <ul>
                        <li>Real-time ECG signal acquisition</li>
                        <li>Digital filtering and noise reduction</li>
                        <li>R-peak detection algorithms</li>
                        <li>Heart rate variability analysis</li>
                        <li>Automated report generation</li>
                    </ul>
                    
                    <h3>Files and Resources</h3>
                    <div class="file-list">
                        <div class="file-item">
                            <span class="file-name">ECG_1.m</span>
                            <span class="file-desc">Basic ECG signal processing</span>
                        </div>
                        <div class="file-item">
                            <span class="file-name">ECG_2.m</span>
                            <span class="file-desc">Advanced analysis algorithms</span>
                        </div>
                        <div class="file-item">
                            <span class="file-name">find_avg_hr.m</span>
                            <span class="file-desc">Heart rate calculation function</span>
                        </div>
                        <div class="file-item">
                            <span class="file-name">ECG_final.pdf</span>
                            <span class="file-desc">Complete project documentation</span>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <section id="gait" class="section">
            <div class="container">
                <h2>Gait Analysis Project</h2>
                <div class="project-detail">
                    <h3>Objective</h3>
                    <p>Analyze human gait patterns using EMG signals to understand muscle activation during walking and identify potential mobility issues.</p>
                    
                    <h3>Key Features</h3>
                    <ul>
                        <li>EMG signal acquisition from leg muscles</li>
                        <li>Gait cycle segmentation</li>
                        <li>Muscle activation pattern analysis</li>
                        <li>Frequency domain analysis</li>
                        <li>Comparative gait assessment</li>
                    </ul>
                    
                    <h3>Files and Resources</h3>
                    <div class="file-list">
                        <div class="file-item">
                            <span class="file-name">EMG_analysis.m</span>
                            <span class="file-desc">EMG signal processing and analysis</span>
                        </div>
                        <div class="file-item">
                            <span class="file-name">gait.m</span>
                            <span class="file-desc">Main gait analysis script</span>
                        </div>
                        <div class="file-item">
                            <span class="file-name">iomega.m</span>
                            <span class="file-desc">Data import and preprocessing</span>
                        </div>
                        <div class="file-item">
                            <span class="file-name">gait_lab.pdf</span>
                            <span class="file-desc">Laboratory exercise guide</span>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <section id="research" class="section">
            <div class="container">
                <h2>Research Project</h2>
                <div class="project-detail">
                    <h3>Objective</h3>
                    <p>Develop an Arduino-based heart rate monitoring system with real-time data processing and visualization capabilities.</p>
                    
                    <h3>Key Features</h3>
                    <ul>
                        <li>Arduino-based hardware implementation</li>
                        <li>Real-time heart rate monitoring</li>
                        <li>Digital signal processing</li>
                        <li>Python-based data visualization</li>
                        <li>Wireless data transmission</li>
                    </ul>
                    
                    <h3>Files and Resources</h3>
                    <div class="file-list">
                        <div class="file-item">
                            <span class="file-name">research_proj.ino</span>
                            <span class="file-desc">Arduino main program</span>
                        </div>
                        <div class="file-item">
                            <span class="file-name">digcomp.cpp/.h</span>
                            <span class="file-desc">Digital signal processing library</span>
                        </div>
                        <div class="file-item">
                            <span class="file-name">filtering.m</span>
                            <span class="file-desc">MATLAB filtering algorithms</span>
                        </div>
                        <div class="file-item">
                            <span class="file-name">plot_heart_rate.py</span>
                            <span class="file-desc">Python visualization script</span>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <footer>
        <div class="container">
            <p><a href="../index.html">← Back to Home</a></p>
            <p>&copy; 2025 Medical Instrumentation Course</p>
        </div>
    </footer>

    <script src="../js/main.js"></script>
</body>
</html>
