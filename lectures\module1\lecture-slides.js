// Lecture Slides Navigation and Functionality

class LectureSlides {
    constructor() {
        this.currentSlide = 1;
        this.totalSlides = 0;
        this.slides = [];
        this.isFullscreen = false;

        this.init();
    }

    init() {
        this.slides = document.querySelectorAll('.slide');
        this.totalSlides = this.slides.length;

        // Update slide counter
        const totalSlidesElement = document.getElementById('totalSlides');
        if (totalSlidesElement) {
            totalSlidesElement.textContent = this.totalSlides;
        }

        // Setup event listeners
        this.setupEventListeners();

        // Initialize first slide
        this.showSlide(1);

        // Setup keyboard navigation
        this.setupKeyboardNavigation();

        // Initialize animations
        this.initializeAnimations();

        console.log(`Lecture slides initialized: ${this.totalSlides} slides`);
    }

    setupEventListeners() {
        // Navigation buttons
        document.getElementById('prevSlide').addEventListener('click', () => this.previousSlide());
        document.getElementById('nextSlide').addEventListener('click', () => this.nextSlide());
        document.getElementById('fullscreenBtn').addEventListener('click', () => this.toggleFullscreen());

        // Slide click navigation (optional)
        this.slides.forEach((slide, index) => {
            slide.addEventListener('click', (e) => {
                if (e.target === slide) {
                    this.nextSlide();
                }
            });
        });
    }

    setupKeyboardNavigation() {
        document.addEventListener('keydown', (e) => {
            switch(e.key) {
                case 'ArrowRight':
                case ' ':
                case 'PageDown':
                    e.preventDefault();
                    this.nextSlide();
                    break;
                case 'ArrowLeft':
                case 'PageUp':
                    e.preventDefault();
                    this.previousSlide();
                    break;
                case 'Home':
                    e.preventDefault();
                    this.showSlide(1);
                    break;
                case 'End':
                    e.preventDefault();
                    this.showSlide(this.totalSlides);
                    break;
                case 'F11':
                case 'f':
                    e.preventDefault();
                    this.toggleFullscreen();
                    break;
                case 'Escape':
                    if (this.isFullscreen) {
                        this.exitFullscreen();
                    }
                    break;
            }
        });
    }

    showSlide(slideNumber) {
        if (slideNumber < 1 || slideNumber > this.totalSlides) {
            return;
        }

        // Hide all slides
        this.slides.forEach(slide => {
            slide.classList.remove('active');
        });

        // Show current slide
        const currentSlideElement = document.querySelector(`[data-slide="${slideNumber}"]`);
        if (currentSlideElement) {
            currentSlideElement.classList.add('active');
            this.currentSlide = slideNumber;

            // Update counter
            document.getElementById('currentSlide').textContent = slideNumber;

            // Update navigation buttons
            this.updateNavigationButtons();

            // Trigger slide-specific animations
            this.triggerSlideAnimations(currentSlideElement);

            // Auto-scroll to top
            window.scrollTo(0, 0);
        }
    }

    nextSlide() {
        if (this.currentSlide < this.totalSlides) {
            this.showSlide(this.currentSlide + 1);
        }
    }

    previousSlide() {
        if (this.currentSlide > 1) {
            this.showSlide(this.currentSlide - 1);
        }
    }

    updateNavigationButtons() {
        const prevBtn = document.getElementById('prevSlide');
        const nextBtn = document.getElementById('nextSlide');

        prevBtn.disabled = this.currentSlide === 1;
        nextBtn.disabled = this.currentSlide === this.totalSlides;
    }

    toggleFullscreen() {
        if (!this.isFullscreen) {
            this.enterFullscreen();
        } else {
            this.exitFullscreen();
        }
    }

    enterFullscreen() {
        const element = document.documentElement;

        if (element.requestFullscreen) {
            element.requestFullscreen();
        } else if (element.mozRequestFullScreen) {
            element.mozRequestFullScreen();
        } else if (element.webkitRequestFullscreen) {
            element.webkitRequestFullscreen();
        } else if (element.msRequestFullscreen) {
            element.msRequestFullscreen();
        }

        this.isFullscreen = true;
        document.getElementById('fullscreenBtn').textContent = '🔍 Exit Fullscreen';
    }

    exitFullscreen() {
        if (document.exitFullscreen) {
            document.exitFullscreen();
        } else if (document.mozCancelFullScreen) {
            document.mozCancelFullScreen();
        } else if (document.webkitExitFullscreen) {
            document.webkitExitFullscreen();
        } else if (document.msExitFullscreen) {
            document.msExitFullscreen();
        }

        this.isFullscreen = false;
        document.getElementById('fullscreenBtn').textContent = '🔍 Fullscreen';
    }

    initializeAnimations() {
        // Register GSAP ScrollTrigger if available
        if (typeof gsap !== 'undefined' && gsap.registerPlugin) {
            gsap.registerPlugin(ScrollTrigger);
        }
    }

    triggerSlideAnimations(slideElement) {
        const slideNumber = parseInt(slideElement.dataset.slide);

        switch(slideNumber) {
            case 1:
                this.animateTitleSlide(slideElement);
                break;
            case 2:
                this.animateObjectives(slideElement);
                break;
            case 3:
                this.animateMembraneDiagram(slideElement);
                break;
            case 4:
                this.animateConcentrationTable(slideElement);
                break;
            case 5:
                this.animateRMPFactors(slideElement);
                break;
            case 6:
                this.animateChannelStructure(slideElement);
                break;
            default:
                this.animateGenericSlide(slideElement);
        }
    }

    animateTitleSlide(slide) {
        const cellAnim = slide.querySelector('.cell-membrane-anim');
        if (cellAnim && typeof gsap !== 'undefined') {
            gsap.fromTo(cellAnim,
                { scale: 0, rotation: -180, opacity: 0 },
                { scale: 1, rotation: 0, opacity: 1, duration: 1.5, ease: "back.out(1.7)" }
            );
        }
    }

    animateObjectives(slide) {
        const objectives = slide.querySelectorAll('.objective-item');
        objectives.forEach((obj, index) => {
            setTimeout(() => {
                obj.classList.add('animate');
                if (typeof gsap !== 'undefined') {
                    gsap.from(obj, {
                        y: 30,
                        opacity: 0,
                        duration: 0.6,
                        ease: "power2.out"
                    });
                }
            }, index * 200);
        });
    }

    animateMembraneDiagram(slide) {
        const channels = slide.querySelectorAll('.ion-channel');
        if (typeof gsap !== 'undefined') {
            gsap.from(channels, {
                scale: 0,
                rotation: 360,
                duration: 1,
                stagger: 0.2,
                ease: "back.out(1.7)"
            });
        }
    }

    animateConcentrationTable(slide) {
        const rows = slide.querySelectorAll('.concentration-table tbody tr');
        if (typeof gsap !== 'undefined') {
            gsap.from(rows, {
                x: -50,
                opacity: 0,
                duration: 0.5,
                stagger: 0.1,
                ease: "power2.out"
            });
        }
    }

    animateRMPFactors(slide) {
        const bars = slide.querySelectorAll('.bar');
        if (typeof gsap !== 'undefined') {
            gsap.from(bars, {
                width: 0,
                duration: 1,
                stagger: 0.2,
                ease: "power2.out"
            });
        }
    }

    animateChannelStructure(slide) {
        const channelParts = slide.querySelectorAll('.channel-protein, .voltage-sensor, .inactivation-gate');
        if (typeof gsap !== 'undefined') {
            gsap.from(channelParts, {
                y: -30,
                opacity: 0,
                duration: 0.8,
                stagger: 0.3,
                ease: "bounce.out"
            });
        }
    }

    animateGenericSlide(slide) {
        const elements = slide.querySelectorAll('.content-grid > *, .summary-section, .concept-item');
        if (typeof gsap !== 'undefined') {
            gsap.from(elements, {
                y: 20,
                opacity: 0,
                duration: 0.6,
                stagger: 0.1,
                ease: "power2.out"
            });
        }
    }
}

// Interactive Functions for Specific Slides

// Ion flow animation
function animateIonFlow() {
    const channels = document.querySelectorAll('.ion-channel');
    channels.forEach(channel => {
        if (typeof gsap !== 'undefined') {
            gsap.to(channel, {
                scale: 1.2,
                duration: 0.5,
                yoyo: true,
                repeat: 3,
                ease: "power2.inOut"
            });
        }
    });

    // Create ion particles
    createIonParticles();
}

function createIonParticles() {
    const diagram = document.getElementById('membraneDiagram');
    if (!diagram) return;

    for (let i = 0; i < 10; i++) {
        const particle = document.createElement('div');
        particle.className = 'ion-particle';
        particle.style.cssText = `
            position: absolute;
            width: 8px;
            height: 8px;
            background: #f39c12;
            border-radius: 50%;
            left: ${Math.random() * 300 + 50}px;
            top: ${Math.random() * 200 + 50}px;
            z-index: 10;
        `;
        diagram.appendChild(particle);

        if (typeof gsap !== 'undefined') {
            gsap.to(particle, {
                x: Math.random() * 100 - 50,
                y: Math.random() * 100 - 50,
                duration: 2,
                repeat: -1,
                yoyo: true,
                ease: "sine.inOut"
            });
        }

        // Remove particles after animation
        setTimeout(() => {
            if (particle.parentNode) {
                particle.parentNode.removeChild(particle);
            }
        }, 4000);
    }
}

function showChannelTypes() {
    const channels = document.querySelectorAll('.ion-channel');
    channels.forEach((channel, index) => {
        setTimeout(() => {
            if (typeof gsap !== 'undefined') {
                gsap.to(channel, {
                    scale: 1.3,
                    duration: 0.3,
                    yoyo: true,
                    repeat: 1
                });
            }

            // Show tooltip
            const channelType = channel.dataset.channel;
            showChannelTooltip(channel, channelType);
        }, index * 500);
    });
}

function showChannelTooltip(element, type) {
    const tooltips = {
        sodium: "Voltage-gated sodium channel\nActivation: -55mV\nFast activation/inactivation",
        potassium: "Voltage-gated potassium channel\nActivation: -40mV\nSlow activation, no inactivation",
        calcium: "Voltage-gated calcium channel\nActivation: -30mV\nSlow activation/inactivation"
    };

    const tooltip = document.createElement('div');
    tooltip.className = 'channel-tooltip';
    tooltip.textContent = tooltips[type] || 'Ion channel';
    tooltip.style.cssText = `
        position: absolute;
        background: #2c3e50;
        color: white;
        padding: 0.5rem;
        border-radius: 4px;
        font-size: 0.8rem;
        z-index: 1000;
        white-space: pre-line;
        pointer-events: none;
    `;

    document.body.appendChild(tooltip);

    const rect = element.getBoundingClientRect();
    tooltip.style.left = rect.left + 'px';
    tooltip.style.top = (rect.bottom + 10) + 'px';

    setTimeout(() => {
        if (tooltip.parentNode) {
            tooltip.parentNode.removeChild(tooltip);
        }
    }, 3000);
}

// Nernst equation calculator
function calculateNernst() {
    const ionSelect = document.getElementById('ionSelect');
    const concOut = parseFloat(document.getElementById('concOut').value);
    const concIn = parseFloat(document.getElementById('concIn').value);
    const resultElement = document.getElementById('nernstResult');

    if (!ionSelect || !concOut || !concIn || !resultElement) return;

    const ionData = {
        na: { z: 1, name: 'Na⁺' },
        k: { z: 1, name: 'K⁺' },
        ca: { z: 2, name: 'Ca²⁺' },
        cl: { z: -1, name: 'Cl⁻' }
    };

    const selectedIon = ionData[ionSelect.value];
    if (!selectedIon) return;

    // Nernst equation: E = (61.5/z) * log10([out]/[in])
    const equilibriumPotential = (61.5 / selectedIon.z) * Math.log10(concOut / concIn);

    resultElement.textContent = `${equilibriumPotential.toFixed(1)} mV`;

    // Animate result
    if (typeof gsap !== 'undefined') {
        gsap.from(resultElement.parentElement, {
            scale: 1.1,
            duration: 0.3,
            ease: "back.out(1.7)"
        });
    }
}

// Voltage slider for channel demo
function setupVoltageDemo() {
    const voltageSlider = document.getElementById('voltageSlider');
    const voltageDisplay = document.getElementById('voltageDisplay');
    const naState = document.getElementById('naState');
    const kState = document.getElementById('kState');
    const caState = document.getElementById('caState');

    if (!voltageSlider) return;

    voltageSlider.addEventListener('input', function() {
        const voltage = parseInt(this.value);
        voltageDisplay.textContent = voltage + ' mV';

        // Update channel states based on voltage
        updateChannelState(naState, voltage > -55, 'Na⁺ Channel');
        updateChannelState(kState, voltage > -40, 'K⁺ Channel');
        updateChannelState(caState, voltage > -30, 'Ca²⁺ Channel');
    });
}

function updateChannelState(element, isOpen, channelName) {
    if (!element) return;

    const stateSpan = element.querySelector('span');
    if (isOpen) {
        element.className = 'state-indicator open';
        stateSpan.textContent = 'Open';
    } else {
        element.className = 'state-indicator closed';
        stateSpan.textContent = 'Closed';
    }

    element.firstChild.textContent = channelName + ': ';
}

// Initialize lecture slides when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    // Initialize lecture slides
    const lectureSlides = new LectureSlides();

    // Setup interactive demos
    setupVoltageDemo();

    // Setup ion selector for Nernst calculator
    const ionSelect = document.getElementById('ionSelect');
    if (ionSelect) {
        ionSelect.addEventListener('change', function() {
            const defaultValues = {
                na: { out: 150, in: 15 },
                k: { out: 5, in: 150 },
                ca: { out: 2, in: 0.0001 },
                cl: { out: 110, in: 10 }
            };

            const selected = defaultValues[this.value];
            if (selected) {
                document.getElementById('concOut').value = selected.out;
                document.getElementById('concIn').value = selected.in;
                calculateNernst();
            }
        });
    }

    // Auto-calculate Nernst on input change
    const concInputs = document.querySelectorAll('#concOut, #concIn');
    concInputs.forEach(input => {
        input.addEventListener('input', calculateNernst);
    });

    // Initial calculation
    if (document.getElementById('nernstResult')) {
        calculateNernst();
    }
});

// Handle fullscreen change events
document.addEventListener('fullscreenchange', function() {
    const isFullscreen = !!document.fullscreenElement;
    const btn = document.getElementById('fullscreenBtn');
    if (btn) {
        btn.textContent = isFullscreen ? '🔍 Exit Fullscreen' : '🔍 Fullscreen';
    }
});

// Export for use in other scripts
window.LectureSlides = LectureSlides;
