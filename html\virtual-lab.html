<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Virtual Simulation Laboratory - Medical Instrumentation</title>
    <link rel="stylesheet" href="../css/style.css">
</head>
<body>
    <header>
        <div class="container">
            <h1>Interactive Virtual Simulation Laboratory</h1>
            <p class="subtitle">Medical Instrumentation: Principles and Applications</p>
            <div class="course-info">
                <p class="author">Course Author: <strong>Dr. <PERSON> Esmail</strong></p>
                <p class="institution">SUST - BME 2025</p>
            </div>
        </div>
    </header>

    <nav>
        <div class="container">
            <ul>
                <li><a href="../index.html">Home</a></li>
                <li><a href="#lab-overview">Lab Overview</a></li>
                <li><a href="#virtual-instruments">Virtual Instruments</a></li>
                <li><a href="#experiments">Experiments</a></li>
                <li><a href="#simulations">Simulations</a></li>
                <li><a href="#resources">Resources</a></li>
            </ul>
        </div>
    </nav>

    <main>
        <section id="lab-overview" class="section">
            <div class="container">
                <h2>🔬 Virtual Laboratory: From Fundamentals to Sophisticated Applications</h2>
                <p class="lab-intro">Our immersive virtual laboratory takes students on a comprehensive journey from understanding the fundamental origins of physiological signals to mastering sophisticated capture, processing, and interpretation techniques. Through hands-on virtual projects, students experience the complete signal pathway from cellular generation to clinical application.</p>

                <div class="lab-journey-overview">
                    <h3>🧬 Complete Signal Journey: Hands-On Virtual Experience</h3>
                    <div class="signal-journey">
                        <div class="journey-stage">
                            <h4>1. Signal Origins</h4>
                            <p>Explore cellular mechanisms that generate bioelectric, biomechanical, and biochemical signals</p>
                        </div>
                        <div class="journey-connector">→</div>
                        <div class="journey-stage">
                            <h4>2. Capture Techniques</h4>
                            <p>Master sophisticated transduction methods and advanced sensor technologies</p>
                        </div>
                        <div class="journey-connector">→</div>
                        <div class="journey-stage">
                            <h4>3. Processing & Analysis</h4>
                            <p>Apply cutting-edge algorithms for signal enhancement and clinical interpretation</p>
                        </div>
                        <div class="journey-connector">→</div>
                        <div class="journey-stage">
                            <h4>4. Clinical Application</h4>
                            <p>Implement complete medical instrumentation systems for real-world healthcare</p>
                        </div>
                    </div>
                </div>

                <div class="lab-capabilities">
                    <div class="capability-card">
                        <h3>🔬 Realistic Simulations</h3>
                        <p>High-fidelity simulations of medical instruments including ECG machines, EMG systems, patient monitors, and diagnostic equipment with authentic user interfaces and responses.</p>
                    </div>
                    <div class="capability-card">
                        <h3>📊 Real-time Processing</h3>
                        <p>Process actual biomedical signals with interactive filtering, analysis, and visualization tools. Students can manipulate parameters and observe real-time changes.</p>
                    </div>
                    <div class="capability-card">
                        <h3>🛠️ Circuit Design</h3>
                        <p>Integrated SPICE simulation environment with biomedical-specific component libraries for designing and testing medical device circuits.</p>
                    </div>
                    <div class="capability-card">
                        <h3>📈 Data Analysis</h3>
                        <p>Advanced signal processing and statistical analysis tools with MATLAB and Python integration for comprehensive data analysis.</p>
                    </div>
                </div>
            </div>
        </section>

        <section id="virtual-instruments" class="section">
            <div class="container">
                <h2>Virtual Instrumentation Suite</h2>
                <div class="instruments-grid">
                    <div class="instrument-card">
                        <h3>Digital Oscilloscope</h3>
                        <div class="instrument-image">📟</div>
                        <p>Multi-channel digital oscilloscope with advanced triggering, measurement, and analysis capabilities for biomedical signal visualization.</p>
                        <ul>
                            <li>4-channel input with differential probes</li>
                            <li>Bandwidth: DC to 100 MHz</li>
                            <li>Advanced triggering modes</li>
                            <li>FFT analysis and measurements</li>
                        </ul>
                        <button class="launch-btn">Launch Instrument</button>
                    </div>

                    <div class="instrument-card">
                        <h3>Function Generator</h3>
                        <div class="instrument-image">🌊</div>
                        <p>Arbitrary waveform generator for creating test signals and simulating physiological waveforms.</p>
                        <ul>
                            <li>Standard waveforms (sine, square, triangle)</li>
                            <li>Arbitrary waveform capability</li>
                            <li>Frequency range: 0.1 Hz to 10 MHz</li>
                            <li>Physiological signal templates</li>
                        </ul>
                        <button class="launch-btn">Launch Instrument</button>
                    </div>

                    <div class="instrument-card">
                        <h3>Spectrum Analyzer</h3>
                        <div class="instrument-image">📊</div>
                        <p>Real-time spectrum analyzer for frequency domain analysis of biomedical signals.</p>
                        <ul>
                            <li>Frequency range: 0.01 Hz to 1 MHz</li>
                            <li>Real-time FFT processing</li>
                            <li>Power spectral density analysis</li>
                            <li>Waterfall and spectrogram displays</li>
                        </ul>
                        <button class="launch-btn">Launch Instrument</button>
                    </div>

                    <div class="instrument-card">
                        <h3>Patient Monitor</h3>
                        <div class="instrument-image">💓</div>
                        <p>Multi-parameter patient monitoring system with ECG, blood pressure, and vital signs display.</p>
                        <ul>
                            <li>12-lead ECG monitoring</li>
                            <li>Non-invasive blood pressure</li>
                            <li>Pulse oximetry simulation</li>
                            <li>Alarm and trending systems</li>
                        </ul>
                        <button class="launch-btn">Launch Instrument</button>
                    </div>

                    <div class="instrument-card">
                        <h3>EMG Analysis System</h3>
                        <div class="instrument-image">💪</div>
                        <p>Electromyography analysis system for muscle signal recording and gait analysis.</p>
                        <ul>
                            <li>Multi-channel EMG recording</li>
                            <li>Surface and intramuscular modes</li>
                            <li>Gait cycle analysis</li>
                            <li>Muscle activation patterns</li>
                        </ul>
                        <button class="launch-btn">Launch Instrument</button>
                    </div>

                    <div class="instrument-card">
                        <h3>Circuit Simulator</h3>
                        <div class="instrument-image">🔌</div>
                        <p>SPICE-based circuit simulation environment with biomedical component libraries.</p>
                        <ul>
                            <li>Schematic capture and simulation</li>
                            <li>Biomedical component models</li>
                            <li>AC/DC/Transient analysis</li>
                            <li>Monte Carlo and sensitivity analysis</li>
                        </ul>
                        <button class="launch-btn">Launch Instrument</button>
                    </div>
                </div>
            </div>
        </section>

        <section id="experiments" class="section">
            <div class="container">
                <h2>Virtual Laboratory Experiments</h2>
                <div class="experiments-categories">
                    <div class="category-section">
                        <h3>Cardiovascular Instrumentation</h3>
                        <div class="experiments-list">
                            <div class="experiment-item">
                                <h4>Experiment 1: ECG Signal Acquisition and Analysis</h4>
                                <p>Learn ECG lead placement, signal conditioning, and arrhythmia detection using virtual ECG equipment.</p>
                                <div class="experiment-details">
                                    <span class="duration">Duration: 2 hours</span>
                                    <span class="difficulty">Intermediate</span>
                                </div>
                                <button class="start-experiment">Start Experiment</button>
                            </div>

                            <div class="experiment-item">
                                <h4>Experiment 2: Heart Rate Variability Analysis</h4>
                                <p>Analyze HRV parameters using time-domain and frequency-domain methods.</p>
                                <div class="experiment-details">
                                    <span class="duration">Duration: 1.5 hours</span>
                                    <span class="difficulty">Advanced</span>
                                </div>
                                <button class="start-experiment">Start Experiment</button>
                            </div>
                        </div>
                    </div>

                    <div class="category-section">
                        <h3>Neuromuscular Systems</h3>
                        <div class="experiments-list">
                            <div class="experiment-item">
                                <h4>Experiment 3: EMG Signal Processing</h4>
                                <p>Record and analyze muscle activation patterns during various movements and exercises.</p>
                                <div class="experiment-details">
                                    <span class="duration">Duration: 2.5 hours</span>
                                    <span class="difficulty">Intermediate</span>
                                </div>
                                <button class="start-experiment">Start Experiment</button>
                            </div>

                            <div class="experiment-item">
                                <h4>Experiment 4: Gait Analysis Laboratory</h4>
                                <p>Comprehensive gait analysis using EMG, force plates, and motion capture simulation.</p>
                                <div class="experiment-details">
                                    <span class="duration">Duration: 3 hours</span>
                                    <span class="difficulty">Advanced</span>
                                </div>
                                <button class="start-experiment">Start Experiment</button>
                            </div>
                        </div>
                    </div>

                    <div class="category-section">
                        <h3>Medical Device Design</h3>
                        <div class="experiments-list">
                            <div class="experiment-item">
                                <h4>Experiment 5: Instrumentation Amplifier Design</h4>
                                <p>Design and test differential amplifiers for biomedical signal conditioning.</p>
                                <div class="experiment-details">
                                    <span class="duration">Duration: 2 hours</span>
                                    <span class="difficulty">Intermediate</span>
                                </div>
                                <button class="start-experiment">Start Experiment</button>
                            </div>

                            <div class="experiment-item">
                                <h4>Experiment 6: Digital Filter Implementation</h4>
                                <p>Implement and test digital filters for noise reduction in biomedical signals.</p>
                                <div class="experiment-details">
                                    <span class="duration">Duration: 2.5 hours</span>
                                    <span class="difficulty">Advanced</span>
                                </div>
                                <button class="start-experiment">Start Experiment</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <section id="simulations" class="section">
            <div class="container">
                <h2>Interactive Simulations</h2>
                <div class="simulations-grid">
                    <div class="simulation-card">
                        <h3>Cardiac Electrophysiology</h3>
                        <p>Interactive simulation of cardiac action potentials and ECG generation mechanisms.</p>
                        <button class="launch-simulation">Launch Simulation</button>
                    </div>

                    <div class="simulation-card">
                        <h3>Muscle Contraction Dynamics</h3>
                        <p>Visualize muscle fiber activation and EMG signal generation during contraction.</p>
                        <button class="launch-simulation">Launch Simulation</button>
                    </div>

                    <div class="simulation-card">
                        <h3>Medical Imaging Physics</h3>
                        <p>Explore X-ray, ultrasound, and MRI imaging principles through interactive simulations.</p>
                        <button class="launch-simulation">Launch Simulation</button>
                    </div>

                    <div class="simulation-card">
                        <h3>Signal Processing Workshop</h3>
                        <p>Hands-on signal processing with filtering, feature extraction, and machine learning tools.</p>
                        <button class="launch-simulation">Launch Simulation</button>
                    </div>
                </div>
            </div>
        </section>

        <section id="resources" class="section">
            <div class="container">
                <h2>Laboratory Resources</h2>
                <div class="resources-section">
                    <div class="resource-category">
                        <h3>📚 Documentation</h3>
                        <ul>
                            <li><a href="#">Virtual Lab User Manual</a></li>
                            <li><a href="#">Experiment Procedures</a></li>
                            <li><a href="#">Safety Guidelines</a></li>
                            <li><a href="#">Troubleshooting Guide</a></li>
                        </ul>
                    </div>

                    <div class="resource-category">
                        <h3>💾 Data Sets</h3>
                        <ul>
                            <li><a href="#">Sample ECG Recordings</a></li>
                            <li><a href="#">EMG Signal Database</a></li>
                            <li><a href="#">Medical Image Collections</a></li>
                            <li><a href="#">Physiological Parameters</a></li>
                        </ul>
                    </div>

                    <div class="resource-category">
                        <h3>🔧 Software Tools</h3>
                        <ul>
                            <li><a href="#">MATLAB Signal Processing Toolbox</a></li>
                            <li><a href="#">Python Biomedical Libraries</a></li>
                            <li><a href="#">Circuit Simulation Software</a></li>
                            <li><a href="#">Data Analysis Templates</a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <footer>
        <div class="container">
            <div class="footer-content">
                <div class="author-info">
                    <h3>Course Author</h3>
                    <p><strong>Dr. Mohammed Yagoub Esmail</strong></p>
                    <p>Sudan University of Science and Technology (SUST)</p>
                    <p>Biomedical Engineering Department</p>
                </div>

                <div class="contact-info">
                    <h3>Contact Information</h3>
                    <p>📧 Email: <a href="mailto:<EMAIL>"><EMAIL></a></p>
                    <p>📱 Phone: +249912867327, +966538076790</p>
                </div>
            </div>

            <div class="copyright">
                <p>&copy; 2025 Dr. Mohammed Yagoub Esmail - SUST BME</p>
                <p>All rights reserved. Email: <EMAIL></p>
            </div>
        </div>
    </footer>

    <script src="../js/main.js"></script>
</body>
</html>
