// Lecture 1.1 Interactive Elements - Cellular Foundations

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    console.log('Lecture 1.1 interactive elements loading...');
    
    // Initialize interactive elements
    initializeInteractiveElements();
    
    // Setup Nernst calculator
    setupNernstCalculator();
    
    // Setup voltage demo
    setupVoltageDemo();
    
    console.log('Lecture 1.1 interactive elements loaded successfully');
});

function initializeInteractiveElements() {
    // Add any initialization code here
    console.log('Interactive elements initialized');
}

// Ion flow animation
function animateIonFlow() {
    console.log('Animating ion flow...');
    
    const channels = document.querySelectorAll('.ion-channel');
    channels.forEach((channel, index) => {
        setTimeout(() => {
            if (typeof gsap !== 'undefined') {
                gsap.to(channel, {
                    scale: 1.2,
                    duration: 0.5,
                    yoyo: true,
                    repeat: 3,
                    ease: "power2.inOut"
                });
            } else {
                // Fallback animation without GSAP
                channel.style.transform = 'scale(1.2)';
                setTimeout(() => {
                    channel.style.transform = 'scale(1)';
                }, 500);
            }
        }, index * 200);
    });
    
    // Create ion particles
    createIonParticles();
}

function createIonParticles() {
    const diagram = document.getElementById('membraneDiagram');
    if (!diagram) return;
    
    for (let i = 0; i < 8; i++) {
        const particle = document.createElement('div');
        particle.className = 'ion-particle';
        particle.style.cssText = `
            position: absolute;
            width: 8px;
            height: 8px;
            background: #f39c12;
            border-radius: 50%;
            left: ${Math.random() * 300 + 50}px;
            top: ${Math.random() * 200 + 50}px;
            z-index: 10;
            pointer-events: none;
        `;
        diagram.appendChild(particle);
        
        // Animate particle movement
        if (typeof gsap !== 'undefined') {
            gsap.to(particle, {
                x: Math.random() * 100 - 50,
                y: Math.random() * 100 - 50,
                duration: 2,
                repeat: 2,
                yoyo: true,
                ease: "sine.inOut"
            });
        }
        
        // Remove particles after animation
        setTimeout(() => {
            if (particle.parentNode) {
                particle.parentNode.removeChild(particle);
            }
        }, 6000);
    }
}

function showChannelTypes() {
    console.log('Showing channel types...');
    
    const channels = document.querySelectorAll('.ion-channel');
    const channelInfo = {
        sodium: "Voltage-gated sodium channel\nActivation: -55mV\nFast activation/inactivation",
        potassium: "Voltage-gated potassium channel\nActivation: -40mV\nSlow activation, no inactivation",
        calcium: "Voltage-gated calcium channel\nActivation: -30mV\nSlow activation/inactivation"
    };
    
    channels.forEach((channel, index) => {
        setTimeout(() => {
            if (typeof gsap !== 'undefined') {
                gsap.to(channel, {
                    scale: 1.3,
                    duration: 0.3,
                    yoyo: true,
                    repeat: 1
                });
            }
            
            // Show tooltip
            const channelType = channel.dataset.channel;
            if (channelInfo[channelType]) {
                showTooltip(channel, channelInfo[channelType]);
            }
        }, index * 500);
    });
}

function showTooltip(element, text) {
    const tooltip = document.createElement('div');
    tooltip.className = 'channel-tooltip';
    tooltip.textContent = text;
    tooltip.style.cssText = `
        position: absolute;
        background: #2c3e50;
        color: white;
        padding: 0.8rem;
        border-radius: 6px;
        font-size: 0.9rem;
        z-index: 1000;
        white-space: pre-line;
        pointer-events: none;
        box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        max-width: 200px;
    `;
    
    document.body.appendChild(tooltip);
    
    const rect = element.getBoundingClientRect();
    tooltip.style.left = (rect.left + rect.width/2 - tooltip.offsetWidth/2) + 'px';
    tooltip.style.top = (rect.bottom + 10) + 'px';
    
    // Animate tooltip appearance
    if (typeof gsap !== 'undefined') {
        gsap.from(tooltip, {
            y: -10,
            opacity: 0,
            duration: 0.3
        });
    }
    
    setTimeout(() => {
        if (tooltip.parentNode) {
            if (typeof gsap !== 'undefined') {
                gsap.to(tooltip, {
                    y: -10,
                    opacity: 0,
                    duration: 0.3,
                    onComplete: () => tooltip.remove()
                });
            } else {
                tooltip.remove();
            }
        }
    }, 3000);
}

// Nernst equation calculator
function setupNernstCalculator() {
    const ionSelect = document.getElementById('ionSelect');
    const concOut = document.getElementById('concOut');
    const concIn = document.getElementById('concIn');
    
    if (ionSelect) {
        ionSelect.addEventListener('change', function() {
            const defaultValues = {
                na: { out: 150, in: 15 },
                k: { out: 5, in: 150 },
                ca: { out: 2, in: 0.0001 },
                cl: { out: 110, in: 10 }
            };
            
            const selected = defaultValues[this.value];
            if (selected && concOut && concIn) {
                concOut.value = selected.out;
                concIn.value = selected.in;
                calculateNernst();
            }
        });
    }
    
    // Auto-calculate on input change
    if (concOut) concOut.addEventListener('input', calculateNernst);
    if (concIn) concIn.addEventListener('input', calculateNernst);
    
    // Initial calculation
    calculateNernst();
}

function calculateNernst() {
    const ionSelect = document.getElementById('ionSelect');
    const concOut = document.getElementById('concOut');
    const concIn = document.getElementById('concIn');
    const resultElement = document.getElementById('nernstResult');
    
    if (!ionSelect || !concOut || !concIn || !resultElement) return;
    
    const concOutValue = parseFloat(concOut.value);
    const concInValue = parseFloat(concIn.value);
    
    if (isNaN(concOutValue) || isNaN(concInValue) || concInValue === 0) {
        resultElement.textContent = 'Invalid input';
        return;
    }
    
    const ionData = {
        na: { z: 1, name: 'Na⁺' },
        k: { z: 1, name: 'K⁺' },
        ca: { z: 2, name: 'Ca²⁺' },
        cl: { z: -1, name: 'Cl⁻' }
    };
    
    const selectedIon = ionData[ionSelect.value];
    if (!selectedIon) return;
    
    // Nernst equation: E = (61.5/z) * log10([out]/[in])
    const equilibriumPotential = (61.5 / selectedIon.z) * Math.log10(concOutValue / concInValue);
    
    resultElement.textContent = `${equilibriumPotential.toFixed(1)} mV`;
    
    // Animate result
    if (typeof gsap !== 'undefined') {
        gsap.from(resultElement.parentElement, {
            scale: 1.05,
            duration: 0.3,
            ease: "back.out(1.7)"
        });
    }
}

// Voltage slider for channel demo
function setupVoltageDemo() {
    const voltageSlider = document.getElementById('voltageSlider');
    const voltageDisplay = document.getElementById('voltageDisplay');
    const naState = document.getElementById('naState');
    const kState = document.getElementById('kState');
    const caState = document.getElementById('caState');
    
    if (!voltageSlider) return;
    
    voltageSlider.addEventListener('input', function() {
        const voltage = parseInt(this.value);
        if (voltageDisplay) {
            voltageDisplay.textContent = voltage + ' mV';
        }
        
        // Update channel states based on voltage
        updateChannelState(naState, voltage > -55, 'Na⁺ Channel');
        updateChannelState(kState, voltage > -40, 'K⁺ Channel');
        updateChannelState(caState, voltage > -30, 'Ca²⁺ Channel');
    });
    
    // Initialize with default values
    if (voltageDisplay) {
        voltageDisplay.textContent = voltageSlider.value + ' mV';
    }
    updateChannelState(naState, false, 'Na⁺ Channel');
    updateChannelState(kState, false, 'K⁺ Channel');
    updateChannelState(caState, false, 'Ca²⁺ Channel');
}

function updateChannelState(element, isOpen, channelName) {
    if (!element) return;
    
    const stateSpan = element.querySelector('span');
    if (!stateSpan) return;
    
    if (isOpen) {
        element.className = 'state-indicator open';
        stateSpan.textContent = 'Open';
        element.style.backgroundColor = '#d5f4e6';
        element.style.borderLeftColor = '#27ae60';
    } else {
        element.className = 'state-indicator closed';
        stateSpan.textContent = 'Closed';
        element.style.backgroundColor = '#fadbd8';
        element.style.borderLeftColor = '#e74c3c';
    }
    
    // Update the channel name
    const textNode = element.firstChild;
    if (textNode && textNode.nodeType === Node.TEXT_NODE) {
        textNode.textContent = channelName + ': ';
    }
}

// Add CSS for interactive elements
const style = document.createElement('style');
style.textContent = `
    .channel-tooltip {
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        line-height: 1.4;
    }
    
    .ion-particle {
        animation: ionFloat 2s ease-in-out infinite;
    }
    
    @keyframes ionFloat {
        0%, 100% { transform: translateY(0px); }
        50% { transform: translateY(-10px); }
    }
    
    .state-indicator {
        background: #f8f9fa;
        padding: 1rem;
        border-radius: 6px;
        text-align: center;
        border-left: 4px solid #bdc3c7;
        transition: all 0.3s ease;
        margin: 0.5rem 0;
    }
    
    .state-indicator.open {
        background: #d5f4e6;
        border-left-color: #27ae60;
        color: #27ae60;
        font-weight: bold;
    }
    
    .state-indicator.closed {
        background: #fadbd8;
        border-left-color: #e74c3c;
        color: #e74c3c;
        font-weight: bold;
    }
    
    .demo-btn {
        background: #27ae60;
        color: white;
        border: none;
        padding: 0.8rem 1.5rem;
        border-radius: 6px;
        cursor: pointer;
        margin: 0.5rem;
        transition: all 0.3s ease;
        font-weight: bold;
        font-size: 0.9rem;
    }
    
    .demo-btn:hover {
        background: #229954;
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(39, 174, 96, 0.3);
    }
    
    .interactive-calculator {
        background: white;
        padding: 2rem;
        border-radius: 8px;
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        margin-top: 2rem;
    }
    
    .calculator-controls {
        display: flex;
        flex-wrap: wrap;
        gap: 1rem;
        align-items: center;
        justify-content: center;
    }
    
    .calculator-controls label {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 0.5rem;
        font-weight: bold;
        color: #2c3e50;
    }
    
    .calculator-controls input, .calculator-controls select {
        padding: 0.5rem;
        border: 2px solid #bdc3c7;
        border-radius: 4px;
        font-size: 1rem;
    }
    
    .calculator-controls button {
        background: #3498db;
        color: white;
        border: none;
        padding: 0.8rem 1.5rem;
        border-radius: 6px;
        cursor: pointer;
        font-weight: bold;
        transition: all 0.3s ease;
    }
    
    .calculator-controls button:hover {
        background: #2980b9;
        transform: translateY(-2px);
    }
    
    .result {
        background: #d5f4e6;
        color: #27ae60;
        padding: 1rem;
        border-radius: 6px;
        margin-top: 1rem;
        font-weight: bold;
        font-size: 1.1rem;
        text-align: center;
    }
    
    .demo-controls {
        background: white;
        padding: 2rem;
        border-radius: 8px;
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        margin-top: 1rem;
    }
    
    .demo-controls label {
        display: block;
        margin-bottom: 1rem;
        font-weight: bold;
        color: #2c3e50;
    }
    
    .demo-controls input[type="range"] {
        width: 100%;
        margin: 1rem 0;
    }
    
    .channel-states {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1rem;
        margin-top: 1rem;
    }
`;
document.head.appendChild(style);
