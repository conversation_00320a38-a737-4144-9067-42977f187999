<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Medical Instrumentation: Principles and Applications - LMS Platform</title>
    <meta name="description" content="Interactive LMS platform for Medical Instrumentation: Principles and Applications, covering signal origins, capture techniques, virtual labs, and more. Authored by Dr. <PERSON>, SUST.">
    <link rel="stylesheet" href="css/style.css">
</head>
<body>
    <header class="hero-header">
        <div class="container">
            <div class="hero-content">
                <h1>🧬 Medical Instrumentation: Principles and Applications</h1>
                <p class="subtitle">⚡ Interactive LMS Platform with Virtual Simulation Lab Facilities</p>
                <p class="hero-description">From Signal Origins to Sophisticated Capture Techniques</p>

                <div class="hero-stats">
                    <div class="stat-item">
                        <span class="stat-number">24</span>
                        <span class="stat-label">Interactive Lectures</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number">6</span>
                        <span class="stat-label">Course Modules</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number">120+</span>
                        <span class="stat-label">Animated Slides</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number">50+</span>
                        <span class="stat-label">Circuit Diagrams</span>
                    </div>
                </div>

                <div class="course-info">
                    <div class="author-card">
                        <div class="author-avatar">👨‍🔬</div>
                        <div class="author-details">
                            <p class="author"><strong>Course Author:</strong> Dr. Mohammed Yagoub Esmail</p>
                            <p class="institution"><strong>Institution:</strong> Sudan University of Science and Technology (SUST)</p>
                            <p class="department"><strong>Department:</strong> Biomedical Engineering - 2025</p>
                            <p class="contact"><strong>Contact:</strong> 📧 <EMAIL></p>
                            <p class="phone"><strong>Phone:</strong> 📱 +249912867327, +966538076790</p>
                        </div>
                    </div>
                </div>

                <div class="hero-buttons">
                    <a href="#lectures" class="btn btn-primary">📖 Start Learning</a>
                    <a href="#modules" class="btn btn-secondary">📚 Explore Modules</a>
                    <a href="#virtual-lab" class="btn btn-accent">🔬 Virtual Lab</a>
                </div>
            </div>

            <div class="hero-visual">
                <div class="signal-animation">
                    <canvas id="heroCanvas" width="400" height="300"></canvas>
                    <div class="animation-controls">
                        <button id="playAnimation" class="animation-btn">▶️ Play</button>
                        <button id="pauseAnimation" class="animation-btn">⏸️ Pause</button>
                        <button id="resetAnimation" class="animation-btn">🔄 Reset</button>
                    </div>
                </div>
                <div class="hero-features">
                    <div class="feature-highlight">
                        <span class="feature-icon">🎬</span>
                        <span class="feature-text">Animated Tools</span>
                    </div>
                    <div class="feature-highlight">
                        <span class="feature-icon">📊</span>
                        <span class="feature-text">Interactive Diagrams</span>
                    </div>
                    <div class="feature-highlight">
                        <span class="feature-icon">⚡</span>
                        <span class="feature-text">Real-time Simulations</span>
                    </div>
                    <div class="feature-highlight">
                        <span class="feature-icon">🔬</span>
                        <span class="feature-text">Virtual Lab</span>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <nav>
        <div class="container">
            <ul>
                <li><a href="#overview">Course Overview</a></li>
                <li><a href="#modules">LMS Modules</a></li>
                <li><a href="#lectures">Slide Lectures</a></li>
                <li><a href="#virtual-lab">Virtual Lab</a></li>
                <li><a href="#projects">Projects</a></li>
                <li><a href="#resources">Resources</a></li>
                <li><a href="#contact">Contact</a></li>
            </ul>
        </div>
    </nav>

    <main>
        <section id="overview" class="section">
            <div class="container">
                <h2>Course Overview</h2>
                <p>This comprehensive course takes students on a fascinating journey from the <strong>fundamental origins of physiological signals</strong> to the <strong>sophisticated techniques</strong> used to capture, process, and interpret them. Through our interactive LMS platform, students explore how electrical, mechanical, and chemical activities in the human body generate measurable signals, and learn cutting-edge methods to acquire, analyze, and extract meaningful clinical information from these signals.</p>

                <div class="course-journey">
                    <h3>🧬 From Fundamentals to Sophistication</h3>
                    <div class="journey-path">
                        <div class="journey-step">
                            <h4>1. Physiological Signal Origins</h4>
                            <p>Understand how cellular activities generate bioelectric, biomechanical, and biochemical signals</p>
                        </div>
                        <div class="journey-arrow">→</div>
                        <div class="journey-step">
                            <h4>2. Signal Capture Techniques</h4>
                            <p>Master advanced transduction methods and sensor technologies for signal acquisition</p>
                        </div>
                        <div class="journey-arrow">→</div>
                        <div class="journey-step">
                            <h4>3. Processing & Interpretation</h4>
                            <p>Apply sophisticated algorithms for signal enhancement, feature extraction, and clinical analysis</p>
                        </div>
                    </div>
                </div>

                <div class="course-objectives">
                    <h3>🎯 Learning Objectives</h3>
                    <ul>
                        <li>Trace the biological origins of physiological signals from cellular to organ system levels</li>
                        <li>Master sophisticated signal capture techniques using modern transduction principles</li>
                        <li>Apply advanced signal processing algorithms for noise reduction and feature extraction</li>
                        <li>Interpret complex biomedical signals for clinical decision-making</li>
                        <li>Design and implement complete medical instrumentation systems</li>
                        <li>Experience hands-on learning through immersive virtual laboratory projects</li>
                    </ul>
                </div>

                <div class="hands-on-emphasis">
                    <h3>🔬 Hands-On Virtual Laboratory Experience</h3>
                    <p>Our state-of-the-art virtual laboratory brings theoretical concepts to life through realistic simulations and interactive projects. Students work with virtual instruments that mirror real medical equipment, processing actual physiological data, and designing complete instrumentation systems from concept to implementation.</p>
                </div>

                <div class="features">
                    <div class="feature-card">
                        <h3>🔬 Virtual Simulation Lab</h3>
                        <p>Interactive virtual laboratory with realistic medical instrumentation simulations and real-time data analysis capabilities.</p>
                        <a href="html/virtual-lab.html" class="btn">Explore Virtual Lab</a>
                    </div>
                    <div class="feature-card">
                        <h3>📚 Complete Course Modules</h3>
                        <p>6 comprehensive modules with interactive content, animations, diagrams, and hands-on virtual laboratory experiences.</p>
                        <a href="modules/modules-overview.html" class="btn">View All Modules</a>
                    </div>
                    <div class="feature-card">
                        <h3>🛠️ Practical Applications</h3>
                        <p>Real-world case studies, Arduino-based projects, and industry-standard signal processing techniques.</p>
                        <a href="html/projects.html" class="btn">View Projects</a>
                    </div>
                </div>
            </div>
        </section>

        <section id="modules" class="section">
            <div class="container">
                <h2>📚 Progressive Learning Modules: From Fundamentals to Sophistication</h2>
                <p class="modules-intro">Our carefully structured curriculum takes students on a comprehensive journey from understanding the fundamental origins of physiological signals to mastering sophisticated capture, processing, and interpretation techniques. Each module builds upon previous knowledge while introducing increasingly advanced concepts and hands-on virtual laboratory experiences.</p>

                <div class="learning-pathway">
                    <h3>🎯 Learning Pathway Overview</h3>
                    <div class="pathway-flow">
                        <div class="pathway-stage">
                            <span class="stage-number">Stage 1</span>
                            <h4>Fundamental Origins</h4>
                            <p>Cellular & physiological signal generation</p>
                        </div>
                        <div class="pathway-arrow">→</div>
                        <div class="pathway-stage">
                            <span class="stage-number">Stage 2</span>
                            <h4>Capture Techniques</h4>
                            <p>Advanced transduction & acquisition methods</p>
                        </div>
                        <div class="pathway-arrow">→</div>
                        <div class="pathway-stage">
                            <span class="stage-number">Stage 3</span>
                            <h4>Sophisticated Processing</h4>
                            <p>AI-powered analysis & clinical interpretation</p>
                        </div>
                    </div>
                </div>

                <div class="modules-grid">
                    <div class="module-card">
                        <div class="module-header">
                            <span class="module-number">Module 1</span>
                            <h3>Fundamentals of Medical Instrumentation</h3>
                        </div>
                        <div class="module-content">
                            <ul>
                                <li>🧬 Cellular Origins of Bioelectric Signals</li>
                                <li>⚡ Ion Channels and Action Potential Generation</li>
                                <li>🔬 From Single Cell to Organ System Signals</li>
                                <li>🛡️ Safety Standards and Patient Protection</li>
                                <li>📋 Medical Device Classification and Regulations</li>
                            </ul>
                            <div class="module-duration">Duration: 2 weeks | Focus: Signal Origins</div>
                            <div class="module-actions">
                                <a href="modules/module1/module1-description.html" class="btn">📖 Enter Module</a>
                                <a href="html/lms-modules.html#module1" class="btn btn-secondary">View Details</a>
                            </div>
                        </div>
                    </div>

                    <div class="module-card">
                        <div class="module-header">
                            <span class="module-number">Module 2</span>
                            <h3>Biomedical Signal Acquisition</h3>
                        </div>
                        <div class="module-content">
                            <ul>
                                <li>🎯 Advanced Transduction Principles</li>
                                <li>🔬 Sophisticated Sensor Technologies</li>
                                <li>⚡ Precision Signal Conditioning Circuits</li>
                                <li>💻 High-Resolution ADC Systems</li>
                                <li>🛠️ Hands-On: Virtual Electrode Design Lab</li>
                            </ul>
                            <div class="module-duration">Duration: 3 weeks | Focus: Capture Techniques</div>
                            <div class="module-actions">
                                <a href="modules/module2/module2-description.html" class="btn">📖 Enter Module</a>
                                <a href="html/lms-modules.html#module2" class="btn btn-secondary">View Details</a>
                            </div>
                        </div>
                    </div>

                    <div class="module-card">
                        <div class="module-header">
                            <span class="module-number">Module 3</span>
                            <h3>ECG and Cardiovascular Monitoring</h3>
                        </div>
                        <div class="module-content">
                            <ul>
                                <li>💓 From Cardiac Cell to ECG: Complete Signal Path</li>
                                <li>🔬 Advanced Multi-Lead Acquisition Systems</li>
                                <li>🧠 AI-Powered Arrhythmia Detection</li>
                                <li>📊 Sophisticated Signal Processing Algorithms</li>
                                <li>🛠️ Hands-On: Virtual ECG Machine Project</li>
                            </ul>
                            <div class="module-duration">Duration: 3 weeks | Focus: Cardiac Systems</div>
                        </div>
                    </div>

                    <div class="module-card">
                        <div class="module-header">
                            <span class="module-number">Module 4</span>
                            <h3>EMG and Neuromuscular Systems</h3>
                        </div>
                        <div class="module-content">
                            <ul>
                                <li>Muscle Physiology and EMG Generation</li>
                                <li>Surface vs. Intramuscular EMG</li>
                                <li>EMG Signal Processing Techniques</li>
                                <li>Gait Analysis and Movement Assessment</li>
                            </ul>
                            <div class="module-duration">Duration: 3 weeks</div>
                        </div>
                    </div>

                    <div class="module-card">
                        <div class="module-header">
                            <span class="module-number">Module 5</span>
                            <h3>EEG and Neurological Monitoring</h3>
                        </div>
                        <div class="module-content">
                            <ul>
                                <li>Brain Electrophysiology</li>
                                <li>EEG Electrode Systems</li>
                                <li>Frequency Domain Analysis</li>
                                <li>Brain-Computer Interface Applications</li>
                            </ul>
                            <div class="module-duration">Duration: 2 weeks</div>
                        </div>
                    </div>

                    <div class="module-card">
                        <div class="module-header">
                            <span class="module-number">Module 6</span>
                            <h3>Medical Imaging Instrumentation</h3>
                        </div>
                        <div class="module-content">
                            <ul>
                                <li>X-ray and CT Imaging Systems</li>
                                <li>Ultrasound Principles and Applications</li>
                                <li>MRI and Nuclear Medicine</li>
                                <li>Image Processing and Enhancement</li>
                            </ul>
                            <div class="module-duration">Duration: 3 weeks</div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <section id="lectures" class="section">
            <div class="container">
                <h2>📖 Interactive Slide Lecture Notes</h2>
                <p class="lectures-intro">Comprehensive slide presentations with animated tools, interactive diagrams, and visual demonstrations for each module. Each lecture includes detailed explanations, circuit diagrams, and hands-on examples.</p>

                <div class="lectures-overview">
                    <div class="lecture-stats">
                        <div class="stat-card">
                            <h3>24</h3>
                            <p>Total Lectures</p>
                        </div>
                        <div class="stat-card">
                            <h3>6</h3>
                            <p>Module Series</p>
                        </div>
                        <div class="stat-card">
                            <h3>120+</h3>
                            <p>Interactive Slides</p>
                        </div>
                        <div class="stat-card">
                            <h3>50+</h3>
                            <p>Circuit Diagrams</p>
                        </div>
                    </div>
                </div>

                <div class="lecture-modules-grid">
                    <!-- Module 1 Lectures -->
                    <div class="lecture-module-card">
                        <div class="lecture-module-header">
                            <span class="module-badge">Module 1</span>
                            <h3>Fundamentals of Medical Instrumentation</h3>
                            <p class="module-subtitle">Signal Origins & Cellular Mechanisms</p>
                        </div>
                        <div class="lecture-list">
                            <div class="lecture-item">
                                <div class="lecture-icon">🧬</div>
                                <div class="lecture-info">
                                    <h4>Lecture 1.1: Cellular Foundations</h4>
                                    <p>Ion channels, membrane potentials, and bioelectric signal generation</p>
                                    <div class="lecture-features">
                                        <span class="feature-tag">🎬 Animations</span>
                                        <span class="feature-tag">📊 Diagrams</span>
                                        <span class="feature-tag">🔬 Simulations</span>
                                    </div>
                                </div>
                                <a href="lectures/module1/lecture1-1.html" class="lecture-btn">📖 View Slides</a>
                            </div>

                            <div class="lecture-item">
                                <div class="lecture-icon">⚡</div>
                                <div class="lecture-info">
                                    <h4>Lecture 1.2: Action Potentials</h4>
                                    <p>Signal generation, propagation, and conduction mechanisms</p>
                                    <div class="lecture-features">
                                        <span class="feature-tag">📈 Graphs</span>
                                        <span class="feature-tag">⚡ Interactive</span>
                                        <span class="feature-tag">🎮 Tools</span>
                                    </div>
                                </div>
                                <a href="lectures/module1/lecture1-2.html" class="lecture-btn">📖 View Slides</a>
                            </div>

                            <div class="lecture-item">
                                <div class="lecture-icon">🔬</div>
                                <div class="lecture-info">
                                    <h4>Lecture 1.3: Organ System Signals</h4>
                                    <p>From cellular signals to measurable organ-level electrical activity</p>
                                    <div class="lecture-features">
                                        <span class="feature-tag">💓 ECG</span>
                                        <span class="feature-tag">🧠 EEG</span>
                                        <span class="feature-tag">💪 EMG</span>
                                    </div>
                                </div>
                                <a href="lectures/module1/lecture1-3.html" class="lecture-btn">📖 View Slides</a>
                            </div>

                            <div class="lecture-item">
                                <div class="lecture-icon">🛡️</div>
                                <div class="lecture-info">
                                    <h4>Lecture 1.4: Safety & Standards</h4>
                                    <p>Medical device classification, regulations, and patient safety</p>
                                    <div class="lecture-features">
                                        <span class="feature-tag">📋 Standards</span>
                                        <span class="feature-tag">🛡️ Safety</span>
                                        <span class="feature-tag">⚖️ Regulations</span>
                                    </div>
                                </div>
                                <a href="lectures/module1/lecture1-4.html" class="lecture-btn">📖 View Slides</a>
                            </div>
                        </div>
                    </div>

                    <!-- Module 2 Lectures -->
                    <div class="lecture-module-card">
                        <div class="lecture-module-header">
                            <span class="module-badge">Module 2</span>
                            <h3>Biomedical Signal Acquisition</h3>
                            <p class="module-subtitle">Advanced Capture Techniques</p>
                        </div>
                        <div class="lecture-list">
                            <div class="lecture-item">
                                <div class="lecture-icon">🔄</div>
                                <div class="lecture-info">
                                    <h4>Lecture 2.1: Transduction Principles</h4>
                                    <p>Energy conversion, transducer characteristics, and specifications</p>
                                    <div class="lecture-features">
                                        <span class="feature-tag">🔄 Conversion</span>
                                        <span class="feature-tag">📏 Specs</span>
                                        <span class="feature-tag">🎯 Accuracy</span>
                                    </div>
                                </div>
                                <a href="lectures/module2/lecture2-1.html" class="lecture-btn">📖 View Slides</a>
                            </div>

                            <div class="lecture-item">
                                <div class="lecture-icon">🔘</div>
                                <div class="lecture-info">
                                    <h4>Lecture 2.2: Electrode Systems</h4>
                                    <p>Surface, invasive, and microelectrodes with interface analysis</p>
                                    <div class="lecture-features">
                                        <span class="feature-tag">🔘 Surface</span>
                                        <span class="feature-tag">📍 Invasive</span>
                                        <span class="feature-tag">🔬 Micro</span>
                                    </div>
                                </div>
                                <a href="lectures/module2/lecture2-2.html" class="lecture-btn">📖 View Slides</a>
                            </div>

                            <div class="lecture-item">
                                <div class="lecture-icon">📈</div>
                                <div class="lecture-info">
                                    <h4>Lecture 2.3: Signal Conditioning</h4>
                                    <p>Amplification, filtering, isolation, and noise reduction</p>
                                    <div class="lecture-features">
                                        <span class="feature-tag">📈 Amplify</span>
                                        <span class="feature-tag">🔧 Filter</span>
                                        <span class="feature-tag">🛡️ Isolate</span>
                                    </div>
                                </div>
                                <a href="lectures/module2/lecture2-3.html" class="lecture-btn">📖 View Slides</a>
                            </div>

                            <div class="lecture-item">
                                <div class="lecture-icon">💻</div>
                                <div class="lecture-info">
                                    <h4>Lecture 2.4: ADC Systems</h4>
                                    <p>Sampling theory, quantization, and digital conversion</p>
                                    <div class="lecture-features">
                                        <span class="feature-tag">📊 Sampling</span>
                                        <span class="feature-tag">🔢 Quantize</span>
                                        <span class="feature-tag">💻 Digital</span>
                                    </div>
                                </div>
                                <a href="lectures/module2/lecture2-4.html" class="lecture-btn">📖 View Slides</a>
                            </div>
                        </div>
                    </div>

                    <!-- Module 3 Lectures -->
                    <div class="lecture-module-card">
                        <div class="lecture-module-header">
                            <span class="module-badge">Module 3</span>
                            <h3>ECG & Cardiovascular Monitoring</h3>
                            <p class="module-subtitle">Cardiac Signal Processing</p>
                        </div>
                        <div class="lecture-list">
                            <div class="lecture-item">
                                <div class="lecture-icon">💓</div>
                                <div class="lecture-info">
                                    <h4>Lecture 3.1: Cardiac Electrophysiology</h4>
                                    <p>Heart conduction system and ECG signal generation</p>
                                    <div class="lecture-features">
                                        <span class="feature-tag">💓 Heart</span>
                                        <span class="feature-tag">⚡ Conduction</span>
                                        <span class="feature-tag">📊 ECG</span>
                                    </div>
                                </div>
                                <a href="lectures/module3/lecture3-1.html" class="lecture-btn">📖 View Slides</a>
                            </div>

                            <div class="lecture-item">
                                <div class="lecture-icon">📡</div>
                                <div class="lecture-info">
                                    <h4>Lecture 3.2: ECG Lead Systems</h4>
                                    <p>12-lead placement, vector analysis, and signal acquisition</p>
                                    <div class="lecture-features">
                                        <span class="feature-tag">📡 Leads</span>
                                        <span class="feature-tag">🎯 Vectors</span>
                                        <span class="feature-tag">📊 Analysis</span>
                                    </div>
                                </div>
                                <a href="lectures/module3/lecture3-2.html" class="lecture-btn">📖 Coming Soon</a>
                            </div>
                        </div>
                    </div>

                    <!-- Additional modules preview -->
                    <div class="lecture-modules-preview">
                        <h3>📚 Additional Lecture Series</h3>
                        <div class="preview-grid">
                            <div class="preview-item">
                                <span class="preview-badge">Module 4</span>
                                <h4>EMG & Neuromuscular Systems</h4>
                                <p>4 comprehensive lectures on muscle signal processing</p>
                            </div>
                            <div class="preview-item">
                                <span class="preview-badge">Module 5</span>
                                <h4>EEG & Neurological Monitoring</h4>
                                <p>4 detailed lectures on brain signal analysis</p>
                            </div>
                            <div class="preview-item">
                                <span class="preview-badge">Module 6</span>
                                <h4>Medical Imaging Instrumentation</h4>
                                <p>6 advanced lectures on imaging systems</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <section id="virtual-lab" class="section">
            <div class="container">
                <h2>🔬 Hands-On Virtual Laboratory: Bringing Concepts to Life</h2>
                <p class="lab-intro">Our immersive virtual laboratory transforms abstract physiological concepts into tangible, interactive experiences. Students progress from understanding signal origins at the cellular level to implementing sophisticated capture and processing systems through realistic, hands-on virtual projects.</p>

                <div class="lab-progression">
                    <h3>📈 Progressive Learning Through Virtual Hands-On Projects</h3>
                    <div class="progression-timeline">
                        <div class="timeline-item">
                            <div class="timeline-marker">1</div>
                            <div class="timeline-content">
                                <h4>Signal Origin Exploration</h4>
                                <p>Interactive cellular and tissue-level simulations showing how physiological processes generate measurable signals</p>
                            </div>
                        </div>
                        <div class="timeline-item">
                            <div class="timeline-marker">2</div>
                            <div class="timeline-content">
                                <h4>Capture Technique Mastery</h4>
                                <p>Hands-on virtual projects with electrode placement, sensor calibration, and signal conditioning circuits</p>
                            </div>
                        </div>
                        <div class="timeline-item">
                            <div class="timeline-marker">3</div>
                            <div class="timeline-content">
                                <h4>Processing Implementation</h4>
                                <p>Real-time signal processing projects using sophisticated algorithms for filtering, analysis, and interpretation</p>
                            </div>
                        </div>
                        <div class="timeline-item">
                            <div class="timeline-marker">4</div>
                            <div class="timeline-content">
                                <h4>System Integration</h4>
                                <p>Complete medical instrumentation system design from physiological understanding to clinical application</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="lab-features">
                    <div class="lab-feature">
                        <h3>🧬 Physiological Signal Genesis</h3>
                        <p>Interactive simulations of ion channels, action potentials, and cellular mechanisms that generate bioelectric signals. Students manipulate parameters and observe real-time signal generation.</p>
                    </div>
                    <div class="lab-feature">
                        <h3>🔬 Advanced Capture Techniques</h3>
                        <p>Virtual instrumentation for sophisticated signal acquisition including differential amplification, active filtering, and multi-channel synchronization with realistic noise and artifact simulation.</p>
                    </div>
                    <div class="lab-feature">
                        <h3>🧠 Intelligent Processing Systems</h3>
                        <p>Hands-on implementation of machine learning algorithms, adaptive filtering, and AI-based signal interpretation for clinical decision support.</p>
                    </div>
                </div>

                <div class="lab-experiments">
                    <h3>🛠️ Hands-On Virtual Laboratory Projects</h3>
                    <div class="experiments-grid">
                        <div class="experiment-card">
                            <h4>Project 1: From Heart Cell to ECG</h4>
                            <p>Trace cardiac signals from cellular action potentials through tissue propagation to 12-lead ECG acquisition and arrhythmia detection.</p>
                            <span class="lab-badge">Fundamental</span>
                        </div>
                        <div class="experiment-card">
                            <h4>Project 2: Muscle Mechanics to EMG</h4>
                            <p>Explore muscle fiber activation, motor unit recruitment, and surface EMG capture for gait analysis and prosthetic control.</p>
                            <span class="lab-badge">Biomechanical</span>
                        </div>
                        <div class="experiment-card">
                            <h4>Project 3: Neural Networks to EEG</h4>
                            <p>Understand brain signal generation, electrode montages, and sophisticated processing for brain-computer interfaces.</p>
                            <span class="lab-badge">Neurological</span>
                        </div>
                        <div class="experiment-card">
                            <h4>Project 4: Complete Monitoring System</h4>
                            <p>Design and implement a multi-parameter patient monitoring system with real-time processing and intelligent alarms.</p>
                            <span class="lab-badge">Integration</span>
                        </div>
                        <div class="experiment-card">
                            <h4>Project 5: AI-Powered Diagnostics</h4>
                            <p>Develop machine learning algorithms for automated signal interpretation and clinical decision support systems.</p>
                            <span class="lab-badge">Advanced</span>
                        </div>
                        <div class="experiment-card">
                            <h4>Project 6: Wearable Device Design</h4>
                            <p>Create next-generation wearable medical devices with wireless connectivity and cloud-based analytics.</p>
                            <span class="lab-badge">Innovation</span>
                        </div>
                    </div>
                </div>

                <div class="hands-on-highlight">
                    <h3>💡 Why Hands-On Virtual Learning?</h3>
                    <div class="highlight-grid">
                        <div class="highlight-item">
                            <h4>🎯 Immediate Application</h4>
                            <p>Apply theoretical knowledge instantly through interactive simulations and real-time feedback</p>
                        </div>
                        <div class="highlight-item">
                            <h4>🔄 Iterative Learning</h4>
                            <p>Experiment with parameters, observe outcomes, and refine understanding through trial and discovery</p>
                        </div>
                        <div class="highlight-item">
                            <h4>🌐 Unlimited Access</h4>
                            <p>Practice complex procedures and rare scenarios without equipment limitations or safety concerns</p>
                        </div>
                        <div class="highlight-item">
                            <h4>📊 Data-Driven Insights</h4>
                            <p>Work with real physiological data and observe how processing techniques reveal clinical information</p>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <section id="projects" class="section">
            <div class="container">
                <h2>Course Projects</h2>
                <div class="projects-grid">
                    <div class="project-card">
                        <h3>ECG Analysis</h3>
                        <p>Electrocardiogram signal processing and heart rate analysis using MATLAB.</p>
                        <div class="project-links">
                            <a href="ECG/" class="btn">View Project</a>
                            <a href="ECG/ECG_final.pdf" class="btn btn-secondary">Documentation</a>
                        </div>
                        <div class="project-files">
                            <span class="file-tag">ECG_1.m</span>
                            <span class="file-tag">ECG_2.m</span>
                            <span class="file-tag">find_avg_hr.m</span>
                        </div>
                    </div>

                    <div class="project-card">
                        <h3>Gait Analysis</h3>
                        <p>EMG signal analysis for gait pattern recognition and biomechanical assessment.</p>
                        <div class="project-links">
                            <a href="Gait_Analysis/" class="btn">View Project</a>
                            <a href="Gait_Analysis/gait_lab.pdf" class="btn btn-secondary">Lab Guide</a>
                        </div>
                        <div class="project-files">
                            <span class="file-tag">EMG_analysis.m</span>
                            <span class="file-tag">gait.m</span>
                            <span class="file-tag">iomega.m</span>
                        </div>
                    </div>

                    <div class="project-card">
                        <h3>Research Project</h3>
                        <p>Arduino-based heart rate monitoring system with real-time data processing.</p>
                        <div class="project-links">
                            <a href="research_project/" class="btn">View Project</a>
                        </div>
                        <div class="project-files">
                            <span class="file-tag">research_proj.ino</span>
                            <span class="file-tag">filtering.m</span>
                            <span class="file-tag">plot_heart_rate.py</span>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <section id="resources" class="section">
            <div class="container">
                <h2>Resources & Tools</h2>
                <div class="resources-grid">
                    <div class="resource-item">
                        <h4>Software Tools</h4>
                        <ul>
                            <li>MATLAB - Signal processing and analysis</li>
                            <li>Python - Data visualization and machine learning</li>
                            <li>Arduino IDE - Hardware programming</li>
                            <li>LTSpice - Circuit simulation</li>
                        </ul>
                    </div>
                    <div class="resource-item">
                        <h4>Hardware Components</h4>
                        <ul>
                            <li>Arduino microcontrollers</li>
                            <li>ECG sensors and electrodes</li>
                            <li>EMG acquisition systems</li>
                            <li>Digital storage oscilloscopes</li>
                        </ul>
                    </div>
                    <div class="resource-item">
                        <h4>Documentation</h4>
                        <ul>
                            <li><a href="README.md">Project README</a></li>
                            <li><a href="ECG/ECG_final.pdf">ECG Analysis Report</a></li>
                            <li><a href="Gait_Analysis/gait_lab.pdf">Gait Analysis Lab</a></li>
                            <li><a href="Gait_Analysis/extra sources/">Additional Resources</a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <footer id="contact">
        <div class="container">
            <div class="footer-content">
                <div class="author-info">
                    <h3>Course Author</h3>
                    <p><strong>Dr. Mohammed Yagoub Esmail</strong></p>
                    <p>Sudan University of Science and Technology (SUST)</p>
                    <p>Biomedical Engineering Department</p>
                    <p>Academic Year: 2025</p>
                </div>

                <div class="contact-info">
                    <h3>Contact Information</h3>
                    <p>📧 Email: <a href="mailto:<EMAIL>"><EMAIL></a></p>
                    <p>📱 Phone (Sudan): <a href="tel:+249912867327">+249 912 867 327</a></p>
                    <p>📱 Phone (KSA): <a href="tel:+966538076790">+966 538 076 790</a></p>
                </div>

                <div class="course-info-footer">
                    <h3>Course Information</h3>
                    <p>Medical Instrumentation: Principles and Applications</p>
                    <p>Interactive LMS Platform with Virtual Simulation Lab</p>
                    <p>Comprehensive Biomedical Engineering Education</p>
                </div>
            </div>

            <div class="copyright">
                <p>&copy; 2025 Dr. Mohammed Yagoub Esmail - SUST BME</p>
                <p>All rights reserved. This course content is protected by copyright law.</p>
                <p>Email: <EMAIL> | Phone: +249912867327, +966538076790</p>
            </div>
        </div>
    </footer>

    <a href="#" id="backToTopBtn" class="back-to-top-btn" title="Go to top">↑</a>

    <script src="js/main.js"></script>
</body>
</html>
