<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Module 2: Biomedical Signal Acquisition</title>
    <link rel="stylesheet" href="../../css/style.css">
    <link rel="stylesheet" href="module2-styles.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/3.9.1/chart.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/gsap.min.js"></script>
</head>
<body>
    <header>
        <div class="container">
            <h1>Module 2: Biomedical Signal Acquisition</h1>
            <p class="subtitle">Sophisticated Capture Techniques & Transduction</p>
            <div class="course-info">
                <p class="author">Course Author: <strong>Dr. <PERSON></strong></p>
                <p class="institution">SUST - BME 2025</p>
            </div>
        </div>
    </header>

    <nav>
        <div class="container">
            <ul>
                <li><a href="../../index.html">Home</a></li>
                <li><a href="#overview">Overview</a></li>
                <li><a href="#lectures">Lectures</a></li>
                <li><a href="#diagrams">Diagrams</a></li>
                <li><a href="#interactive">Interactive</a></li>
                <li><a href="#assessment">Assessment</a></li>
            </ul>
        </div>
    </nav>

    <main>
        <section id="overview" class="section">
            <div class="container">
                <h2>🎯 Module Overview: Advanced Signal Capture Techniques</h2>
                <div class="module-intro">
                    <p>This module focuses on the <strong>sophisticated techniques</strong> used to capture biomedical signals from the human body. Students will master advanced transduction principles, sensor technologies, and signal conditioning methods that transform physiological phenomena into measurable electrical signals.</p>
                    
                    <div class="acquisition-pathway">
                        <h3>📡 Signal Acquisition Journey</h3>
                        <div class="acquisition-steps">
                            <div class="acq-step" data-step="1">
                                <div class="step-icon">🔬</div>
                                <h4>Transduction</h4>
                                <p>Convert physiological signals to electrical</p>
                            </div>
                            <div class="step-connector">→</div>
                            <div class="acq-step" data-step="2">
                                <div class="step-icon">⚡</div>
                                <h4>Conditioning</h4>
                                <p>Amplify, filter, and isolate signals</p>
                            </div>
                            <div class="step-connector">→</div>
                            <div class="acq-step" data-step="3">
                                <div class="step-icon">💻</div>
                                <h4>Digitization</h4>
                                <p>Convert analog signals to digital</p>
                            </div>
                            <div class="step-connector">→</div>
                            <div class="acq-step" data-step="4">
                                <div class="step-icon">📊</div>
                                <h4>Processing</h4>
                                <p>Extract meaningful information</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="signal-types-overview">
                    <h3>🌊 Biomedical Signal Categories</h3>
                    <div class="signal-types-grid">
                        <div class="signal-type-card" data-signal="bioelectric">
                            <div class="signal-icon">⚡</div>
                            <h4>Bioelectric Signals</h4>
                            <p>ECG, EMG, EEG, EOG</p>
                            <div class="signal-specs">
                                <span>Frequency: 0.1-10kHz</span>
                                <span>Amplitude: µV-mV</span>
                            </div>
                        </div>
                        <div class="signal-type-card" data-signal="biomechanical">
                            <div class="signal-icon">🫀</div>
                            <h4>Biomechanical Signals</h4>
                            <p>Blood pressure, heart sounds, respiration</p>
                            <div class="signal-specs">
                                <span>Frequency: 0.1-1kHz</span>
                                <span>Amplitude: Variable</span>
                            </div>
                        </div>
                        <div class="signal-type-card" data-signal="biochemical">
                            <div class="signal-icon">🧪</div>
                            <h4>Biochemical Signals</h4>
                            <p>pH, O2, CO2, glucose</p>
                            <div class="signal-specs">
                                <span>Frequency: DC-1Hz</span>
                                <span>Amplitude: Variable</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <section id="lectures" class="section">
            <div class="container">
                <h2>📚 Interactive Lecture Series</h2>
                <div class="lectures-grid">
                    
                    <!-- Lecture 1: Transduction Principles -->
                    <div class="lecture-card" data-lecture="1">
                        <div class="lecture-header">
                            <span class="lecture-number">Lecture 1</span>
                            <h3>Advanced Transduction Principles</h3>
                            <div class="lecture-duration">Duration: 90 minutes</div>
                        </div>
                        <div class="lecture-content">
                            <div class="lecture-preview">
                                <div class="transducer-animation" id="transducer-demo">
                                    <div class="input-signal">📡</div>
                                    <div class="transducer-box">🔄</div>
                                    <div class="output-signal">⚡</div>
                                </div>
                            </div>
                            <div class="lecture-topics">
                                <h4>Topics Covered:</h4>
                                <ul>
                                    <li>🔄 Energy conversion mechanisms</li>
                                    <li>📏 Transducer characteristics and specifications</li>
                                    <li>🎯 Sensitivity, linearity, and dynamic range</li>
                                    <li>🌡️ Temperature effects and compensation</li>
                                    <li>⚡ Resistive, capacitive, and inductive transducers</li>
                                </ul>
                            </div>
                            <div class="lecture-actions">
                                <button class="btn-primary" onclick="openLectureSlides(1)">📖 View Slides</button>
                                <button class="btn-secondary" onclick="startTransducerDemo()">🔄 Transducer Demo</button>
                            </div>
                        </div>
                    </div>

                    <!-- Lecture 2: Electrode Systems -->
                    <div class="lecture-card" data-lecture="2">
                        <div class="lecture-header">
                            <span class="lecture-number">Lecture 2</span>
                            <h3>Sophisticated Electrode Systems</h3>
                            <div class="lecture-duration">Duration: 90 minutes</div>
                        </div>
                        <div class="lecture-content">
                            <div class="lecture-preview">
                                <div class="electrode-types" id="electrode-showcase">
                                    <div class="electrode surface-electrode">🔘</div>
                                    <div class="electrode needle-electrode">📍</div>
                                    <div class="electrode microelectrode">🔬</div>
                                </div>
                            </div>
                            <div class="lecture-topics">
                                <h4>Topics Covered:</h4>
                                <ul>
                                    <li>🔘 Surface electrodes and skin interface</li>
                                    <li>📍 Invasive electrodes and biocompatibility</li>
                                    <li>🔬 Microelectrodes and single-cell recording</li>
                                    <li>⚡ Electrode-electrolyte interface</li>
                                    <li>🛡️ Polarization and impedance effects</li>
                                </ul>
                            </div>
                            <div class="lecture-actions">
                                <button class="btn-primary" onclick="openLectureSlides(2)">📖 View Slides</button>
                                <button class="btn-secondary" onclick="startElectrodeDemo()">🔘 Electrode Explorer</button>
                            </div>
                        </div>
                    </div>

                    <!-- Lecture 3: Signal Conditioning -->
                    <div class="lecture-card" data-lecture="3">
                        <div class="lecture-header">
                            <span class="lecture-number">Lecture 3</span>
                            <h3>Advanced Signal Conditioning</h3>
                            <div class="lecture-duration">Duration: 90 minutes</div>
                        </div>
                        <div class="lecture-content">
                            <div class="lecture-preview">
                                <div class="conditioning-chain" id="conditioning-demo">
                                    <div class="stage">📡</div>
                                    <div class="arrow">→</div>
                                    <div class="stage">📈</div>
                                    <div class="arrow">→</div>
                                    <div class="stage">🔧</div>
                                    <div class="arrow">→</div>
                                    <div class="stage">📊</div>
                                </div>
                            </div>
                            <div class="lecture-topics">
                                <h4>Topics Covered:</h4>
                                <ul>
                                    <li>📈 Instrumentation amplifiers and differential amplification</li>
                                    <li>🔧 Active and passive filtering techniques</li>
                                    <li>🛡️ Electrical isolation and patient safety</li>
                                    <li>📡 Common mode rejection and noise reduction</li>
                                    <li>⚡ Right leg drive and guard circuits</li>
                                </ul>
                            </div>
                            <div class="lecture-actions">
                                <button class="btn-primary" onclick="openLectureSlides(3)">📖 View Slides</button>
                                <button class="btn-secondary" onclick="startConditioningDemo()">🔧 Conditioning Lab</button>
                            </div>
                        </div>
                    </div>

                    <!-- Lecture 4: ADC Systems -->
                    <div class="lecture-card" data-lecture="4">
                        <div class="lecture-header">
                            <span class="lecture-number">Lecture 4</span>
                            <h3>High-Resolution ADC Systems</h3>
                            <div class="lecture-duration">Duration: 90 minutes</div>
                        </div>
                        <div class="lecture-content">
                            <div class="lecture-preview">
                                <div class="adc-visualization" id="adc-demo">
                                    <canvas id="adcCanvas" width="300" height="150"></canvas>
                                </div>
                            </div>
                            <div class="lecture-topics">
                                <h4>Topics Covered:</h4>
                                <ul>
                                    <li>📊 Sampling theory and Nyquist criterion</li>
                                    <li>🔢 Quantization and resolution requirements</li>
                                    <li>⚡ ADC architectures: SAR, Delta-Sigma, Flash</li>
                                    <li>📈 Anti-aliasing filters and sample-and-hold</li>
                                    <li>💻 Digital signal processing fundamentals</li>
                                </ul>
                            </div>
                            <div class="lecture-actions">
                                <button class="btn-primary" onclick="openLectureSlides(4)">📖 View Slides</button>
                                <button class="btn-secondary" onclick="startADCDemo()">💻 ADC Simulator</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <section id="diagrams" class="section">
            <div class="container">
                <h2>📊 Interactive System Diagrams</h2>
                
                <!-- Block Diagram: Signal Acquisition Chain -->
                <div class="diagram-section">
                    <h3>🔲 Block Diagram: Complete Signal Acquisition System</h3>
                    <div class="block-diagram-container">
                        <div class="acquisition-chain" id="acquisitionChain">
                            <div class="chain-block" data-block="sensor">
                                <div class="block-icon">🔬</div>
                                <h4>Sensor/Electrode</h4>
                                <p>Transduction</p>
                                <div class="block-specs">
                                    <span>Sensitivity: µV/unit</span>
                                    <span>Bandwidth: 0.1-1kHz</span>
                                </div>
                            </div>
                            <div class="chain-arrow">→</div>
                            <div class="chain-block" data-block="preamp">
                                <div class="block-icon">📈</div>
                                <h4>Pre-amplifier</h4>
                                <p>Initial amplification</p>
                                <div class="block-specs">
                                    <span>Gain: 10-1000x</span>
                                    <span>Input Z: >10MΩ</span>
                                </div>
                            </div>
                            <div class="chain-arrow">→</div>
                            <div class="chain-block" data-block="filter">
                                <div class="block-icon">🔧</div>
                                <h4>Filter</h4>
                                <p>Noise reduction</p>
                                <div class="block-specs">
                                    <span>Type: Bandpass</span>
                                    <span>Order: 2nd-8th</span>
                                </div>
                            </div>
                            <div class="chain-arrow">→</div>
                            <div class="chain-block" data-block="isolation">
                                <div class="block-icon">🛡️</div>
                                <h4>Isolation</h4>
                                <p>Patient safety</p>
                                <div class="block-specs">
                                    <span>Isolation: 4kV</span>
                                    <span>Leakage: <10µA</span>
                                </div>
                            </div>
                            <div class="chain-arrow">→</div>
                            <div class="chain-block" data-block="adc">
                                <div class="block-icon">💻</div>
                                <h4>ADC</h4>
                                <p>Digitization</p>
                                <div class="block-specs">
                                    <span>Resolution: 12-24 bits</span>
                                    <span>Rate: 1-10kSPS</span>
                                </div>
                            </div>
                        </div>
                        <div class="diagram-controls">
                            <button class="btn-control" onclick="animateAcquisitionChain()">▶️ Animate Signal Flow</button>
                            <button class="btn-control" onclick="showSignalTransforms()">📊 Show Signal Transforms</button>
                            <button class="btn-control" onclick="resetAcquisitionChain()">🔄 Reset</button>
                        </div>
                    </div>
                </div>

                <!-- Schematic Diagram: Instrumentation Amplifier -->
                <div class="diagram-section">
                    <h3>⚡ Schematic: Three Op-Amp Instrumentation Amplifier</h3>
                    <div class="schematic-diagram-container">
                        <div class="instrumentation-amp" id="instAmpSchematic">
                            <svg width="700" height="450" viewBox="0 0 700 450">
                                <!-- Input Stage Op-Amps -->
                                <g class="opamp" data-component="opamp1">
                                    <polygon points="100,150 100,200 150,175" fill="#3498db" stroke="#2980b9" stroke-width="2"/>
                                    <text x="110" y="180" fill="white" font-size="12">+</text>
                                    <text x="110" y="165" fill="white" font-size="12">-</text>
                                    <text x="125" y="140" class="component-label">A1</text>
                                </g>
                                
                                <g class="opamp" data-component="opamp2">
                                    <polygon points="100,250 100,300 150,275" fill="#3498db" stroke="#2980b9" stroke-width="2"/>
                                    <text x="110" y="280" fill="white" font-size="12">+</text>
                                    <text x="110" y="265" fill="white" font-size="12">-</text>
                                    <text x="125" y="240" class="component-label">A2</text>
                                </g>
                                
                                <!-- Output Stage Op-Amp -->
                                <g class="opamp" data-component="opamp3">
                                    <polygon points="500,200 500,250 550,225" fill="#e74c3c" stroke="#c0392b" stroke-width="2"/>
                                    <text x="510" y="230" fill="white" font-size="12">+</text>
                                    <text x="510" y="215" fill="white" font-size="12">-</text>
                                    <text x="525" y="190" class="component-label">A3</text>
                                </g>
                                
                                <!-- Gain Setting Resistor -->
                                <g class="resistor" data-component="rgain">
                                    <rect x="120" y="210" width="60" height="15" fill="#f39c12" stroke="#e67e22" stroke-width="2"/>
                                    <text x="150" y="240" text-anchor="middle" class="component-label">RG</text>
                                    <text x="150" y="255" text-anchor="middle" class="component-value">1kΩ-100kΩ</text>
                                </g>
                                
                                <!-- Feedback Resistors -->
                                <g class="resistor" data-component="r1">
                                    <rect x="300" y="180" width="40" height="10" fill="#27ae60" stroke="#229954" stroke-width="1"/>
                                    <text x="320" y="175" text-anchor="middle" class="component-label">R1</text>
                                </g>
                                
                                <g class="resistor" data-component="r2">
                                    <rect x="300" y="240" width="40" height="10" fill="#27ae60" stroke="#229954" stroke-width="1"/>
                                    <text x="320" y="265" text-anchor="middle" class="component-label">R2</text>
                                </g>
                                
                                <!-- Input Connections -->
                                <line x1="50" y1="160" x2="100" y2="160" stroke="#34495e" stroke-width="2"/>
                                <line x1="50" y1="290" x2="100" y2="290" stroke="#34495e" stroke-width="2"/>
                                <text x="30" y="165" class="input-label">V1</text>
                                <text x="30" y="295" class="input-label">V2</text>
                                
                                <!-- Internal Connections -->
                                <line x1="150" y1="175" x2="200" y2="175" stroke="#34495e" stroke-width="2"/>
                                <line x1="150" y1="275" x2="200" y2="275" stroke="#34495e" stroke-width="2"/>
                                <line x1="200" y1="175" x2="300" y2="185" stroke="#34495e" stroke-width="2"/>
                                <line x1="200" y1="275" x2="300" y2="245" stroke="#34495e" stroke-width="2"/>
                                
                                <!-- Output -->
                                <line x1="550" y1="225" x2="650" y2="225" stroke="#34495e" stroke-width="2"/>
                                <text x="660" y="230" class="output-label">Vout</text>
                                
                                <!-- Gain Equation -->
                                <text x="350" y="350" class="equation">Gain = (1 + 2R/RG) × (R2/R1)</text>
                                <text x="350" y="370" class="equation">CMRR > 100 dB</text>
                                <text x="350" y="390" class="equation">Input Impedance > 10¹² Ω</text>
                            </svg>
                        </div>
                        <div class="diagram-controls">
                            <button class="btn-control" onclick="simulateInstAmp()">⚡ Simulate Operation</button>
                            <button class="btn-control" onclick="adjustGain()">📈 Adjust Gain</button>
                            <button class="btn-control" onclick="showCMRR()">🔧 Show CMRR</button>
                            <button class="btn-control" onclick="resetInstAmp()">🔄 Reset</button>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <section id="interactive" class="section">
            <div class="container">
                <h2>🎮 Interactive Learning Tools</h2>
                
                <div class="interactive-tools-grid">
                    <!-- Transducer Characteristics Explorer -->
                    <div class="tool-card">
                        <h3>🔄 Transducer Characteristics Explorer</h3>
                        <div class="tool-preview">
                            <div class="transducer-explorer" id="transducerExplorer">
                                <canvas id="transducerCanvas" width="400" height="200"></canvas>
                            </div>
                        </div>
                        <div class="tool-controls">
                            <label>Input Range: <span id="inputRangeDisplay">0-100</span></label>
                            <input type="range" id="inputRangeSlider" min="10" max="1000" value="100">
                            <label>Sensitivity: <span id="sensitivityDisplay">1.0 mV/unit</span></label>
                            <input type="range" id="sensitivitySlider" min="0.1" max="10" value="1" step="0.1">
                            <label>Linearity Error: <span id="linearityDisplay">0%</span></label>
                            <input type="range" id="linearitySlider" min="0" max="10" value="0" step="0.1">
                            <button onclick="updateTransducerResponse()">📊 Update Response</button>
                        </div>
                    </div>

                    <!-- Filter Design Tool -->
                    <div class="tool-card">
                        <h3>🔧 Active Filter Designer</h3>
                        <div class="tool-preview">
                            <canvas id="filterCanvas" width="400" height="200"></canvas>
                        </div>
                        <div class="tool-controls">
                            <label>Filter Type:</label>
                            <select id="filterType">
                                <option value="lowpass">Low Pass</option>
                                <option value="highpass">High Pass</option>
                                <option value="bandpass">Band Pass</option>
                                <option value="notch">Notch</option>
                            </select>
                            <label>Cutoff Frequency: <span id="cutoffDisplay">100 Hz</span></label>
                            <input type="range" id="cutoffSlider" min="1" max="1000" value="100">
                            <label>Order: <span id="orderDisplay">2</span></label>
                            <input type="range" id="orderSlider" min="1" max="8" value="2">
                            <button onclick="designFilter()">🔧 Design Filter</button>
                        </div>
                    </div>

                    <!-- ADC Performance Analyzer -->
                    <div class="tool-card">
                        <h3>💻 ADC Performance Analyzer</h3>
                        <div class="tool-preview">
                            <canvas id="adcAnalyzerCanvas" width="400" height="200"></canvas>
                        </div>
                        <div class="tool-controls">
                            <label>Resolution: <span id="resolutionDisplay">12 bits</span></label>
                            <input type="range" id="resolutionSlider" min="8" max="24" value="12">
                            <label>Sampling Rate: <span id="samplingDisplay">1000 SPS</span></label>
                            <input type="range" id="samplingSlider" min="100" max="10000" value="1000" step="100">
                            <label>Input Signal Frequency: <span id="signalFreqDisplay">50 Hz</span></label>
                            <input type="range" id="signalFreqSlider" min="1" max="500" value="50">
                            <button onclick="analyzeADC()">📊 Analyze Performance</button>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <section id="assessment" class="section">
            <div class="container">
                <h2>📝 Assessment & Evaluation</h2>
                
                <div class="assessment-overview">
                    <div class="assessment-breakdown">
                        <h3>Assessment Distribution</h3>
                        <canvas id="assessmentChart" width="300" height="300"></canvas>
                    </div>
                    
                    <div class="assessment-details">
                        <div class="assessment-item">
                            <h4>🔬 Virtual Laboratory Work (45%)</h4>
                            <ul>
                                <li>Transducer characterization lab</li>
                                <li>Electrode interface analysis</li>
                                <li>Signal conditioning design</li>
                                <li>ADC performance evaluation</li>
                            </ul>
                        </div>
                        
                        <div class="assessment-item">
                            <h4>📝 Theory & Problem Solving (35%)</h4>
                            <ul>
                                <li>Transduction principles quiz</li>
                                <li>Circuit analysis problems</li>
                                <li>Filter design calculations</li>
                                <li>Sampling theory applications</li>
                            </ul>
                        </div>
                        
                        <div class="assessment-item">
                            <h4>🎯 Design Project (20%)</h4>
                            <ul>
                                <li>Complete acquisition system design</li>
                                <li>Component selection and justification</li>
                                <li>Performance analysis and optimization</li>
                                <li>Presentation and documentation</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <footer>
        <div class="container">
            <div class="footer-content">
                <div class="author-info">
                    <h3>Course Author</h3>
                    <p><strong>Dr. Mohammed Yagoub Esmail</strong></p>
                    <p>Sudan University of Science and Technology (SUST)</p>
                    <p>Biomedical Engineering Department</p>
                </div>
                
                <div class="contact-info">
                    <h3>Contact Information</h3>
                    <p>📧 Email: <a href="mailto:<EMAIL>"><EMAIL></a></p>
                    <p>📱 Phone: +249912867327, +966538076790</p>
                </div>
            </div>
            
            <div class="copyright">
                <p>&copy; 2025 Dr. Mohammed Yagoub Esmail - SUST BME</p>
                <p>All rights reserved. Email: <EMAIL></p>
            </div>
        </div>
    </footer>

    <script src="module2-animations.js"></script>
    <script src="module2-interactive.js"></script>
</body>
</html>
