// Module 1 Interactive Tools and Simulations

// Interactive tool variables
let channelSimulationRunning = false;
let apGeneratorRunning = false;
let currentSignalType = 'heart';

// Initialize interactive tools
document.addEventListener('DOMContentLoaded', function() {
    setupVoltageSlider();
    setupStimulusSlider();
    setupOrganButtons();
    setupQuizInteraction();
    initializeChannelSimulator();
});

// Voltage slider for ion channel simulator
function setupVoltageSlider() {
    const voltageSlider = document.getElementById('voltageSlider');
    const voltageDisplay = document.getElementById('voltageDisplay');
    
    if (voltageSlider && voltageDisplay) {
        voltageSlider.addEventListener('input', function() {
            const voltage = this.value;
            voltageDisplay.textContent = voltage + 'mV';
            updateChannelStates(voltage);
        });
    }
}

// Stimulus slider for action potential generator
function setupStimulusSlider() {
    const stimulusSlider = document.getElementById('stimulusSlider');
    const stimulusDisplay = document.getElementById('stimulusDisplay');
    
    if (stimulusSlider && stimulusDisplay) {
        stimulusSlider.addEventListener('input', function() {
            const stimulus = this.value;
            stimulusDisplay.textContent = stimulus + 'mA';
        });
    }
}

// Organ selection buttons
function setupOrganButtons() {
    const organButtons = document.querySelectorAll('.organ-btn');
    
    organButtons.forEach(button => {
        button.addEventListener('click', function() {
            // Remove active class from all buttons
            organButtons.forEach(btn => btn.classList.remove('active'));
            
            // Add active class to clicked button
            this.classList.add('active');
            
            // Update current signal type
            currentSignalType = this.dataset.organ;
            updateSignalDisplay(currentSignalType);
        });
    });
}

// Quiz interaction setup
function setupQuizInteraction() {
    const options = document.querySelectorAll('.option');
    
    options.forEach(option => {
        option.addEventListener('click', function() {
            // Remove previous selections
            options.forEach(opt => {
                opt.classList.remove('correct', 'wrong', 'selected');
            });
            
            // Mark this option as selected
            this.classList.add('selected');
            
            // Check if answer is correct
            const isCorrect = this.dataset.answer === 'correct';
            
            if (isCorrect) {
                this.classList.add('correct');
                showQuizFeedback('Correct! The resting membrane potential of a typical neuron is approximately -70mV.', 'success');
            } else {
                this.classList.add('wrong');
                // Also highlight the correct answer
                const correctOption = document.querySelector('[data-answer="correct"]');
                if (correctOption) {
                    correctOption.classList.add('correct');
                }
                showQuizFeedback('Incorrect. The correct answer is -70mV. This potential is maintained by the sodium-potassium pump and selective membrane permeability.', 'error');
            }
        });
    });
}

// Show quiz feedback
function showQuizFeedback(message, type) {
    const feedbackElement = document.getElementById('quizFeedback');
    if (feedbackElement) {
        feedbackElement.innerHTML = `
            <div class="feedback ${type}">
                <p>${message}</p>
            </div>
        `;
        
        // Animate feedback appearance
        gsap.from('.feedback', {
            duration: 0.5,
            y: 20,
            opacity: 0
        });
    }
}

// Initialize channel simulator
function initializeChannelSimulator() {
    const simulator = document.getElementById('channelSim');
    if (!simulator) return;
    
    // Create additional ions for animation
    createFloatingIons();
}

// Create floating ions for animation
function createFloatingIons() {
    const simulator = document.getElementById('channelSim');
    if (!simulator) return;
    
    const ionTypes = [
        { class: 'na-ion', symbol: 'Na+', color: '#e74c3c' },
        { class: 'k-ion', symbol: 'K+', color: '#9b59b6' },
        { class: 'cl-ion', symbol: 'Cl-', color: '#f39c12' }
    ];
    
    // Create multiple ions of each type
    ionTypes.forEach(ionType => {
        for (let i = 0; i < 3; i++) {
            const ion = document.createElement('div');
            ion.className = `ion ${ionType.class} floating-ion`;
            ion.textContent = ionType.symbol;
            ion.style.backgroundColor = ionType.color;
            ion.style.left = Math.random() * 250 + 'px';
            ion.style.top = Math.random() * 100 + 20 + 'px';
            simulator.querySelector('.membrane-representation').appendChild(ion);
        }
    });
}

// Update channel states based on voltage
function updateChannelStates(voltage) {
    const naGate = document.getElementById('naGate');
    const kGate = document.getElementById('kGate');
    const ions = document.querySelectorAll('.floating-ion');
    
    if (voltage > -55) { // Above threshold
        // Open sodium channels
        if (naGate) {
            naGate.style.backgroundColor = '#27ae60';
            naGate.textContent = 'Na+ OPEN';
        }
        
        // Animate sodium ions moving inward
        const naIons = document.querySelectorAll('.na-ion.floating-ion');
        naIons.forEach(ion => {
            gsap.to(ion, {
                duration: 1,
                x: '+=30',
                scale: 1.2,
                repeat: -1,
                yoyo: true
            });
        });
    } else {
        // Close sodium channels
        if (naGate) {
            naGate.style.backgroundColor = '#3498db';
            naGate.textContent = 'Na+ Gate';
        }
        
        // Stop sodium ion animation
        const naIons = document.querySelectorAll('.na-ion.floating-ion');
        naIons.forEach(ion => {
            gsap.killTweensOf(ion);
            gsap.set(ion, { x: 0, scale: 1 });
        });
    }
    
    if (voltage > -30) { // Potassium channels open during repolarization
        if (kGate) {
            kGate.style.backgroundColor = '#27ae60';
            kGate.textContent = 'K+ OPEN';
        }
        
        // Animate potassium ions moving outward
        const kIons = document.querySelectorAll('.k-ion.floating-ion');
        kIons.forEach(ion => {
            gsap.to(ion, {
                duration: 1.5,
                x: '-=30',
                scale: 1.2,
                repeat: -1,
                yoyo: true
            });
        });
    } else {
        if (kGate) {
            kGate.style.backgroundColor = '#3498db';
            kGate.textContent = 'K+ Gate';
        }
        
        // Stop potassium ion animation
        const kIons = document.querySelectorAll('.k-ion.floating-ion');
        kIons.forEach(ion => {
            gsap.killTweensOf(ion);
            gsap.set(ion, { x: 0, scale: 1 });
        });
    }
}

// Start channel simulation
function startChannelSimulation() {
    if (channelSimulationRunning) return;
    
    channelSimulationRunning = true;
    const voltageSlider = document.getElementById('voltageSlider');
    
    // Simulate action potential voltage changes
    const voltageSequence = [-70, -55, -30, 30, -80, -70];
    let currentStep = 0;
    
    const simulationInterval = setInterval(() => {
        if (currentStep >= voltageSequence.length) {
            clearInterval(simulationInterval);
            channelSimulationRunning = false;
            return;
        }
        
        const targetVoltage = voltageSequence[currentStep];
        
        // Animate voltage slider
        gsap.to(voltageSlider, {
            duration: 1,
            value: targetVoltage,
            onUpdate: function() {
                voltageSlider.value = this.targets()[0].value;
                voltageSlider.dispatchEvent(new Event('input'));
            }
        });
        
        currentStep++;
    }, 1500);
}

// Generate action potential
function generateActionPotential() {
    const canvas = document.getElementById('apGenerator');
    if (!canvas) return;
    
    const ctx = canvas.getContext('2d');
    const stimulusValue = document.getElementById('stimulusSlider').value;
    
    // Clear previous trace
    ctx.clearRect(0, 0, canvas.width, canvas.height);
    
    // Draw axes
    drawAPAxes(ctx, canvas.width, canvas.height);
    
    if (stimulusValue >= 5) { // Threshold stimulus
        drawActionPotential(ctx, canvas.width, canvas.height);
        showAPPhases();
    } else {
        drawSubthresholdResponse(ctx, canvas.width, canvas.height, stimulusValue);
    }
}

// Draw action potential axes
function drawAPAxes(ctx, width, height) {
    ctx.strokeStyle = '#34495e';
    ctx.lineWidth = 2;
    
    // X-axis
    ctx.beginPath();
    ctx.moveTo(40, height - 40);
    ctx.lineTo(width - 20, height - 40);
    ctx.stroke();
    
    // Y-axis
    ctx.beginPath();
    ctx.moveTo(40, 20);
    ctx.lineTo(40, height - 40);
    ctx.stroke();
    
    // Labels
    ctx.fillStyle = '#2c3e50';
    ctx.font = '12px Arial';
    ctx.fillText('Time (ms)', width - 60, height - 20);
    ctx.save();
    ctx.translate(15, height / 2);
    ctx.rotate(-Math.PI / 2);
    ctx.fillText('mV', 0, 0);
    ctx.restore();
    
    // Voltage markers
    ctx.fillText('-70', 5, height - 35);
    ctx.fillText('0', 10, height / 2);
    ctx.fillText('+30', 5, 35);
}

// Draw action potential curve
function drawActionPotential(ctx, width, height) {
    ctx.strokeStyle = '#e74c3c';
    ctx.lineWidth = 3;
    ctx.beginPath();
    
    const baseY = height - 40;
    const restingY = baseY - 30; // -70mV
    const peakY = 40; // +30mV
    
    // Action potential phases
    const points = [
        { x: 40, y: restingY },      // Resting
        { x: 80, y: restingY },      // Stimulus
        { x: 120, y: peakY },        // Depolarization
        { x: 160, y: peakY - 10 },   // Peak
        { x: 200, y: restingY + 10 }, // Repolarization
        { x: 240, y: restingY - 10 }, // Hyperpolarization
        { x: 280, y: restingY }      // Return to rest
    ];
    
    ctx.moveTo(points[0].x, points[0].y);
    
    // Animate drawing
    let pointIndex = 1;
    const drawInterval = setInterval(() => {
        if (pointIndex >= points.length) {
            clearInterval(drawInterval);
            return;
        }
        
        ctx.lineTo(points[pointIndex].x, points[pointIndex].y);
        ctx.stroke();
        pointIndex++;
    }, 200);
}

// Draw subthreshold response
function drawSubthresholdResponse(ctx, width, height, stimulus) {
    ctx.strokeStyle = '#95a5a6';
    ctx.lineWidth = 2;
    ctx.beginPath();
    
    const baseY = height - 40;
    const restingY = baseY - 30;
    const responseY = restingY - (stimulus * 2); // Small depolarization
    
    ctx.moveTo(40, restingY);
    ctx.lineTo(80, restingY);
    ctx.lineTo(120, responseY);
    ctx.lineTo(160, responseY);
    ctx.lineTo(200, restingY);
    ctx.stroke();
    
    // Add text
    ctx.fillStyle = '#7f8c8d';
    ctx.font = '14px Arial';
    ctx.fillText('Subthreshold', 150, 30);
}

// Show action potential phases
function showAPPhases() {
    const phases = [
        'Resting Potential (-70mV)',
        'Stimulus Applied',
        'Rapid Depolarization',
        'Peak (+30mV)',
        'Repolarization',
        'Hyperpolarization',
        'Return to Rest'
    ];
    
    let phaseIndex = 0;
    const phaseInterval = setInterval(() => {
        if (phaseIndex >= phases.length) {
            clearInterval(phaseInterval);
            return;
        }
        
        console.log(`Phase ${phaseIndex + 1}: ${phases[phaseIndex]}`);
        // In a real implementation, this would update a phase indicator on the UI
        phaseIndex++;
    }, 300);
}

// Clear action potential trace
function clearAPTrace() {
    const canvas = document.getElementById('apGenerator');
    if (!canvas) return;
    
    const ctx = canvas.getContext('2d');
    ctx.clearRect(0, 0, canvas.width, canvas.height);
    drawAPAxes(ctx, canvas.width, canvas.height);
}

// Update signal display for pathway explorer
function updateSignalDisplay(organType) {
    const canvas = document.getElementById('signalCanvas');
    if (!canvas) return;
    
    const ctx = canvas.getContext('2d');
    ctx.clearRect(0, 0, canvas.width, canvas.height);
    
    // Draw appropriate signal based on organ type
    switch (organType) {
        case 'heart':
            drawECGSignal(ctx, canvas.width, canvas.height);
            break;
        case 'brain':
            drawEEGSignal(ctx, canvas.width, canvas.height);
            break;
        case 'muscle':
            drawEMGSignal(ctx, canvas.width, canvas.height);
            break;
    }
}

// Draw ECG signal
function drawECGSignal(ctx, width, height) {
    ctx.strokeStyle = '#e74c3c';
    ctx.lineWidth = 2;
    ctx.beginPath();
    
    const centerY = height / 2;
    const amplitude = 40;
    
    // Simplified ECG waveform
    ctx.moveTo(0, centerY);
    ctx.lineTo(50, centerY);
    ctx.lineTo(60, centerY - amplitude * 0.3); // P wave
    ctx.lineTo(70, centerY);
    ctx.lineTo(80, centerY);
    ctx.lineTo(85, centerY + amplitude * 0.2); // Q wave
    ctx.lineTo(90, centerY + amplitude); // R wave
    ctx.lineTo(95, centerY - amplitude * 0.3); // S wave
    ctx.lineTo(100, centerY);
    ctx.lineTo(120, centerY);
    ctx.lineTo(140, centerY + amplitude * 0.4); // T wave
    ctx.lineTo(160, centerY);
    ctx.lineTo(width, centerY);
    ctx.stroke();
    
    // Label
    ctx.fillStyle = '#e74c3c';
    ctx.font = '14px Arial';
    ctx.fillText('ECG Signal', 10, 20);
}

// Draw EEG signal
function drawEEGSignal(ctx, width, height) {
    ctx.strokeStyle = '#9b59b6';
    ctx.lineWidth = 2;
    ctx.beginPath();
    
    const centerY = height / 2;
    
    // Simulate alpha waves (8-12 Hz)
    ctx.moveTo(0, centerY);
    for (let x = 0; x < width; x += 2) {
        const y = centerY + Math.sin(x * 0.1) * 20 + Math.random() * 10 - 5;
        ctx.lineTo(x, y);
    }
    ctx.stroke();
    
    // Label
    ctx.fillStyle = '#9b59b6';
    ctx.font = '14px Arial';
    ctx.fillText('EEG Signal (Alpha Waves)', 10, 20);
}

// Draw EMG signal
function drawEMGSignal(ctx, width, height) {
    ctx.strokeStyle = '#27ae60';
    ctx.lineWidth = 1;
    ctx.beginPath();
    
    const centerY = height / 2;
    
    // Simulate EMG burst
    ctx.moveTo(0, centerY);
    for (let x = 0; x < width; x += 1) {
        let amplitude = 0;
        if (x > 100 && x < 200) { // Muscle activation period
            amplitude = (Math.random() - 0.5) * 60;
        } else {
            amplitude = (Math.random() - 0.5) * 10;
        }
        ctx.lineTo(x, centerY + amplitude);
    }
    ctx.stroke();
    
    // Label
    ctx.fillStyle = '#27ae60';
    ctx.font = '14px Arial';
    ctx.fillText('EMG Signal (Muscle Activation)', 10, 20);
}

// Explore signal path functions
function exploreSignalPath(organType) {
    currentSignalType = organType;
    updateSignalDisplay(organType);
    
    // Update organ button states
    document.querySelectorAll('.organ-btn').forEach(btn => {
        btn.classList.remove('active');
        if (btn.dataset.organ === organType) {
            btn.classList.add('active');
        }
    });
    
    // Show information about the signal
    showSignalInfo(organType);
}

// Show signal information
function showSignalInfo(organType) {
    const signalInfo = {
        heart: {
            title: 'Cardiac Signals (ECG)',
            description: 'Generated by the coordinated electrical activity of cardiac muscle cells. The signal originates from the sinoatrial node and propagates through the conduction system.',
            frequency: '0.5-100 Hz',
            amplitude: '0.1-3 mV'
        },
        brain: {
            title: 'Neural Signals (EEG)',
            description: 'Result from synchronized postsynaptic potentials of cortical neurons. Different frequency bands correspond to different brain states and activities.',
            frequency: '0.5-100 Hz',
            amplitude: '10-100 µV'
        },
        muscle: {
            title: 'Muscle Signals (EMG)',
            description: 'Generated by the electrical activity of muscle fibers during contraction. The signal amplitude correlates with the force of muscle contraction.',
            frequency: '10-500 Hz',
            amplitude: '0.1-5 mV'
        }
    };
    
    const info = signalInfo[organType];
    if (info) {
        // Update info display (this would be implemented in the UI)
        console.log(`Signal Type: ${info.title}`);
        console.log(`Description: ${info.description}`);
        console.log(`Frequency Range: ${info.frequency}`);
        console.log(`Amplitude Range: ${info.amplitude}`);
    }
}

// Add CSS for interactive elements
const interactiveStyle = document.createElement('style');
interactiveStyle.textContent = `
    .organ-btn {
        padding: 0.8rem 1.5rem;
        border: 2px solid #3498db;
        background: white;
        color: #3498db;
        border-radius: 6px;
        cursor: pointer;
        transition: all 0.3s ease;
        margin: 0.5rem;
    }
    
    .organ-btn:hover {
        background: #3498db;
        color: white;
    }
    
    .organ-btn.active {
        background: #3498db;
        color: white;
        box-shadow: 0 4px 8px rgba(52, 152, 219, 0.3);
    }
    
    .feedback {
        padding: 1rem;
        border-radius: 6px;
        margin-top: 1rem;
    }
    
    .feedback.success {
        background: #d5f4e6;
        border: 1px solid #27ae60;
        color: #27ae60;
    }
    
    .feedback.error {
        background: #fadbd8;
        border: 1px solid #e74c3c;
        color: #e74c3c;
    }
    
    .floating-ion {
        position: absolute !important;
        animation: ionFloat 3s ease-in-out infinite !important;
    }
    
    @keyframes ionFloat {
        0%, 100% { transform: translateY(0px); }
        50% { transform: translateY(-10px); }
    }
`;
document.head.appendChild(interactiveStyle);
