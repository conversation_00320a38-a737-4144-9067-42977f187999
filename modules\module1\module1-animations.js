// Module 1 Animations and Interactive Elements

// Initialize animations when page loads
document.addEventListener('DOMContentLoaded', function() {
    initializeAnimations();
    setupInteractiveElements();
    createActionPotentialGraph();
    createAssessmentChart();
});

// Initialize GSAP animations
function initializeAnimations() {
    // Animate step items on scroll
    gsap.registerPlugin(ScrollTrigger);
    
    gsap.from(".step-item", {
        duration: 1,
        y: 50,
        opacity: 0,
        stagger: 0.2,
        scrollTrigger: {
            trigger: ".pathway-steps",
            start: "top 80%"
        }
    });
    
    // Animate objective cards
    gsap.from(".objective-card", {
        duration: 1,
        scale: 0.8,
        opacity: 0,
        stagger: 0.15,
        scrollTrigger: {
            trigger: ".objectives-grid",
            start: "top 80%"
        }
    });
    
    // Animate lecture cards
    gsap.from(".lecture-card", {
        duration: 1,
        x: -50,
        opacity: 0,
        stagger: 0.2,
        scrollTrigger: {
            trigger: ".lectures-grid",
            start: "top 80%"
        }
    });
}

// Setup interactive elements
function setupInteractiveElements() {
    // Step item click interactions
    document.querySelectorAll('.step-item').forEach(item => {
        item.addEventListener('click', function() {
            const step = this.dataset.step;
            highlightStep(step);
            showStepDetails(step);
        });
    });
    
    // Block diagram interactions
    document.querySelectorAll('.block-item').forEach(block => {
        block.addEventListener('click', function() {
            const blockType = this.dataset.block;
            showBlockDetails(blockType);
        });
    });
    
    // Component interactions in schematic
    document.querySelectorAll('.component').forEach(component => {
        component.addEventListener('click', function() {
            const componentType = this.dataset.component;
            showComponentDetails(componentType);
        });
    });
}

// Highlight specific step in pathway
function highlightStep(stepNumber) {
    // Remove previous highlights
    document.querySelectorAll('.step-item').forEach(item => {
        item.classList.remove('highlighted');
    });
    
    // Add highlight to selected step
    const selectedStep = document.querySelector(`[data-step="${stepNumber}"]`);
    if (selectedStep) {
        selectedStep.classList.add('highlighted');
        
        // Animate highlight
        gsap.to(selectedStep, {
            duration: 0.5,
            scale: 1.05,
            boxShadow: "0 8px 25px rgba(52, 152, 219, 0.3)",
            yoyo: true,
            repeat: 1
        });
    }
}

// Show step details
function showStepDetails(stepNumber) {
    const stepDetails = {
        1: {
            title: "Cellular Foundations",
            content: "Understanding how ion channels and membrane potentials create the foundation for all bioelectric signals.",
            details: [
                "Cell membrane structure and lipid bilayer",
                "Ion channels: voltage-gated and ligand-gated",
                "Resting membrane potential (-70mV)",
                "Nernst equation and equilibrium potentials"
            ]
        },
        2: {
            title: "Action Potentials",
            content: "The fundamental mechanism of electrical signal generation and propagation in excitable cells.",
            details: [
                "Threshold potential and excitability",
                "Depolarization and repolarization phases",
                "Sodium and potassium channel dynamics",
                "Refractory periods and signal integrity"
            ]
        },
        3: {
            title: "Organ Systems",
            content: "How individual cell signals combine to create measurable organ-level electrical activity.",
            details: [
                "Signal summation and synchronization",
                "Cardiac conduction system",
                "Neural network activity",
                "Muscle fiber recruitment"
            ]
        },
        4: {
            title: "Safety & Standards",
            content: "Essential safety considerations and regulatory standards for medical instrumentation.",
            details: [
                "Patient electrical safety",
                "Device classification systems",
                "International standards (ISO, IEC)",
                "Risk management and quality assurance"
            ]
        }
    };
    
    const details = stepDetails[stepNumber];
    if (details) {
        showModal(details);
    }
}

// Show modal with step details
function showModal(details) {
    // Create modal if it doesn't exist
    let modal = document.getElementById('stepModal');
    if (!modal) {
        modal = document.createElement('div');
        modal.id = 'stepModal';
        modal.className = 'modal';
        modal.innerHTML = `
            <div class="modal-content">
                <span class="close">&times;</span>
                <h3 id="modalTitle"></h3>
                <p id="modalContent"></p>
                <ul id="modalDetails"></ul>
            </div>
        `;
        document.body.appendChild(modal);
        
        // Close modal functionality
        modal.querySelector('.close').addEventListener('click', () => {
            modal.style.display = 'none';
        });
        
        window.addEventListener('click', (e) => {
            if (e.target === modal) {
                modal.style.display = 'none';
            }
        });
    }
    
    // Update modal content
    document.getElementById('modalTitle').textContent = details.title;
    document.getElementById('modalContent').textContent = details.content;
    
    const detailsList = document.getElementById('modalDetails');
    detailsList.innerHTML = '';
    details.details.forEach(detail => {
        const li = document.createElement('li');
        li.textContent = detail;
        detailsList.appendChild(li);
    });
    
    // Show modal with animation
    modal.style.display = 'block';
    gsap.from('.modal-content', {
        duration: 0.3,
        scale: 0.8,
        opacity: 0
    });
}

// Block diagram animation
function animateBlockDiagram() {
    const blocks = document.querySelectorAll('.block-item');
    const arrows = document.querySelectorAll('.block-arrow');
    
    // Reset all blocks
    blocks.forEach(block => block.classList.remove('active'));
    
    // Animate sequence
    let delay = 0;
    blocks.forEach((block, index) => {
        setTimeout(() => {
            block.classList.add('active');
            
            // Animate arrow after block
            if (arrows[index]) {
                gsap.to(arrows[index], {
                    duration: 0.5,
                    scale: 1.2,
                    color: "#e74c3c",
                    yoyo: true,
                    repeat: 1
                });
            }
        }, delay);
        delay += 1000;
    });
}

// Reset block diagram
function resetBlockDiagram() {
    document.querySelectorAll('.block-item').forEach(block => {
        block.classList.remove('active');
    });
    
    gsap.set('.block-arrow', {
        scale: 1,
        color: "#3498db"
    });
}

// Show block details
function showBlockDetails(blockType) {
    const blockInfo = {
        cell: {
            title: "Cell Membrane",
            description: "The lipid bilayer that separates intracellular and extracellular environments, containing ion channels that control electrical activity."
        },
        potential: {
            title: "Action Potential",
            description: "The rapid change in membrane voltage that propagates along excitable cells, forming the basis of neural and cardiac signals."
        },
        propagation: {
            title: "Signal Propagation",
            description: "The mechanism by which electrical signals travel along cell membranes and between cells through gap junctions."
        },
        measurement: {
            title: "External Measurement",
            description: "The detection of bioelectric signals using electrodes placed on the body surface or inserted into tissues."
        }
    };
    
    const info = blockInfo[blockType];
    if (info) {
        // Create tooltip or info panel
        showTooltip(info.title, info.description);
    }
}

// Show tooltip
function showTooltip(title, description) {
    // Remove existing tooltip
    const existingTooltip = document.querySelector('.tooltip');
    if (existingTooltip) {
        existingTooltip.remove();
    }
    
    // Create new tooltip
    const tooltip = document.createElement('div');
    tooltip.className = 'tooltip';
    tooltip.innerHTML = `
        <h4>${title}</h4>
        <p>${description}</p>
    `;
    
    document.body.appendChild(tooltip);
    
    // Position tooltip
    const rect = event.target.getBoundingClientRect();
    tooltip.style.left = rect.left + 'px';
    tooltip.style.top = (rect.bottom + 10) + 'px';
    
    // Animate tooltip
    gsap.from(tooltip, {
        duration: 0.3,
        y: -10,
        opacity: 0
    });
    
    // Remove tooltip after 3 seconds
    setTimeout(() => {
        if (tooltip.parentNode) {
            gsap.to(tooltip, {
                duration: 0.3,
                y: -10,
                opacity: 0,
                onComplete: () => tooltip.remove()
            });
        }
    }, 3000);
}

// Create action potential graph
function createActionPotentialGraph() {
    const canvas = document.getElementById('apCanvas');
    if (!canvas) return;
    
    const ctx = canvas.getContext('2d');
    const width = canvas.width;
    const height = canvas.height;
    
    // Clear canvas
    ctx.clearRect(0, 0, width, height);
    
    // Draw axes
    ctx.strokeStyle = '#34495e';
    ctx.lineWidth = 2;
    ctx.beginPath();
    ctx.moveTo(50, height - 30);
    ctx.lineTo(width - 20, height - 30);
    ctx.moveTo(50, 20);
    ctx.lineTo(50, height - 30);
    ctx.stroke();
    
    // Draw action potential curve
    ctx.strokeStyle = '#e74c3c';
    ctx.lineWidth = 3;
    ctx.beginPath();
    
    const points = [
        {x: 50, y: height - 50},   // Resting potential
        {x: 80, y: height - 50},   // Stimulus
        {x: 120, y: 30},           // Depolarization
        {x: 160, y: 40},           // Peak
        {x: 200, y: height - 40},  // Repolarization
        {x: 240, y: height - 60},  // Hyperpolarization
        {x: 280, y: height - 50}   // Return to rest
    ];
    
    ctx.moveTo(points[0].x, points[0].y);
    for (let i = 1; i < points.length; i++) {
        ctx.lineTo(points[i].x, points[i].y);
    }
    ctx.stroke();
    
    // Add labels
    ctx.fillStyle = '#2c3e50';
    ctx.font = '12px Arial';
    ctx.fillText('Time (ms)', width - 60, height - 10);
    ctx.save();
    ctx.translate(15, height / 2);
    ctx.rotate(-Math.PI / 2);
    ctx.fillText('Voltage (mV)', 0, 0);
    ctx.restore();
    
    // Add voltage markers
    ctx.fillText('-70mV', 10, height - 45);
    ctx.fillText('+30mV', 10, 35);
    ctx.fillText('0mV', 10, height - 80);
}

// Create assessment chart
function createAssessmentChart() {
    const canvas = document.getElementById('assessmentChart');
    if (!canvas) return;
    
    const ctx = canvas.getContext('2d');
    
    new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: ['Virtual Labs', 'Quizzes & Tests', 'Projects'],
            datasets: [{
                data: [40, 35, 25],
                backgroundColor: [
                    '#3498db',
                    '#e74c3c',
                    '#27ae60'
                ],
                borderWidth: 2,
                borderColor: '#fff'
            }]
        },
        options: {
            responsive: true,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            },
            animation: {
                animateRotate: true,
                duration: 2000
            }
        }
    });
}

// Schematic diagram interactions
function simulateDepolarization() {
    const membrane = document.querySelector('.interactive-schematic');
    if (!membrane) return;
    
    // Animate voltage change
    gsap.to('.voltage-label', {
        duration: 2,
        color: '#e74c3c',
        scale: 1.1,
        yoyo: true,
        repeat: 1
    });
    
    // Animate sodium channels
    gsap.to('[data-component="sodium"]', {
        duration: 1,
        scale: 1.2,
        fill: '#c0392b',
        yoyo: true,
        repeat: 1
    });
}

function showCurrentFlow() {
    // Create animated current flow indicators
    const svg = document.querySelector('.interactive-schematic svg');
    if (!svg) return;
    
    // Add current flow arrows
    const arrow = document.createElementNS('http://www.w3.org/2000/svg', 'path');
    arrow.setAttribute('d', 'M150,180 L300,180');
    arrow.setAttribute('stroke', '#f39c12');
    arrow.setAttribute('stroke-width', '3');
    arrow.setAttribute('marker-end', 'url(#arrowhead)');
    arrow.setAttribute('class', 'current-flow');
    
    svg.appendChild(arrow);
    
    // Animate current flow
    gsap.from('.current-flow', {
        duration: 1,
        strokeDasharray: '10,5',
        strokeDashoffset: 15,
        repeat: 3
    });
    
    // Remove after animation
    setTimeout(() => {
        if (arrow.parentNode) {
            arrow.remove();
        }
    }, 4000);
}

function resetSchematic() {
    // Reset all components to original state
    gsap.set('.component', {
        scale: 1,
        fill: 'original'
    });
    
    gsap.set('.voltage-label', {
        color: '#e74c3c',
        scale: 1
    });
    
    // Remove any current flow indicators
    document.querySelectorAll('.current-flow').forEach(el => el.remove());
}

// Lecture slide functions
function openLectureSlides(lectureNumber) {
    // This would open the specific lecture slides
    // For now, we'll show a placeholder
    alert(`Opening Lecture ${lectureNumber} slides...`);
    // In a real implementation, this would open a new window or modal with the slides
}

function startInteractiveDemo(demoNumber) {
    // This would start the interactive demo for the specific lecture
    alert(`Starting Interactive Demo ${demoNumber}...`);
    // In a real implementation, this would launch the appropriate interactive simulation
}

// Add CSS for modal and tooltip
const style = document.createElement('style');
style.textContent = `
    .modal {
        display: none;
        position: fixed;
        z-index: 1000;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0,0,0,0.5);
    }
    
    .modal-content {
        background-color: white;
        margin: 15% auto;
        padding: 2rem;
        border-radius: 12px;
        width: 80%;
        max-width: 600px;
        position: relative;
    }
    
    .close {
        color: #aaa;
        float: right;
        font-size: 28px;
        font-weight: bold;
        cursor: pointer;
        position: absolute;
        right: 1rem;
        top: 1rem;
    }
    
    .close:hover {
        color: #000;
    }
    
    .tooltip {
        position: absolute;
        background: #2c3e50;
        color: white;
        padding: 1rem;
        border-radius: 8px;
        max-width: 300px;
        z-index: 1000;
        box-shadow: 0 4px 8px rgba(0,0,0,0.2);
    }
    
    .tooltip h4 {
        margin: 0 0 0.5rem 0;
        color: #3498db;
    }
    
    .tooltip p {
        margin: 0;
        font-size: 0.9rem;
    }
    
    .highlighted {
        background: linear-gradient(135deg, #e74c3c, #c0392b) !important;
        color: white !important;
    }
`;
document.head.appendChild(style);
