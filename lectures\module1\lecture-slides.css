/* Lecture Slides Styling */

/* Base Lecture Mode */
.lecture-mode {
    background: linear-gradient(135deg, #f5f7fa, #c3cfe2);
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    margin: 0;
    padding: 0;
    overflow-x: hidden;
    min-height: 100vh;
}

/* Lecture Header */
.lecture-header {
    background: linear-gradient(135deg, #2c3e50, #34495e);
    color: white;
    padding: 1rem 0;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.lecture-navigation {
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-wrap: wrap;
    gap: 1rem;
}

.nav-btn {
    background: rgba(255,255,255,0.1);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 6px;
    text-decoration: none;
    transition: all 0.3s ease;
    font-size: 0.9rem;
}

.nav-btn:hover {
    background: rgba(255,255,255,0.2);
    transform: translateY(-2px);
}

.lecture-info {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
}

.lecture-title {
    font-weight: bold;
    font-size: 1.1rem;
    margin-bottom: 0.25rem;
}

.slide-counter {
    font-size: 0.9rem;
    opacity: 0.8;
}

.lecture-controls {
    display: flex;
    gap: 0.5rem;
}

.control-btn {
    background: #3498db;
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.9rem;
}

.control-btn:hover {
    background: #2980b9;
    transform: translateY(-2px);
}

.control-btn:disabled {
    background: #95a5a6;
    cursor: not-allowed;
    transform: none;
}

/* Main Lecture Content */
.lecture-content {
    margin-top: 100px;
    min-height: calc(100vh - 150px);
    padding: 2rem 0;
}

.slides-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
}

/* Individual Slides */
.slide {
    display: none;
    background: white;
    border-radius: 12px;
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
    padding: 3rem;
    margin-bottom: 2rem;
    min-height: 600px;
    animation: slideIn 0.5s ease-in-out;
}

.slide.active {
    display: block;
}

.slide-content h2 {
    color: #2c3e50;
    border-bottom: 3px solid #3498db;
    padding-bottom: 1rem;
    margin-bottom: 2rem;
    font-size: 2rem;
}

/* Title Slide */
.title-slide {
    text-align: center;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    min-height: 500px;
}

.title-slide h1 {
    font-size: 3rem;
    color: #2c3e50;
    margin-bottom: 1rem;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
}

.title-slide h2 {
    font-size: 1.5rem;
    color: #3498db;
    margin-bottom: 2rem;
    border: none;
}

.title-info {
    background: linear-gradient(135deg, #3498db, #2980b9);
    color: white;
    padding: 1.5rem;
    border-radius: 8px;
    margin-bottom: 2rem;
}

.author-info {
    background: #f8f9fa;
    padding: 1.5rem;
    border-radius: 8px;
    color: #2c3e50;
}

.animated-cell-intro {
    width: 200px;
    height: 120px;
    position: relative;
    margin-top: 2rem;
}

.cell-membrane-anim {
    width: 100%;
    height: 80px;
    border: 3px solid #e74c3c;
    border-radius: 40px;
    position: relative;
    animation: cellPulse 3s ease-in-out infinite;
}

.ion-particles {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
}

/* Learning Objectives */
.objectives-list {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.objective-item {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    padding: 1.5rem;
    background: #f8f9fa;
    border-radius: 8px;
    border-left: 4px solid #3498db;
    transition: all 0.3s ease;
    opacity: 0;
    transform: translateY(20px);
}

.objective-item.animate {
    opacity: 1;
    transform: translateY(0);
}

.objective-item:hover {
    background: #e9ecef;
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.objective-icon {
    font-size: 2rem;
    min-width: 50px;
}

.objective-text h3 {
    color: #2c3e50;
    margin: 0 0 0.5rem 0;
    font-size: 1.1rem;
}

.objective-text p {
    color: #7f8c8d;
    margin: 0;
    line-height: 1.5;
}

/* Content Grid Layout */
.content-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 3rem;
    margin-top: 2rem;
}

.content-left, .content-right {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

/* Diagrams and Visualizations */
.membrane-diagram, .channel-diagram {
    background: #f8f9fa;
    padding: 2rem;
    border-radius: 8px;
    text-align: center;
}

.diagram-label, .channel-label {
    font-size: 12px;
    fill: #2c3e50;
    font-weight: bold;
}

.potential-label {
    font-size: 11px;
    fill: #e74c3c;
    font-weight: bold;
}

.membrane-label {
    font-size: 12px;
    fill: #34495e;
}

.sensor-label, .gate-label {
    font-size: 10px;
    fill: #2c3e50;
}

/* Key Concepts */
.key-concepts {
    background: white;
    padding: 2rem;
    border-radius: 8px;
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.concept-item {
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #ecf0f1;
}

.concept-item:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.concept-item h4 {
    color: #3498db;
    margin: 0 0 0.5rem 0;
}

.concept-item p {
    color: #2c3e50;
    margin: 0;
    line-height: 1.6;
}

/* Tables */
.concentration-table {
    width: 100%;
    border-collapse: collapse;
    background: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.concentration-table th {
    background: #3498db;
    color: white;
    padding: 1rem;
    text-align: left;
    font-weight: bold;
}

.concentration-table td {
    padding: 1rem;
    border-bottom: 1px solid #ecf0f1;
}

.concentration-table tr:hover {
    background: #f8f9fa;
}

.sodium-row { border-left: 4px solid #e74c3c; }
.potassium-row { border-left: 4px solid #9b59b6; }
.calcium-row { border-left: 4px solid #f39c12; }
.chloride-row { border-left: 4px solid #27ae60; }

/* Equations */
.equation-box {
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    padding: 2rem;
    border-radius: 8px;
    border-left: 4px solid #3498db;
    margin: 1rem 0;
}

.equation-main {
    font-size: 1.2rem;
    font-weight: bold;
    color: #2c3e50;
    text-align: center;
    margin-bottom: 1rem;
    font-family: 'Courier New', monospace;
}

.equation-simplified {
    background: white;
    padding: 1rem;
    border-radius: 6px;
    margin: 1rem 0;
    text-align: center;
}

.equation-variables {
    font-size: 0.9rem;
    color: #7f8c8d;
}

.equation-variables p {
    margin: 0.25rem 0;
}

/* Interactive Elements */
.interactive-controls, .interactive-demo {
    background: #e8f5e8;
    padding: 2rem;
    border-radius: 8px;
    margin-top: 2rem;
    text-align: center;
}

.demo-btn {
    background: #27ae60;
    color: white;
    border: none;
    padding: 0.8rem 1.5rem;
    border-radius: 6px;
    cursor: pointer;
    margin: 0.5rem;
    transition: all 0.3s ease;
    font-weight: bold;
}

.demo-btn:hover {
    background: #229954;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(39, 174, 96, 0.3);
}

.interactive-calculator {
    background: white;
    padding: 2rem;
    border-radius: 8px;
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    margin-top: 2rem;
}

.calculator-controls {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    align-items: center;
    justify-content: center;
}

.calculator-controls label {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
    font-weight: bold;
    color: #2c3e50;
}

.calculator-controls input, .calculator-controls select {
    padding: 0.5rem;
    border: 2px solid #bdc3c7;
    border-radius: 4px;
    font-size: 1rem;
}

.calculator-controls button {
    background: #3498db;
    color: white;
    border: none;
    padding: 0.8rem 1.5rem;
    border-radius: 6px;
    cursor: pointer;
    font-weight: bold;
    transition: all 0.3s ease;
}

.calculator-controls button:hover {
    background: #2980b9;
    transform: translateY(-2px);
}

.result {
    background: #d5f4e6;
    color: #27ae60;
    padding: 1rem;
    border-radius: 6px;
    margin-top: 1rem;
    font-weight: bold;
    font-size: 1.1rem;
}

/* Permeability Bars */
.permeability-factors {
    margin-top: 1rem;
}

.perm-bar {
    display: flex;
    align-items: center;
    margin: 0.5rem 0;
    gap: 1rem;
}

.perm-bar span {
    min-width: 40px;
    font-weight: bold;
}

.bar {
    height: 20px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 0.8rem;
    font-weight: bold;
    transition: width 0.5s ease;
}

.k-bar { background: #9b59b6; }
.na-bar { background: #e74c3c; }
.cl-bar { background: #27ae60; }

/* Channel States */
.demo-controls {
    background: white;
    padding: 2rem;
    border-radius: 8px;
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.demo-controls label {
    display: block;
    margin-bottom: 1rem;
    font-weight: bold;
    color: #2c3e50;
}

.demo-controls input[type="range"] {
    width: 100%;
    margin: 1rem 0;
}

.channel-states {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-top: 1rem;
}

.state-indicator {
    background: #f8f9fa;
    padding: 1rem;
    border-radius: 6px;
    text-align: center;
    border-left: 4px solid #bdc3c7;
    transition: all 0.3s ease;
}

.state-indicator.open {
    background: #d5f4e6;
    border-left-color: #27ae60;
}

.state-indicator.closed {
    background: #fadbd8;
    border-left-color: #e74c3c;
}

/* Summary Grid */
.summary-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.summary-section {
    background: #f8f9fa;
    padding: 2rem;
    border-radius: 8px;
    border-top: 4px solid #3498db;
}

.summary-section h3 {
    color: #2c3e50;
    margin: 0 0 1rem 0;
}

.summary-section ul {
    list-style-type: none;
    padding-left: 0;
    margin: 0;
}

.summary-section li {
    padding: 0.5rem 0;
    padding-left: 1.5rem;
    position: relative;
    border-bottom: 1px solid #ecf0f1;
}

.summary-section li:before {
    content: "✓";
    position: absolute;
    left: 0;
    color: #27ae60;
    font-weight: bold;
}

.summary-section li:last-child {
    border-bottom: none;
}

/* Next Lecture */
.next-lecture {
    background: linear-gradient(135deg, #3498db, #2980b9);
    color: white;
    padding: 2rem;
    border-radius: 8px;
    text-align: center;
    margin-top: 2rem;
}

.next-lecture h3 {
    color: white;
    margin: 0 0 1rem 0;
}

.next-btn {
    background: white;
    color: #3498db;
    padding: 1rem 2rem;
    border-radius: 6px;
    text-decoration: none;
    font-weight: bold;
    display: inline-block;
    margin-top: 1rem;
    transition: all 0.3s ease;
}

.next-btn:hover {
    background: #ecf0f1;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
}

/* Footer */
.lecture-footer {
    background: #2c3e50;
    color: white;
    padding: 2rem 0;
    margin-top: 3rem;
}

.lecture-footer .footer-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;
}

.lecture-footer .author-info p {
    margin: 0.25rem 0;
}

.lecture-footer .copyright {
    text-align: right;
}

/* Animations */
@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateX(50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes cellPulse {
    0%, 100% {
        transform: scale(1);
        border-color: #e74c3c;
    }
    50% {
        transform: scale(1.05);
        border-color: #3498db;
    }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .lecture-navigation {
        flex-direction: column;
        text-align: center;
    }

    .lecture-content {
        margin-top: 150px;
    }

    .slide {
        padding: 2rem 1rem;
        margin: 0 1rem 2rem 1rem;
    }

    .content-grid {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .title-slide h1 {
        font-size: 2rem;
    }

    .objectives-list {
        grid-template-columns: 1fr;
    }

    .summary-grid {
        grid-template-columns: 1fr;
    }

    .calculator-controls {
        flex-direction: column;
    }

    .lecture-footer .footer-content {
        flex-direction: column;
        text-align: center;
    }

    .lecture-footer .copyright {
        text-align: center;
    }
}
